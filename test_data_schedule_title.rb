#!/usr/bin/env ruby

# Test script to verify Data Schedule title functionality
# This script tests the backend API changes for the title feature

require 'net/http'
require 'json'
require 'uri'

# Configuration - update these values for your environment
BASE_URL = 'http://localhost:3000'
API_TOKEN = 'your_api_token_here' # Replace with actual API token

def make_request(method, path, body = nil)
  uri = URI("#{BASE_URL}#{path}")
  http = Net::HTTP.new(uri.host, uri.port)

  case method.upcase
  when 'GET'
    request = Net::HTTP::Get.new(uri)
  when 'POST'
    request = Net::HTTP::Post.new(uri)
  when 'PUT'
    request = Net::HTTP::Put.new(uri)
  when 'DELETE'
    request = Net::HTTP::Delete.new(uri)
  end

  request['Content-Type'] = 'application/json'
  request['Authorization'] = "Bearer #{API_TOKEN}" if API_TOKEN != 'your_api_token_here'
  request.body = body.to_json if body

  response = http.request(request)

  puts "#{method} #{path}"
  puts "Status: #{response.code} #{response.message}"
  puts "Response: #{response.body}"
  puts '---'

  response
end

def test_data_schedule_title_functionality
  puts 'Testing Data Schedule Title Functionality'
  puts '=' * 50

  # Test 1: Create a new data schedule with title
  puts 'Test 1: Creating data schedule with title'
  create_payload = {
    data_schedule: {
      title: 'Test Schedule Title',
      source_id: 1, # Replace with actual dashboard/report ID
      source_type: 'Dashboard',
      dest: {
        type: 'EmailDest',
        destinations: [
          {
            label: '<EMAIL>',
            value: '<EMAIL>',
          },
        ],
      },
      schedule: {
        repeat: 'daily',
        time: '09:00',
        timezone: 'UTC',
      },
    },
  }

  response = make_request('POST', '/api/v2/data_schedules', create_payload)

  if response.code == '200' || response.code == '201'
    result = JSON.parse(response.body)
    schedule_id = result.dig('data_schedule', 'id')
    title = result.dig('data_schedule', 'title')

    puts "✅ Created schedule with ID: #{schedule_id}"
    puts "✅ Title in response: #{title}"

    if title == 'Test Schedule Title'
      puts '✅ Title correctly saved and returned'
    else
      puts "❌ Title not correctly saved. Expected: 'Test Schedule Title', Got: '#{title}'"
    end

    # Test 2: Update the schedule title
    puts "\nTest 2: Updating schedule title"
    update_payload = {
      data_schedule: {
        title: 'Updated Schedule Title',
      },
    }

    response = make_request('PUT', "/api/v2/data_schedules/#{schedule_id}", update_payload)

    if response.code == '200'
      result = JSON.parse(response.body)
      updated_title = result.dig('data_schedule', 'title')

      if updated_title == 'Updated Schedule Title'
        puts '✅ Title correctly updated'
      else
        puts "❌ Title not correctly updated. Expected: 'Updated Schedule Title', Got: '#{updated_title}'"
      end
    else
      puts '❌ Failed to update schedule'
    end

    # Test 3: Fetch the schedule and verify title is included
    puts "\nTest 3: Fetching schedule to verify title"
    response = make_request('GET', "/api/v2/data_schedules/#{schedule_id}")

    if response.code == '200'
      result = JSON.parse(response.body)
      fetched_title = result.dig('data_schedule', 'title')

      if fetched_title == 'Updated Schedule Title'
        puts '✅ Title correctly fetched'
      else
        puts "❌ Title not correctly fetched. Expected: 'Updated Schedule Title', Got: '#{fetched_title}'"
      end
    else
      puts '❌ Failed to fetch schedule'
    end

    # Clean up - delete the test schedule
    puts "\nCleaning up: Deleting test schedule"
    response = make_request('DELETE', "/api/v2/data_schedules/#{schedule_id}")

    if response.code == '200' || response.code == '204'
      puts '✅ Test schedule deleted'
    else
      puts '❌ Failed to delete test schedule'
    end

  else
    puts '❌ Failed to create schedule'
    puts "Response: #{response.body}"
  end

  # Test 4: Create schedule without title (backward compatibility)
  puts "\nTest 4: Creating schedule without title (backward compatibility)"
  create_payload_no_title = {
    data_schedule: {
      source_id: 1, # Replace with actual dashboard/report ID
      source_type: 'Dashboard',
      dest: {
        type: 'EmailDest',
        destinations: [
          {
            label: '<EMAIL>',
            value: '<EMAIL>',
          },
        ],
      },
      schedule: {
        repeat: 'daily',
        time: '09:00',
        timezone: 'UTC',
      },
    },
  }

  response = make_request('POST', '/api/v2/data_schedules', create_payload_no_title)

  if response.code == '200' || response.code == '201'
    result = JSON.parse(response.body)
    schedule_id = result.dig('data_schedule', 'id')
    title = result.dig('data_schedule', 'title')

    puts '✅ Created schedule without title'
    puts "✅ Title in response: #{title.nil? ? 'null' : title}"

    if title.nil?
      puts '✅ Backward compatibility maintained - null title accepted'
    else
      puts "⚠️  Title is not null: '#{title}'"
    end

    # Clean up
    response = make_request('DELETE', "/api/v2/data_schedules/#{schedule_id}")
    puts '✅ Test schedule deleted' if response.code == '200' || response.code == '204'

  else
    puts '❌ Failed to create schedule without title'
  end
end

# Run the tests
if __FILE__ == $0
  puts 'Data Schedule Title Feature Test'
  puts 'Make sure to update BASE_URL and API_TOKEN before running'
  puts

  if API_TOKEN == 'your_api_token_here'
    puts '⚠️  Please update the API_TOKEN in this script before running'
    exit 1
  end

  test_data_schedule_title_functionality

  puts "\nTest completed!"
end
