# frozen_string_literal: true

# Description: Enables specific ag-grid feature toggles in the database.
# How to run: bundle exec rails runner script/adhoc/enable_ag_grid_features.rb

feature_toggles_to_enable = [
  # AG-Grid FTs
  'ag-grid:data-table',
  'ag-grid:pivot-table',
  'ag-grid:metric-sheet',
  'ag-grid:cohort-retention',

  # Table interaction FTs
  'table:reorder_columns',
  'table:freeze_columns',
  'table:hide_fields',
  'table:auto_size',
  'table:remove_columns',
  'table:rename_columns',
  'table:aggregate_columns',
  'table:add_new_columns',
  'table:add_edit_calculations',
  'viz:action',
  'table:manage_actions',

  # Pivot interaction FTs
  'pivot:freeze_columns',
  'pivot:hide_fields',
  'pivot:auto_size',
  'pivot:remove_columns',
  'pivot:rename_columns',
  'pivot:aggregate_columns',
  'pivot:add_edit_calculations',
  'pivot:transpose',
]

feature_toggles_to_enable.each do |feature_key|
  ft = FeatureToggle.find_or_create_by(key: feature_key)
  ft.toggle_mode = 'enabled'
  ft.save!
end
