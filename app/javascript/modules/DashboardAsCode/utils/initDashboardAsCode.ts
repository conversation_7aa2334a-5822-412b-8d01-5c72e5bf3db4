/* eslint-disable max-len */
import { snakeCase } from 'lodash';
import { type DashboardDefinition, type DashboardRenderOptions, reporting2 } from '@holistics/aml-std';
import { DashboardAsCode } from '../types';

export function generateDashboardUname (title: string) {
  // Clean special characters to prevent invalid uname
  const uname = snakeCase(title.replace(/[^a-zA-Z0-9 ]/g, ''));
  if (/^\d.*/.test(uname)) {
    // if uname starts with a number, prepend with an underscore (_)
    return `_${uname}`;
  }
  return uname;
}

const additionalSampleDashboardCodeBlocks = `block title: TextBlock {
    content: @md # Your title goes here ;;
  }

  view: CanvasLayout {
    label: 'View 1'
    height: 840
    grid_size: 20
    block title {
      position: pos(20, 20, 1160, 60)
    }
    mobile {
      mode: 'auto'
    }
  }

  theme: H.themes.vanilla
`;

export function initAMLDashboard (definition: DashboardDefinition, renderOptions?: DashboardRenderOptions): DashboardAsCode {
  return {
    id: 0,
    uname: definition.uname,
    version: 4 as const,
    path: '',
    definition_aml: '',
    from_aml: true,
    definition,
    widgets: [],
    permissions: {
      can_update: false,
      can_live_update: false,
      can_read: true,
      can_crud: false,
      can_share: false,
      can_export: false,
      can_export_data: false,
      can_pin: false,
    },
    render_options: renderOptions,
    dynamic_filters: [],
  };
}

export function initDashboardAsCode ({ title, description, uname }: {
  title: string, description?: string, uname?: string
}) {
  const definition: DashboardDefinition = {
    uname: uname || generateDashboardUname(title),
    title,
    description,
    blocks: [{
      type: 'TextBlock', uname: 'title', content: '# Your title goes here ', settings: { hide_controls: false },
    }],
    interactions: [],
    views: [{
      type: 'CanvasLayout',
      uname: 'view_1',
      label: 'View 1',
      width: 1200,
      height: 840,
      blocks: {
        title: {
          layer: 0,
          position: {
            x: 20, y: 20, w: 1160, h: 60,
          },
        },
      },
      grid_size: 20,
      mobile: { mode: 'auto' },
    }],
    settings: { autorun: true, allow_timezone_change: false },
    theme: {
      _id: 'H.themes.vanilla',
      title: 'Vanilla',
      background: { bg_color: '#FAFAFA' },
      canvas: {
        border: {
          border_width: {
            top: '1px', left: '1px', bottom: '1px', right: '1px',
          },
          border_radius: {
            top_left: '8px', top_right: '8px', bottom_left: '8px', bottom_right: '8px',
          },
          border_color: '#E8E8E8',
          border_style: 'solid',
        },
        background: { bg_color: '#FDFDFD' },
        shadow: 'none',
        opacity: 1,
      },
      block: {
        label: {
          font_family: 'Inter', font_size: '14px', font_color: '#53586A', font_weight: 'medium', font_style: 'normal',
        },
        text: {
          font_family: 'Inter', font_size: '12px', font_color: '#53586A', font_weight: 'normal', font_style: 'normal',
        },
        border: {
          border_width: {
            top: '1px', left: '1px', bottom: '1px', right: '1px',
          },
          border_radius: {
            top_left: '6px', top_right: '6px', bottom_left: '6px', bottom_right: '6px',
          },
          border_color: '#E8E8E8',
          border_style: 'solid',
        },
        background: { bg_color: '#FFFFFF' },
        padding: {
          top: '16px', left: '16px', bottom: '16px', right: '16px',
        },
        shadow: 'none',
        opacity: 1,
      },
      custom_css: { original: '.dac-block:has(.dac-ic-block),\n.dac-block:has(.dac-text-block) {\n  background-color: transparent !important;\n  border-color: transparent !important;\n  border-width: 0px !important;\n  padding: 0px !important;\n}\n.dac-block:has(.dac-ic-block) .dm-filter-container,\n.dac-block:has(.dac-ic-block) .hui-select-trigger {\n  margin-right: 0px !important;\n}', final: '.h-dac-theme .dac-block:has(.dac-ic-block),.h-dac-theme .dac-block:has(.dac-text-block){background-color:transparent!important;border-color:transparent!important;border-width:0px!important;padding:0px!important}.h-dac-theme .dac-block:has(.dac-ic-block) .dm-filter-container,.h-dac-theme .dac-block:has(.dac-ic-block) .hui-select-trigger{margin-right:0px!important}' },
    },
  };

  const definitionAML = `Dashboard ${definition.uname} {
  title: ${reporting2.createAmlString(title)}
  description: ${reporting2.createAmlString(description ?? '')}
  ${additionalSampleDashboardCodeBlocks}
}`;

  return {
    version: 4,
    definition,
    definition_aml: definitionAML,
  };
}
