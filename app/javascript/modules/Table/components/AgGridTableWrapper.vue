<template>
  <div
    v-h-loading.body.disableRelative="loading"
    class="data-table-wrapper"
    :class="{ 'is-pptr': IS_PUPPETEER }"
  >
    <empty-table
      v-if="empty"
      :fields="data.fields"
    />
    <ag-grid-vue
      v-else
      ref="tableGrid"
      class="h-table w-full"
      data-ci="ci-ag-grid-data-table"
      suppress-scroll-on-new-data
      tooltip-interaction
      suppress-drag-leave-hides-columns
      :animate-rows="false"
      :style="{ height: `${gridHeight}px` }"
      :column-defs="columnDefs"
      :default-col-def="defaultColDef"
      :row-data="rowData"
      :context="context"
      :pinned-bottom-row-data="isPinnedBottomRowEnabled ? pinnedRowData : undefined"
      :pinned-top-row-data="!isPinnedBottomRowEnabled ? pinnedRowData : undefined"
      :suppress-column-virtualisation="suppressColumnVirtualisation"
      :suppress-max-rendered-row-restriction="IS_PUPPETEER"
      :suppress-movable-columns="!canInteract"
      :process-unpinned-columns="onUnpinnedColumns"
      :theme="dataTableTheme"
      :drag-and-drop-image-component="DragAndDropImageComponent"
      @first-data-rendered="onFirstDataRendered"
      @cell-clicked="onCellClicked"
      @cell-context-menu="onCellContextMenu"
      @grid-columns-changed="onGridColumnsChanged"
      @grid-ready="onGridReady"
      @grid-size-changed="onGridSizeChanged"
      @cell-key-down="handleCellKeyDown"
      @row-data-updated="onRowDataUpdated"
      @body-scroll="onBodyScroll"
      @grid-pre-destroyed="onGridPreDestroyed"
      @column-resized="onColumnResized"
      @column-pinned="onFreezeColumn"
      @column-visible="onColumnVisible"
      @state-updated="onStateUpdated"
    />
  </div>
  <TableFooter
    :data="data"
    :is-infinite-scroll-enabled="!!isInfiniteScrollEnabled"
    :is-widget="isWidget"
    :pagination="pagination"
    @paginate="emitPagination"
  />
</template>

<script setup lang="ts">
import { AgGridVue } from 'ag-grid-vue3';
import {
  ComponentPublicInstance,
  computed,
  inject,
  shallowRef,
  useTemplateRef,
  watch,
} from 'vue';
import { get } from 'lodash';
import { useDebounceFn } from '@vueuse/core';
import { SortOption, VizSettingSort } from '@/modules/Viz/services/types';
import type { Action, DataSet } from '@holistics/types';
import { ExtractedValues } from '@/modules/Viz/utils/valuesExtractors/types';
import { handleAjaxError } from '@/core/services/ajax';
import { isCopyCommandPressed } from '@/core/utils/copy';
import { DATA_TABLE } from '@/modules/Viz/constants/vizTypes';
import {
  FirstDataRenderedEvent, GridReadyEvent, GridApi,
  GridColumnsChangedEvent, GridSizeChangedEvent, CellKeyDownEvent,
  BodyScrollEvent, SuppressHeaderKeyboardEventParams,
  ColumnResizedEvent, RowDataUpdatedEvent,
  ColumnVisibleEvent,
  GridPreDestroyedEvent,
  ColumnPinnedEvent,
  StateUpdatedEvent,
} from 'ag-grid-community';
import DrilldownHelper from '@/drilldowns/drilldown_helper';
import { ActionHelper } from '@/modules/Action/utils/ActionHelper';
import type { createEventBus } from '@/core/services/eventBus';
import { useToasts } from '@/core/composables/useToasts';
import type { VizSettingInjection } from '@/modules/Table/types/contextData';
import { AqlPopupContainerKey } from '@aml-studio/h/constants';
import { Field } from '@holistics/aml-std';
import { amlProjectIdKey } from '@aml-studio/client/context';
import { CALCULATE_TABLE_HEIGHT_DELAY_MS, EMIT_RENDERED_EVENT_DELAY_MS } from '@/modules/Table/constants/tableRenderingTiming';
import { INTERACTION_PRESETS, InteractionConfig } from '@/modules/Viz/types/interactionPreset';
import DragAndDropImageComponent from '@/modules/Table/components/headerComponents/DragAndDropImageComponent.vue';
import AgGridSingleRowPopover from './dataTablePopover/AgGridSingleRowPopover.vue';
import { buildDataTableColumnDefs } from '../utils/renderers/table/buildDataTableColumnDefs';
import { useInfiniteScroll } from '../composables/infiniteScroll';
import { IS_PUPPETEER } from '../constants/puppeteer';
import { useDataTableAggregateRow } from '../composables/aggregateRow';
import { RangeSelectionService } from '../services/RangeSelection';
import { useDataTableCrossFiltering } from '../composables/crossFiltering';
import { useFitHeightGrid } from '../composables/fitHeightGrid';
import { useTableSort } from '../composables/tableSort';
import { useDataTableContextMenu } from '../composables/contextMenu';
import EmptyTable from './otherTables/EmptyTable.vue';
import {
  DataTablePropData, DataTableCellClickedEventParams, Pagination, ManualWidths,
  ColumnModifier,
  ColumnAddition,
  ColumnCalculation,
} from '../types/propData';
import { DataTableRow } from '../types/dataTable';
import InteractableCellRenderer from './cellComponents/InteractableCellRenderer.vue';
import { resizeTableColumns, shouldSupressColVirtualisation } from '../utils/renderers/resize/resizeColumns';
import { copyCellRangeDataToClipboard } from '../utils/helpers/copy/copyToClipBoard';
import { ResizeTableHelper } from '../services/resizeTableHelper';
import { ColumnReorder, useColumnReorder } from '../composables/columnReorder';
import { DataTableContext, useDataTableContext } from '../composables/dataTableContext';
import { onUnpinnedColumns } from '../utils/helpers/unpinnedColumns';
import { getCellContextMenuHandler } from '../utils/helpers/cellContextMenu';
import { createVizFormatter } from '../utils/helpers/copy/copyFormatter';
import { arePopColumns } from '../utils/renderers/table/columnInteraction';
import { ContextMenuDisplayOptions } from '../services/ContextMenuDisplayOptions';
import { trackScrollbarHeight } from '../utils/renderers/trackScrollbarHeight';
import TableFooter from './TableFooter.vue';
import { useManageMovingEventColumns } from '../composables/useManageMovingEventColumns';
import { getDefaultTableStyle } from '../constants/defaultTableStyle';
import { useStyleNewColumns } from '../composables/useStyleNewColumns';
import { useTableClickOutside } from '../composables/useTableClickOutside';
import { usePopBanner } from '../composables/usePopBanner';
import { useFreezeOutOfViewport } from '../composables/useFreezeOutOfViewport';

const props = withDefaults(defineProps<{
  data: DataTablePropData,
  tableId?: string;
  pagination?: Pagination,
  infiniteScroll?: boolean,
  isWidget?: boolean,
  sortOptions: SortOption[],
  highlightedRow?: number,
  dataSet?: DataSet,
  height: number,
  loading?: boolean,
  isSingleLineRow?: boolean,
  isLoadingMore?: boolean,
  isStretch?: boolean,
  interactOnExploreMode?: boolean,
  interactOnViewMode?: boolean,
  applyColumnWidth?: boolean,
  canInteract?: boolean,
  isProduction?: boolean,
  showFieldDetails: boolean
  interactionConfig?: InteractionConfig,
}>(), {
  pagination: undefined,
  infiniteScroll: false,
  isWidget: false,
  highlightedRow: -1,
  isSingleLineRow: true,
  isLoadingMore: false,
  tableId: '',
  dataSet: undefined,
  loading: false,
  isStretch: false,
  interactOnExploreMode: false,
  interactOnViewMode: false,
  applyColumnWidth: false,
  canInteract: true,
  isProduction: true,
  interactionConfig: () => (INTERACTION_PRESETS.paginateSortOnly),
});

const emit = defineEmits<{
  paginate: [value: { page: number, pageSize: number }]
  sort: [vizSettingSort: VizSettingSort]
  fieldsValues: [values: ExtractedValues]
  render: [value: { isFullyVisible: boolean }]
  fetchMore: []
  columnResized: [value: ManualWidths]
  columnReorder: [value: ColumnReorder]
  columnFreeze: [totalColumns: number]
  columnHide: [columnId: string | null, isHidden: boolean]
  columnRename: [columnId: string, customLabel: string | null]
  columnRemove: [columnId: string]
  columnModifier: [value: ColumnModifier]
  columnAdd: [value: ColumnAddition]
  columnUpdateCalculation: [value: ColumnCalculation]
  columnUpdateAqlAdhocField: [value: { newField: Field, oldField: Field }]
  columnManageActions: [columnId: string, value: Action[]]
  freezeOutOfViewport: [isFreezeOutOfViewport: boolean]
}>();
const tableGrid = useTemplateRef<ComponentPublicInstance>('tableGrid');
const rangeSelectionService = new RangeSelectionService(tableGrid);
const contextMenuDisplayOptions = computed(() => new ContextMenuDisplayOptions({
  vizType: DATA_TABLE,
  totalFrozenColumns: props.data.settings?.misc?.column_freeze ?? 0,
  vizFields: props.data.vizFields,
  dataSet: props.dataSet as any,
  interactionConfig: props.interactionConfig,
}));
const { styleNewColumns } = useStyleNewColumns(rangeSelectionService);
useTableClickOutside(tableGrid, rangeSelectionService);
usePopBanner(() => props.data.detailedFields);
const { isFreezeOutOfViewport } = useFreezeOutOfViewport();

const { context } = useDataTableContext({
  onFreezeColumn: (totalFrozenColumns: number) => {
    emit('columnFreeze', totalFrozenColumns);
  },
  onHideColumn: (columnId: string | null, isHidden: boolean) => {
    emit('columnHide', columnId, isHidden);
  },
  onRenameColumn: (columnId: string, customLabel: string | null) => {
    emit('columnRename', columnId, customLabel);
  },
  onRemoveColumn: (columnId: string) => {
    emit('columnRemove', columnId);
  },
  onChangeColumnModifier: (value: ColumnModifier) => {
    emit('columnModifier', value);
  },
  onAddColumn: (value: ColumnAddition) => {
    emit('columnAdd', value);
  },
  onUpdateCalculationColumn: (value: ColumnCalculation) => {
    emit('columnUpdateCalculation', value);
  },
  onUpdateAqlAdhocField: (value: { newField: Field, oldField: Field }) => {
    emit('columnUpdateAqlAdhocField', value);
  },
  onManageActionsField: (columnId: string, value: Action[]) => {
    emit('columnManageActions', columnId, value);
  },
  // canInteract affects all interactions, including sorting.
  canInteract: () => props.canInteract,
  vizSettingInjection: inject<VizSettingInjection>('vizSettingInjection'),
  aqlPopupContainer: inject(AqlPopupContainerKey, document.body),
  amlProjectIdKey: inject<number>(amlProjectIdKey, -1),
  rangeSelectionService: () => rangeSelectionService,
  contextMenuDisplayOptions: () => contextMenuDisplayOptions.value,
});
const gridApi = shallowRef<GridApi<DataTableRow>>();

const { pinnedRowData, isPinnedBottomRowEnabled } = useDataTableAggregateRow(props);
const { handleCrossFiltering } = useDataTableCrossFiltering(props);

const rowData = computed<DataTableRow[]>(() => props.data.values);

const dataTableTheme = getDefaultTableStyle(DATA_TABLE);

const emitSort = (vizSettingSort: VizSettingSort) => emit('sort', vizSettingSort);
const emitFieldsValues = (values: ExtractedValues) => emit('fieldsValues', values);
const emitPagination = (value: { page: number, pageSize: number }) => emit('paginate', value);
const emitFetchMore = () => emit('fetchMore');
const emitReorderColumn = (val: ColumnReorder) => emit('columnReorder', val);

const {
  onFieldsChange, handleColumnDragend, handleReorderByFreezing,
} = useColumnReorder({
  emitValue: emitReorderColumn,
  sorts: () => props.data.settings?.sort,
  rangeSelectionService,
});
const { handleCellContextMenu } = useDataTableContextMenu(props, emitFieldsValues);

const {
  onSort,
} = useTableSort({
  dataSet: props.dataSet, emitSort, tableType: DATA_TABLE, sortOptions: () => props.sortOptions,
});
const drilldownHelper = computed(() => new DrilldownHelper({
  // @ts-expect-error
  data: props.data.values,
  // @ts-expect-error
  drilldowns: props.data.drilldowns,
  filters: props.data.filters,
}));
const actionHelper = computed(() => new ActionHelper({
  actions: props.data.actions,
  detailedFields: props.data.detailedFields || [],
}));
const resizeTableHelper = new ResizeTableHelper({
  shouldApplyColumnWidth: props.applyColumnWidth,
  shouldFullWidth: props.isWidget || props.isStretch,
});

const columnDefs = computed(() => buildDataTableColumnDefs({
  propsData: props.data,
  highlightedRow: props.highlightedRow,
  isInfiniteScrollEnabled: props.infiniteScroll,
  isSingleLineRowEnabled: props.isSingleLineRow,
  rangeSelectionService,
  contextMenuDisplayOptions: contextMenuDisplayOptions.value,
  columnHeaderSortProps: {
    sortOptions: props.sortOptions,
    onSort,
  },
  drilldownHelper,
  actionHelper,
  resizeTableHelper,
  dataSet: props.dataSet,
  showFieldDetails: props.showFieldDetails,
}));

const defaultColDef = {
  tooltipComponent: props.isSingleLineRow ? AgGridSingleRowPopover : undefined,
  tooltipComponentParams: {
    grid: tableGrid,
  },
  headerComponent: InteractableCellRenderer,
  suppressHeaderKeyboardEvent: (params: SuppressHeaderKeyboardEventParams) => {
    // handle copy command in custom header
    if (isCopyCommandPressed(params.event)) return true;

    return false;
  },
  sortable: false,
};

const suppressColumnVirtualisation = computed(() => shouldSupressColVirtualisation(get(props.data, 'fields', []).length));
const { gridHeight, calculateTableHeight } = useFitHeightGrid(props, gridApi, tableGrid, emitFetchMore);

function copyRawValues () {
  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange());
}
function copyFormattedValues () {
  const vizFormatterFunc = createVizFormatter({ formats: props.data.formats, isConvertedNullToZeroEnabled: false });
  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange(vizFormatterFunc));
}

const eventBus = inject<ReturnType<typeof createEventBus>>('eventBus');
const { registerEvents, removeEvents } = useManageMovingEventColumns({
  api: gridApi,
  detailedFields: props.data.detailedFields ?? [],
  callback: handleColumnDragend,
});
const onGridReady = (params: GridReadyEvent<DataTableRow>) => {
  gridApi.value = params.api;
  if (tableGrid.value) {
    // set the single line popover outside the grid
    const closestModal = tableGrid.value.$el.closest('.h-modal-backdrop');
    params.api.setGridOption('popupParent', closestModal ?? document.body);
  }

  // drag event listener
  rangeSelectionService.registerRangeSelectionDragEvent(params.api);
  eventBus?.$on('contextMenu:copyRawValue', copyRawValues);
  eventBus?.$on('contextMenu:copyFormattedValue', copyFormattedValues);
};

const onGridPreDestroyed = (e: GridPreDestroyedEvent<DataTableRow>) => {
  rangeSelectionService.removeCellRange();
  rangeSelectionService.removeRangeSelectionDragEvent();
  eventBus?.$off('contextMenu:copyRawValue', copyRawValues);
  eventBus?.$off('contextMenu:copyFormattedValue', copyFormattedValues);

  removeEvents(e.api);
};

const handleCellKeyDown = (e: CellKeyDownEvent<DataTableRow>) => {
  try {
    if (!isCopyCommandPressed(e.event as KeyboardEvent)) return;

    copyFormattedValues();
  } catch (error) {
    handleAjaxError(error);
  }
};

const { isInfiniteScrollEnabled, handleInfiniteScroll } = useInfiniteScroll(props, emitFetchMore, gridApi);

const empty = computed<boolean>(() => {
  return get(props.data, 'values', []).length === 0;
});

const debouncedResizeTableColumns = useDebounceFn((api: GridApi) => {
  if (tableGrid.value) {
    // case column width in viz block, we already defined it in columnDefs
    if (props.applyColumnWidth) {
      return;
    }
    resizeTableColumns(api, {
      sizeToFit: props.isWidget || props.isStretch,
      containerWidth: tableGrid.value.$el.clientWidth,
    });
  }
}, 50);

const debouncedShowFreezeOutOfViewport = useDebounceFn((api: GridApi, containerWidth: number = tableGrid.value?.$el.clientWidth ?? 0) => {
  emit('freezeOutOfViewport', isFreezeOutOfViewport(api, containerWidth));
}, 300);

const onRowDataUpdated = (params: RowDataUpdatedEvent<DataTableRow>) => {
  onFieldsChange(props.data.detailedFields ?? [], params.api);
  calculateTableHeight();

  registerEvents(params.api, props.data.detailedFields ?? []);
};

const onFirstDataRendered = async (params: FirstDataRenderedEvent<DataTableRow>) => {
  const isFullyVisible = params.lastRow - params.firstRow + 1 >= props.data.meta.numRows;
  await debouncedResizeTableColumns(params.api);

  // Allow some time for the table to fully render,
  // enabling calculateTableHeight to accurately determine the height of the horizontal scrollbar
  setTimeout(() => {
    calculateTableHeight();
  }, CALCULATE_TABLE_HEIGHT_DELAY_MS);

  trackScrollbarHeight(tableGrid, params.api, gridHeight.value);

  setTimeout(() => {
    emit('render', { isFullyVisible });
  }, EMIT_RENDERED_EVENT_DELAY_MS);
};

const onGridColumnsChanged = (params: GridColumnsChangedEvent<DataTableRow>) => {
  onFieldsChange(props.data.detailedFields ?? [], params.api);
  if (props.isWidget) {
    debouncedResizeTableColumns(params.api);
  }
  styleNewColumns(params.api, { autoSize: !props.isWidget, highlight: true });
};

const onGridSizeChanged = (params: GridSizeChangedEvent<DataTableRow>) => {
  const isResized = params.clientHeight !== 0 || params.clientWidth !== 0;
  if (isResized && props.isWidget) {
    debouncedResizeTableColumns(params.api);
  }
  if (params.clientWidth > 0) {
    debouncedShowFreezeOutOfViewport(params.api, params.clientWidth);
  }
};

const onCellClicked = (e: DataTableCellClickedEventParams) => {
  if (rangeSelectionService.shouldIgnoreCellClicked(e?.event?.target)) {
    return;
  }

  rangeSelectionService.handleCellClicked(e);

  handleCrossFiltering(e);
};

const onCellContextMenu = getCellContextMenuHandler(rangeSelectionService, handleCellContextMenu);

const onBodyScroll = (e: BodyScrollEvent) => {
  useDebounceFn(() => {
    handleInfiniteScroll(e);
  }, 50)();
};

const debouncedHandleColumnResizedEvent = useDebounceFn((e: ColumnResizedEvent) => {
  const fieldWidths = resizeTableHelper.getFieldWidthFromColumns(e?.columns || []);
  emit('columnResized', fieldWidths);
}, 300);

const onColumnResized = (e: ColumnResizedEvent) => {
  const isAllowResizeColumn = props.applyColumnWidth
                              && ['uiColumnResized', 'autosizeColumns'].includes(e.source)
                              && e.finished === true;

  if (!isAllowResizeColumn) return;
  debouncedHandleColumnResizedEvent(e);
};

function onFreezeColumn (e: ColumnPinnedEvent<DataTableRow, DataTableContext>) {
  if (e.source !== 'uiColumnDragged') {
    return;
  }

  handleReorderByFreezing(e);
}

function onColumnVisible (e: ColumnVisibleEvent<DataTableRow>) {
  if (!e.columns || !props.data.detailedFields) {
    return;
  }

  if (arePopColumns(e.columns.slice(1), props.data.detailedFields)) {
    const description = e.visible
      ? 'Unhiding a measure will also unhide its linked Period Comparison columns.'
      : 'Hiding a measure will also hide its linked Period Comparison columns.';

    const { toast } = useToasts();
    toast.info('Period Comparison columns hidden', {
      description,
    });
  }
}

function onStateUpdated (e: StateUpdatedEvent<DataTableRow>) {
  debouncedShowFreezeOutOfViewport(e.api);
}

watch(() => props.height, () => {
  calculateTableHeight();
});

defineExpose({
  calculateTableHeight,
});
</script>
