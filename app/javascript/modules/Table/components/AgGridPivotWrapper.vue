<template>
  <div
    v-h-loading.body.disableRelative="showLoading"
    class="pivot-table-wrapper flex flex-col"
  >
    <empty-table
      v-if="emptyData"
    />
    <ag-grid-vue
      v-else
      ref="pivotGrid"
      class="h-pivot w-full"
      data-ci="ci-ag-grid-pivot-table"
      suppress-movable-columns
      suppress-row-transform
      tooltip-interaction
      suppress-scroll-on-new-data
      :row-buffer="20"
      :animate-rows="false"
      :style="{ height: `${gridHeight}px` }"
      :column-defs="columnDefs"
      :default-col-def="defaultColDef"
      :row-data="rowData"
      :pinned-bottom-row-data="pinnedRowData"
      :context="context"
      :process-unpinned-columns="onUnpinnedColumns"
      :suppress-max-rendered-row-restriction="IS_PUPPETEER"
      :theme="pivotTheme"
      @cell-clicked="onCellClicked"
      @grid-ready="onGridReady"
      @first-data-rendered="onFirstDataRendered"
      @cell-context-menu="onCellContextMenu"
      @grid-columns-changed="onGridColumnsChanged"
      @grid-size-changed="onGridSizeChanged"
      @row-data-updated="onRowDataUpdated"
      @body-scroll="onBodyScroll"
      @cell-key-down="handleCellKeyDown"
      @grid-pre-destroyed="onGridPreDestroyed"
      @column-resized="onColumnResized"
      @state-updated="onStateUpdated"
    />
  </div>
  <TableFooter
    :data="data"
    :is-infinite-scroll-enabled="!!isInfiniteScrollEnabled"
    :is-widget="isWidget"
    :pagination="pagination"
    @paginate="emitPagination"
  />
</template>

<script setup lang="ts">
import { AgGridVue } from 'ag-grid-vue3';
import {
  ComponentPublicInstance,
  computed, inject, shallowRef, useTemplateRef, watch,
} from 'vue';
import { get } from 'lodash';
import { useDebounceFn } from '@vueuse/core';
import type { DataSet } from '@holistics/types';
import { SortOption, VizSettingSort } from '@/modules/Viz/services/types';
import { PIVOT_TABLE } from '@/modules/Viz/constants/vizTypes';
import { Range } from '@/modules/Viz/services/crossFilterHandlers/pivotTable';
import { ExtractedValues } from '@/modules/Viz/utils/valuesExtractors/types';
import {
  FirstDataRenderedEvent, GridApi, GridReadyEvent,
  BodyScrollEvent,
  CellKeyDownEvent,
  GridColumnsChangedEvent,
  GridSizeChangedEvent,
  SuppressHeaderKeyboardEventParams,
  ColumnResizedEvent,
  StateUpdatedEvent,
} from 'ag-grid-community';
import { isCopyCommandPressed } from '@/core/utils/copy';
import { handleAjaxError } from '@holistics/aml-studio/h/services/ajax';
import type { createEventBus } from '@/core/services/eventBus';
import { aqlIsEnabled } from '@/shared/feature_toggle';
import type { VizSettingInjection } from '@/modules/Table/types/contextData';
import { AqlPopupContainerKey } from '@aml-studio/h/constants';
import { Field } from '@holistics/aml-std';
import { amlProjectIdKey } from '@aml-studio/client/context';
import { CALCULATE_TABLE_HEIGHT_DELAY_MS, EMIT_RENDERED_EVENT_DELAY_MS } from '@/modules/Table/constants/tableRenderingTiming';
import { getDefaultTableStyle } from '@/modules/Table/constants/defaultTableStyle';
import { INTERACTION_PRESETS, InteractionConfig } from '@/modules/Viz/types/interactionPreset';
import EmptyTable from './otherTables/EmptyTable.vue';
import AgGridSingleRowPopover from './dataTablePopover/AgGridSingleRowPopover.vue';
import { useFitHeightGrid } from '../composables/fitHeightGrid';
import { usePivotTableAggregateRow } from '../composables/aggregateRow';
import { useTableSort } from '../composables/tableSort';
import { usePivotTableCrossFiltering } from '../composables/crossFiltering';
import { usePivotTableContextMenu } from '../composables/contextMenu';
import { buildPivotColumnOptions } from '../utils/renderers/pivot/buildPivotColumnOptions';
import { buildPivotRowData } from '../utils/renderers/pivot/buildPivotRowData';
import { useInfiniteScroll } from '../composables/infiniteScroll';
import {
  PivotTablePropData, PivotTableCellClickedEventParams, Pagination, ManualWidths,
  ColumnModifier, ColumnAddition, ColumnCalculation,
} from '../types/propData';
import { PivotTableRow } from '../types/pivotTable';
import { RowSpan } from '../services/RowSpan';
import { resizePivotColumns } from '../utils/renderers/resize/resizeColumns';
import { RangeSelectionService } from '../services/RangeSelection';
import { copyCellRangeDataToClipboard } from '../utils/helpers/copy/copyToClipBoard';
import { createVizFormatter } from '../utils/helpers/copy/copyFormatter';
import { flatPivotFormats } from '../utils/renderers/pivot/flatFormats';
import { ResizePivotHelper } from '../services/resizePivotHelper';
import { getCellContextMenuHandler } from '../utils/helpers/cellContextMenu';
import { onUnpinnedColumns } from '../utils/helpers/unpinnedColumns';
import { usePivotTableContext } from '../composables/pivotTableContext';
import { ContextMenuDisplayOptions } from '../services/ContextMenuDisplayOptions';
import { trackScrollbarHeight } from '../utils/renderers/trackScrollbarHeight';
import { IS_PUPPETEER } from '../constants/puppeteer';
import TableFooter from './TableFooter.vue';
import { useStyleNewColumns } from '../composables/useStyleNewColumns';
import { useTableClickOutside } from '../composables/useTableClickOutside';
import { usePopBanner } from '../composables/usePopBanner';
import { useFreezeOutOfViewport } from '../composables/useFreezeOutOfViewport';

const props = withDefaults(defineProps<{
  data: PivotTablePropData,
  pagination?: Pagination,
  loading?: boolean,
  infiniteScroll?: boolean,
  sortOptions?: SortOption[],
  isWidget?: boolean,
  height: number,
  isSingleLineRow?: boolean,
  crossFilter?: {
    highlightedRanges: Range[];
    agHandleCrossFiltering:(pivotedData: PivotTablePropData, row: number, col: number) => void;
  } | undefined,
  dataSet?: DataSet,
  isLoadingMore?: boolean,
  isStretch?: boolean,
  applyColumnWidth?: boolean,
  canInteract?: boolean,
  sortable?: boolean,
  isProduction?: boolean,
  interactionConfig?: InteractionConfig,
}>(), {
  pagination: undefined,
  infiniteScroll: false,
  sortOptions: () => [],
  isWidget: false,
  isSingleLineRow: true,
  loading: false,
  dataSet: undefined,
  crossFilter: undefined,
  isLoadingMore: false,
  isStretch: false,
  applyColumnWidth: false,
  canInteract: true,
  sortable: true,
  isProduction: true,
  interactionConfig: () => (INTERACTION_PRESETS.paginateSortOnly),
});

const gridApi = shallowRef<GridApi<PivotTableRow>>();

const emit = defineEmits<{
  paginate: [value: { page: number, pageSize: number }]
  render: [value: { isFullyVisible: boolean }]
  sort: [vizSettingSort: VizSettingSort]
  fieldsValues: [values: ExtractedValues]
  fetchMore: []
  columnResized: [value: ManualWidths],
  columnFreeze: [totalColumns: number]
  columnHide: [columnId: string | null, isHidden: boolean]
  columnRename: [columnId: string, customLabel: string | null]
  columnRemove: [columnId: string]
  columnModifier: [value: ColumnModifier]
  columnAdd: [value: ColumnAddition]
  columnUpdateCalculation: [value: ColumnCalculation]
  columnUpdateAqlAdhocField: [value: { newField: Field, oldField: Field }]
  freezeOutOfViewport: [isFreezeOutOfViewport: boolean]
}>();

const pivotGrid = useTemplateRef<ComponentPublicInstance>('pivotGrid');
const rangeSelectionService = new RangeSelectionService(pivotGrid);
const contextMenuDisplayOptions = computed(() => new ContextMenuDisplayOptions({
  vizType: PIVOT_TABLE,
  totalFrozenColumns: props.data.pivotOptions?.columnFreeze ?? Math.max(props.data.rowFields.length, 1),
  vizFields: (props.data.vizFields as any),
  dataSet: props.dataSet as any,
  interactionConfig: props.interactionConfig,
}));
const { styleNewColumns } = useStyleNewColumns(rangeSelectionService);
useTableClickOutside(pivotGrid, rangeSelectionService);
usePopBanner(() => props.data.exploreOpts.measures);
const { isFreezeOutOfViewport } = useFreezeOutOfViewport();

const { context } = usePivotTableContext({
  onFreezeColumn: (totalFrozenColumns: number) => {
    emit('columnFreeze', totalFrozenColumns);
  },
  // canInteract affects all interactions, including sorting.
  canInteract: () => props.canInteract,
  onHideColumn: (columnId: string | null, isHidden: boolean) => {
    emit('columnHide', columnId, isHidden);
  },
  onRenameColumn: (columnId: string, customLabel: string | null) => {
    emit('columnRename', columnId, customLabel);
  },
  onRemoveColumn: (columnId: string) => {
    emit('columnRemove', columnId);
  },
  onChangeColumnModifier: (value: ColumnModifier) => {
    emit('columnModifier', value);
  },
  onAddColumn: (value: ColumnAddition) => {
    emit('columnAdd', value);
  },
  onUpdateCalculationColumn: (value: ColumnCalculation) => {
    emit('columnUpdateCalculation', value);
  },
  onUpdateAqlAdhocField: (value: { newField: Field, oldField: Field }) => {
    emit('columnUpdateAqlAdhocField', value);
  },
  onManageActionsField: undefined,
  vizSettingInjection: inject<VizSettingInjection>('vizSettingInjection'),
  aqlPopupContainer: inject(AqlPopupContainerKey, document.body),
  amlProjectIdKey: inject<number>(amlProjectIdKey, -1),
  rangeSelectionService: () => rangeSelectionService,
  contextMenuDisplayOptions: () => contextMenuDisplayOptions.value,
});
const emitSort = (vizSettingSort: VizSettingSort) => emit('sort', vizSettingSort);
const emitFetchMore = () => emit('fetchMore');
const {
  onSort,
} = useTableSort({
  dataSet: props.dataSet, emitSort, tableType: PIVOT_TABLE, sortOptions: () => props.sortOptions,
});
const emitPagination = (value: { page: number, pageSize: number }) => emit('paginate', value);
const emitFieldsValues = (values: ExtractedValues) => emit('fieldsValues', values);
const { emitPivotTableFieldsValues: handleDrillThroughByHeaderClicked, handleCellContextMenu } = usePivotTableContextMenu(props, emitFieldsValues);

const rowSpanService = computed(() => new RowSpan(props.data.rowValues));

const handleCrossFilterByHeaderClicked = props.crossFilter
  ? (row: number, col: number) => props.crossFilter?.agHandleCrossFiltering(props.data, row, col)
  : undefined;

function copyRawValues () {
  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange());
}
function copyFormattedValues () {
  const flattedFormats = flatPivotFormats({ formats: props.data.formats, totalMeasureGroups: props.data.columnValues.length });
  const vizFormatterFunc = createVizFormatter({ formats: flattedFormats, isConvertedNullToZeroEnabled: get(props.data, 'pivotOptions.convertNullToZero', false) });

  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange(
    vizFormatterFunc,
    props.data.transposeManager,
  ));
}

const eventBus = inject<ReturnType<typeof createEventBus>>('eventBus');
const onGridReady = (params: GridReadyEvent<PivotTableRow>) => {
  gridApi.value = params.api;
  if (pivotGrid.value) {
    // set the single line popover outside the grid
    const closestModal = pivotGrid.value.$el.closest('.h-modal-backdrop');
    params.api.setGridOption('popupParent', closestModal ?? document.body);
  }

  // drag event listener
  rangeSelectionService.registerRangeSelectionDragEvent(params.api);
  eventBus?.$on('contextMenu:copyRawValue', copyRawValues);
  eventBus?.$on('contextMenu:copyFormattedValue', copyFormattedValues);
};

const onGridPreDestroyed = () => {
  rangeSelectionService.removeCellRange();
  rangeSelectionService.removeRangeSelectionDragEvent();
  eventBus?.$off('contextMenu:copyRawValue', copyRawValues);
  eventBus?.$off('contextMenu:copyFormattedValue', copyFormattedValues);
};

const debouncedResizeTableColumns = useDebounceFn((api: GridApi) => {
  if (pivotGrid.value) {
    // case column width in viz block, we already defined it in columnDefs
    if (props.applyColumnWidth) {
      return;
    }

    resizePivotColumns(api, {
      totalRowColumns: Math.max(props.data.rowFields.length, 1),
      totalMeasureColumnsInGroup: props.data.measureFields.length,
      sizeToFit: props.isWidget || props.isStretch,
      containerWidth: pivotGrid.value.$el.clientWidth,
    });
  }
}, 50);

const debouncedShowFreezeOutOfViewport = useDebounceFn((api: GridApi, containerWidth: number = pivotGrid.value?.$el.clientWidth) => {
  emit('freezeOutOfViewport', isFreezeOutOfViewport(api, containerWidth));
}, 300);

const { gridHeight, calculateTableHeight } = useFitHeightGrid(props, gridApi, pivotGrid, emitFetchMore);

const onFirstDataRendered = async (params: FirstDataRenderedEvent<PivotTableRow>) => {
  const isFullyVisible = params.lastRow - params.firstRow + 1 >= props.data.numRows;

  await debouncedResizeTableColumns(params.api);

  // Allow some time for the table to fully render,
  // enabling calculateTableHeight to accurately determine the height of the horizontal scrollbar
  setTimeout(() => {
    calculateTableHeight();
  }, CALCULATE_TABLE_HEIGHT_DELAY_MS);

  trackScrollbarHeight(pivotGrid, params.api, gridHeight.value);

  // Since both the resize behavior and calculate height update the DOM,
  // we use setTimeout with 100ms to make sure captured export runs after both two these methods.
  setTimeout(() => {
    emit('render', { isFullyVisible });
  }, EMIT_RENDERED_EVENT_DELAY_MS);
};

const onGridColumnsChanged = (params: GridColumnsChangedEvent<PivotTableRow>) => {
  if (props.isWidget) {
    debouncedResizeTableColumns(params.api);
  }
  styleNewColumns(params.api, { autoSize: !props.isWidget, highlight: true });
};

const onGridSizeChanged = (params: GridSizeChangedEvent<PivotTableRow>) => {
  const isResized = params.clientHeight !== 0 || params.clientWidth !== 0;
  if (isResized && props.isWidget) {
    debouncedResizeTableColumns(params.api);
  }
  if (params.clientWidth > 0) {
    debouncedShowFreezeOutOfViewport(params.api);
  }
};

const onRowDataUpdated = () => {
  calculateTableHeight();
};

const resizePivotHelper = new ResizePivotHelper({
  shouldApplyColumnWidth: props.applyColumnWidth,
  shouldFullWidth: props.isWidget || props.isStretch,
});

const columnDefs = computed(() => buildPivotColumnOptions({
  propsData: props.data,
  isSingleLineRowEnabled: props.isSingleLineRow,
  rowSpanService,
  columnHeaderSortProps: props.sortable ? {
    sortOptions: props.sortOptions,
    onSort,
  } : undefined,
  highlightedRanges: get(props, 'crossFilter.highlightedRanges', []),
  handleCrossFilterByHeaderClicked,
  handleDrillThroughByHeaderClicked,
  rangeSelectionService,
  resizePivotHelper,
  aqlEnabled: aqlIsEnabled(props.dataSet as any),
}));

const rowData = computed(() => buildPivotRowData(props.data));

const { pinnedRowData } = usePivotTableAggregateRow(props);

const emptyData = computed<boolean>(() => {
  return get(props.data, 'pivotedData', []).length === 0;
});

const defaultColDef = computed(() => ({
  tooltipComponent: props.isSingleLineRow ? AgGridSingleRowPopover : undefined,
  tooltipComponentParams: {
    grid: pivotGrid,
  },
  minWidth: 50,
  suppressSpanHeaderHeight: true,
  suppressHeaderKeyboardEvent: (params: SuppressHeaderKeyboardEventParams) => {
    // handle copy command in custom header
    if (isCopyCommandPressed(params.event)) return true;

    return false;
  },
  sortable: false,
}));

const showLoading = computed(() => props.loading);
const { handleCrossFiltering } = usePivotTableCrossFiltering(props);

const { isInfiniteScrollEnabled, handleInfiniteScroll } = useInfiniteScroll(props, emitFetchMore, gridApi);

const pivotTheme = getDefaultTableStyle(PIVOT_TABLE);

const onCellClicked = (e: PivotTableCellClickedEventParams) => {
  if (rangeSelectionService.shouldIgnoreCellClicked(e?.event?.target)) {
    return;
  }

  rangeSelectionService.handleCellClicked(e);

  handleCrossFiltering(e);
};

const onCellContextMenu = getCellContextMenuHandler(rangeSelectionService, handleCellContextMenu);

const onBodyScroll = (e: BodyScrollEvent) => {
  useDebounceFn(() => {
    handleInfiniteScroll(e);
  }, 50)();
};

watch(() => props.height, () => {
  calculateTableHeight();
});

defineExpose({
  calculateTableHeight,
});

const handleCellKeyDown = (e: CellKeyDownEvent<PivotTableRow>) => {
  try {
    if (!isCopyCommandPressed(e.event as KeyboardEvent)) return;

    copyFormattedValues();
  } catch (error) {
    handleAjaxError(error);
  }
};

const debouncedHandleColumnResizedEvent = useDebounceFn((e: ColumnResizedEvent) => {
  const measureWidths = resizePivotHelper.getMeasureWidthFromColumns(e?.columns || []);
  // Sometime measure dependencies don't change immediately after resize a measure
  // So we need manual update to ensure ux better
  resizePivotHelper.resizePivot(e.api, measureWidths);
  emit('columnResized', measureWidths);
}, 300);

const onColumnResized = (e: ColumnResizedEvent) => {
  const isAllowResizeColumn = props.applyColumnWidth
                              && ['uiColumnResized', 'autosizeColumns'].includes(e.source)
                              && e.finished === true;

  if (!isAllowResizeColumn) return;
  debouncedHandleColumnResizedEvent(e);
};

function onStateUpdated (e: StateUpdatedEvent<PivotTableRow>) {
  debouncedShowFreezeOutOfViewport(e.api);
}
</script>
