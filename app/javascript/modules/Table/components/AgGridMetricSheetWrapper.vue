<template>
  <div
    v-h-loading.body.disableRelative="loading"
    class="data-table-wrapper metric-sheet-wrapper"
  >
    <div
      v-if="!IS_PUPPETEER && !emptyData"
      class="table-header w-full pb-2"
    >
      <div class="setting-wrapper">
        Show metrics by
        <div class="mx-1 min-w-36">
          <HSelect
            :model-value="dateTrunc"
            :clearable="false"
            :options="DATE_TRUNC_OPTIONS"
            theme="underline"
            @update:model-value="selectDateTrunc"
          >
            <template #right-annotation-option="{ option, focused, selected, searchText, onClick, onMouseover }">
              <div
                class="hui-select-option"
                :class="[
                  { focused, selected },
                  !selected
                ]"
                :data-value="option.value"
                @click="onClick"
                @mouseover="onMouseover"
              >
                <HTextHighlight
                  :text="option.label"
                  :highlight="searchText"
                />
                <span class="ml-1 inline-block text-description-xs text-blue-gray-500">{{ get(option, 'annotation') }}</span>
              </div>
            </template>
          </HSelect>
        </div>
        in
        <div class="mx-1 min-w-32">
          <HSelect
            :model-value="maxColumn"
            :options="MAX_COL_OPTIONS"
            :clearable="false"
            theme="underline"
            @update:model-value="selectMaxColumn"
          />
        </div>
      </div>
    </div>
    <div
      class="w-full overflow-hidden"
      :class="{ 'is-pptr': IS_PUPPETEER }"
    >
      <empty-table
        v-if="emptyData"
        :fields="data.fields"
      />
      <ag-grid-vue
        v-else
        ref="metricSheetGrid"
        class="h-metric-sheet w-full"
        data-ci="ci-ag-grid-metric-sheet"
        suppress-movable-columns
        suppress-scroll-on-new-data
        :style="{ height: `${gridHeight}px` }"
        :column-defs="columnDefs"
        :row-data="rowData"
        :full-width-cell-renderer="TextRow"
        :is-full-width-row="isFullWidthRow"
        :context="context"
        :process-unpinned-columns="onUnpinnedColumns"
        :suppress-max-rendered-row-restriction="IS_PUPPETEER"
        :theme="metricSheetTheme"
        @grid-ready="onGridReady"
        @first-data-rendered="onFirstDataRendered"
        @cell-clicked="onCellClicked"
        @cell-context-menu="onCellContextMenu"
        @cell-key-down="handleCellKeyDown"
        @row-data-updated="onRowDataUpdated"
        @grid-pre-destroyed="onGridPreDestroyed"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { HSelect, HTextHighlight } from '@holistics/design-system';
import { AgGridVue } from 'ag-grid-vue3';
import { get, isNil } from 'lodash';
import {
  ComponentPublicInstance,
  computed, inject, shallowRef, useTemplateRef, watch,
} from 'vue';
import {
  CellKeyDownEvent,
  FirstDataRenderedEvent,
  GridApi, GridReadyEvent, IsFullWidthRowParams,
} from 'ag-grid-community';
import { isCopyCommandPressed } from '@/core/utils/copy';
import { handleAjaxError } from '@holistics/aml-studio/h/services/ajax';
import type { createEventBus } from '@/core/services/eventBus';
import { CALCULATE_TABLE_HEIGHT_DELAY_MS, EMIT_RENDERED_EVENT_DELAY_MS } from '@/modules/Table/constants/tableRenderingTiming';
import { getDefaultTableStyle } from '@/modules/Table/constants/defaultTableStyle';
import { METRIC_SHEET } from '@/modules/Viz/constants/vizTypes';
import EmptyTable from './otherTables/EmptyTable.vue';
import type { MetricSheetPropData, MetricSheetCellClickedEventParams } from '../types/propData';
import {
  MAX_COL_OPTIONS, DATE_TRUNC_OPTIONS,
} from '../constants/metricSheet';
import { IS_PUPPETEER } from '../constants/puppeteer';
import { useMetricSheetDateColumn } from '../composables/metricSheetDateColumn';
import { useMetricSheetGridOptions } from '../composables/metricSheetGridOptions';
import type { MetricSheetRow } from '../types/metricSheet';
import { useFitHeightGrid } from '../composables/simpleFitHeight';
import TextRow from './cellComponents/TextRow.vue';
import { useMetricSheetContext } from '../composables/metricSheetContext';
import { RangeSelectionService } from '../services/RangeSelection';
import { copyCellRangeDataToClipboard } from '../utils/helpers/copy/copyToClipBoard';
import { onUnpinnedColumns } from '../utils/helpers/unpinnedColumns';
import { getCellContextMenuHandler } from '../utils/helpers/cellContextMenu';
import { useTableClickOutside } from '../composables/useTableClickOutside';

const props = withDefaults(defineProps<{
  data: MetricSheetPropData,
  isWidget?: boolean,
  height: number,
  loading?: boolean,
}>(), {
  isWidget: false,
  loading: false,
});

const emit = defineEmits<{
  settingChanged: [value: { dateTrunc: string, maxColumn: number }],
  render: [value: { isFullyVisible: boolean }],
}>();
const emitSettingChanged = (value: { dateTrunc: string, maxColumn: number }) => emit('settingChanged', value);

const gridApi = shallowRef<GridApi<MetricSheetRow>>();
const metricSheetGrid = useTemplateRef<ComponentPublicInstance>('metricSheetGrid');
const rangeSelectionService = new RangeSelectionService(metricSheetGrid);
useTableClickOutside(metricSheetGrid, rangeSelectionService);

const { context } = useMetricSheetContext(
  props,
  () => rangeSelectionService,
);
const {
  dateTrunc, maxColumn, selectDateTrunc, selectMaxColumn,
} = useMetricSheetDateColumn(props, emitSettingChanged);

const emptyData = computed<boolean>(() => {
  return get(props.data, 'values', []).length === 0;
});

const { columnDefs, rowData } = useMetricSheetGridOptions(props, gridApi);

const metricSheetTheme = getDefaultTableStyle(METRIC_SHEET);

function copyFormattedValues () {
  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange());
}

const eventBus = inject<ReturnType<typeof createEventBus>>('eventBus');
const onGridReady = (params: GridReadyEvent<MetricSheetRow>) => {
  gridApi.value = params.api;

  // drag event listener
  rangeSelectionService.registerRangeSelectionDragEvent(params.api);
  eventBus?.$on('contextMenu:copyFormattedValue', copyFormattedValues);
};

const onCellContextMenu = getCellContextMenuHandler(rangeSelectionService);

const onGridPreDestroyed = () => {
  rangeSelectionService.removeCellRange();
  rangeSelectionService.removeRangeSelectionDragEvent();
  eventBus?.$off('contextMenu:copyFormattedValue', copyFormattedValues);
};

const { gridHeight, calculateTableHeight } = useFitHeightGrid(props, metricSheetGrid, gridApi);

const isFullWidthRow = (params: IsFullWidthRowParams) => {
  const { rowIndex } = params.rowNode;
  if (isNil(rowIndex)) {
    return false;
  }
  return props.data.rows[rowIndex]?.rowType === 'text';
};

const onFirstDataRendered = async (params: FirstDataRenderedEvent<MetricSheetRow>) => {
  const isFullyVisible = params.lastRow - params.firstRow + 1 >= props.data.meta.num_rows;

  // Allow some time for the table to fully render,
  // enabling calculateTableHeight to accurately determine the height of the horizontal scrollbar
  setTimeout(() => {
    calculateTableHeight();
  }, CALCULATE_TABLE_HEIGHT_DELAY_MS);

  setTimeout(() => {
    emit('render', { isFullyVisible });
  }, EMIT_RENDERED_EVENT_DELAY_MS);
};

const handleCellKeyDown = (e: CellKeyDownEvent<MetricSheetRow>) => {
  try {
    if (!isCopyCommandPressed(e.event as KeyboardEvent)) return;

    copyFormattedValues();
  } catch (error) {
    handleAjaxError(error);
  }
};

const onCellClicked = (e: MetricSheetCellClickedEventParams) => {
  rangeSelectionService.handleCellClicked(e);
};

const onRowDataUpdated = () => {
  calculateTableHeight();
};

watch(() => props.height, () => {
  calculateTableHeight();
});

defineExpose({
  calculateTableHeight,
});
</script>
