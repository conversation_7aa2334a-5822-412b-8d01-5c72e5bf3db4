<template>
  <div
    v-if="shouldShowPopOver"
    class="popover-wrapper ci-single-line-row-popover bg-white"
  >
    <div class="single-row-popover">
      <div class="header mx-1 overflow-hidden border-b pb-2 pt-1">
        <span class="title text-capitalize font-medium text-gray-900">{{ title }}</span>
      </div>
      <div
        :style="bodyStyle"
        class="body flex p-1 pt-2"
      >
        <div class="content w-full flex-1 rounded p-1">
          <copy-btn
            class="copybox invisible absolute"
            size="md"
            type="secondary-default"
            :content="content"
          />
          <!-- eslint-disable vue/no-v-html -->
          <div
            class="ci-popover-content w-full text-justify text-xs"
            v-html="sanitizedContent"
          />
          <!-- eslint-disable vue/no-v-html -->
        </div>
      </div>
    </div>
  </div>
  <div v-else />
</template>
<script setup lang="ts">
import {
  ref, computed, onBeforeMount, Ref,
} from 'vue';
import CopyBtn from '@/core/components/ui/CopyBtn.vue';
import sanitizeHTML from '@holistics/utils/sanitizeHTML';
import { Column, ITooltipParams } from 'ag-grid-community';
import { get } from 'lodash';
import { AgGridVue } from 'ag-grid-vue3';
import { extractValueFromCell } from '@/modules/Viz/utils/valuesExtractors/dataTable';
import { checkShouldShowPopover } from '../../utils/renderers/helpers/checkShouldShowPopover';
import { generateCellQuerySelector } from '../../utils/helpers/generateCellQuerySelector';
import { ROW_NUMBER_FIELD } from '../../constants/rowNumber';
import { DataTableRow, DataTableCell } from '../../types/dataTable';

type Params = ITooltipParams<DataTableRow, DataTableCell>
& {
  colDef: {
    cellRenderer?: (params: ITooltipParams) => string,
  };
  grid: Ref<typeof AgGridVue | null>;
}

const props = defineProps<{
  params: Params
}>();

const shouldShowPopOver = ref(false);
const content = computed(() => {
  if (typeof props.params.colDef.cellRenderer === 'function') {
    return props.params.colDef.cellRenderer?.(props.params) ?? '';
  }

  return extractValueFromCell(props.params.value);
});
const sanitizedContent = computed(() => sanitizeHTML(content.value));
const bodyStyle = computed(() => {
  const minWidth = props.params.column ? Math.max(props.params.column.getActualWidth(), 250) : 250;
  return `width: ${minWidth}px`;
});
const title = computed(() => props.params.colDef?.headerName || '');

function calculateShouldShowPopover () {
  // get HTML element
  const rowId = get(props.params, 'node.id', '');

  if (!('column' in props.params)) {
    return false;
  }

  const colId = (props.params.column as Column).getColId() ?? ROW_NUMBER_FIELD;

  const cellQuerySelector = generateCellQuerySelector(rowId, colId);

  const htmlElement = props.params.grid.value?.$el.querySelector(cellQuerySelector) as HTMLElement;
  // check truncated, utilise from dataTableSingleLineRowMixin
  return checkShouldShowPopover(htmlElement);
}

onBeforeMount(() => {
  shouldShowPopOver.value = calculateShouldShowPopover();
});
</script>
<style lang="postcss">
.popover-wrapper {
  border-radius: 4px;
  @apply shadow;
  @apply border border-gray-300; /* border/default */
  padding: 4px;

  .single-row-popover {
    .header {
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .body {
      max-height: 150px;
      .content {
        overflow-y: auto;
        overflow-wrap: break-word;
        min-height: 36px;
        &:hover {
          .copybox {
            visibility: visible;
          }
        }
        .copybox {
          right: 12px;
        }
      }
    }
  }
}
</style>
