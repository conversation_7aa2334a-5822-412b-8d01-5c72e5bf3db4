<template>
  <div
    v-h-loading.body.disableRelative="loading"
    class="data-table-wrapper"
  >
    <empty-table
      v-if="empty"
      :fields="data.fields"
    />
    <ag-grid-vue
      v-else
      ref="cohortRetentionGrid"
      class="h-cohort-retention w-full"
      data-ci="ci-ag-grid-cohort-retention"
      suppress-movable-columns
      suppress-scroll-on-new-data
      tooltip-interaction
      :animate-rows="false"
      :style="{ height: `${gridHeight}px` }"
      :column-defs="columnDefs"
      :row-data="rowData"
      :default-col-def="defaultColDef"
      :row-height="ROW_HEIGHT"
      :header-height="ROW_HEIGHT"
      :context="context"
      :process-unpinned-columns="onUnpinnedColumns"
      :suppress-max-rendered-row-restriction="IS_PUPPETEER"
      :theme="defaultTableTheme"
      @grid-ready="onGridReady"
      @first-data-rendered="onFirstDataRendered"
      @row-data-updated="onRowDataUpdated"
      @cell-key-down="handleCellKeyDown"
      @grid-size-changed="onGridSizeChanged"
      @cell-clicked="onCellClicked"
      @cell-context-menu="onCellContextMenu"
      @grid-pre-destroyed="onGridPreDestroyed"
    />
  </div>
</template>

<script setup lang="ts">
import { get } from 'lodash';
import {
  ComponentPublicInstance,
  computed, inject, shallowRef, useTemplateRef, watch,
} from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import {
  CellKeyDownEvent, FirstDataRenderedEvent, GridApi, GridReadyEvent, GridSizeChangedEvent, SuppressHeaderKeyboardEventParams,
} from 'ag-grid-community';
import { isCopyCommandPressed } from '@/core/utils/copy';
import { handleAjaxError } from '@holistics/aml-studio/h/services/ajax';
import { useDebounceFn } from '@vueuse/core';
import type { createEventBus } from '@/core/services/eventBus';
import { CALCULATE_TABLE_HEIGHT_DELAY_MS, EMIT_RENDERED_EVENT_DELAY_MS } from '@/modules/Table/constants/tableRenderingTiming';
import EmptyTable from './otherTables/EmptyTable.vue';
import { CohortRetentionPropData, CohortRetentionCellClickedEventParams } from '../types/propData';
import { useCohortRetentionGridOptions } from '../composables/cohortRetentionGridOptions';
import { useCohortRetentionContext } from '../composables/cohortRetentionContext';
import { ROW_HEIGHT } from '../constants/rowHeight';
import { CohortRetentionRow } from '../types/cohortRetention';
import { useFitHeightGrid } from '../composables/simpleFitHeight';
import { RangeSelectionService } from '../services/RangeSelection';
import { copyCellRangeDataToClipboard } from '../utils/helpers/copy/copyToClipBoard';
import { resizeCohortRetentionColumns } from '../utils/renderers/resize/resizeColumns';
import { onUnpinnedColumns } from '../utils/helpers/unpinnedColumns';
import { getCellContextMenuHandler } from '../utils/helpers/cellContextMenu';
import { flatCohortRetentionFormats } from '../utils/renderers/pivot/flatFormats';
import { createVizFormatter } from '../utils/helpers/copy/copyFormatter';
import { IS_PUPPETEER } from '../constants/puppeteer';
import { defaultTableTheme } from '../constants/defaultTableStyle';
import { useTableClickOutside } from '../composables/useTableClickOutside';

const props = withDefaults(defineProps<{
  data: CohortRetentionPropData,
  height: number,
  loading?: boolean,
}>(), {
  loading: false,
});

const emit = defineEmits<{
  render: [value: { isFullyVisible: boolean }],
}>();

const empty = computed<boolean>(() => {
  return get(props.data, 'values', []).length === 0;
});

const cohortRetentionGrid = useTemplateRef<ComponentPublicInstance>('cohortRetentionGrid');
const gridApi = shallowRef<GridApi<CohortRetentionRow>>();
const rangeSelectionService = new RangeSelectionService(cohortRetentionGrid);
useTableClickOutside(cohortRetentionGrid, rangeSelectionService);

const { context } = useCohortRetentionContext(
  props,
  () => rangeSelectionService,
);
const {
  columnDefs, rowData,
} = useCohortRetentionGridOptions(props, gridApi);
const { gridHeight, calculateTableHeight } = useFitHeightGrid(props, cohortRetentionGrid);
const defaultColDef = computed(() => ({
  suppressHeaderKeyboardEvent: (params: SuppressHeaderKeyboardEventParams) => {
    // handle copy command in custom header
    if (isCopyCommandPressed(params.event)) return true;

    return false;
  },
  sortable: false,
}));

const debouncedResizeTableColumns = useDebounceFn((api: GridApi) => {
  if (cohortRetentionGrid.value) {
    resizeCohortRetentionColumns(api, {
      startValueColumnIndex: props.data.hasCohortSize ? 2 : 1,
      containerWidth: cohortRetentionGrid.value.$el.clientWidth,
    });
  }
}, 50);

function copyRawValues () {
  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange());
}
function copyFormattedValues () {
  const { formats, fields } = props.data;
  const flattedFormats = flatCohortRetentionFormats({ formats, totalValueFields: formats.sizeFormat ? fields.length - 2 : fields.length - 1 });
  const vizFormatterFunc = createVizFormatter({ formats: flattedFormats, isConvertedNullToZeroEnabled: false });
  copyCellRangeDataToClipboard(rangeSelectionService.getDataValuesRange(vizFormatterFunc));
}

const eventBus = inject<ReturnType<typeof createEventBus>>('eventBus');
const onGridReady = (params: GridReadyEvent<CohortRetentionRow>) => {
  gridApi.value = params.api;

  // drag event listener
  rangeSelectionService.registerRangeSelectionDragEvent(params.api);
  eventBus?.$on('contextMenu:copyRawValue', copyRawValues);
  eventBus?.$on('contextMenu:copyFormattedValue', copyFormattedValues);
};

const onFirstDataRendered = async (params: FirstDataRenderedEvent<CohortRetentionRow>) => {
  const isFullyVisible = params.lastRow - params.firstRow + 1 >= props.data.values.length;
  await debouncedResizeTableColumns(params.api);

  // Allow some time for the table to fully render,
  // enabling calculateTableHeight to accurately determine the height of the horizontal scrollbar
  setTimeout(() => {
    calculateTableHeight();
  }, CALCULATE_TABLE_HEIGHT_DELAY_MS);

  setTimeout(() => {
    emit('render', { isFullyVisible });
  }, EMIT_RENDERED_EVENT_DELAY_MS);
};

const handleCellKeyDown = (e: CellKeyDownEvent<CohortRetentionRow>) => {
  try {
    if (!isCopyCommandPressed(e.event as KeyboardEvent)) return;

    copyFormattedValues();
  } catch (error) {
    handleAjaxError(error);
  }
};
const onCellContextMenu = getCellContextMenuHandler(rangeSelectionService);

const onCellClicked = (e: CohortRetentionCellClickedEventParams) => {
  rangeSelectionService.handleCellClicked(e);
};

const onRowDataUpdated = () => {
  calculateTableHeight();
};

const onGridSizeChanged = (params: GridSizeChangedEvent<CohortRetentionRow>) => {
  debouncedResizeTableColumns(params.api);
};

const onGridPreDestroyed = () => {
  rangeSelectionService.removeCellRange();
  rangeSelectionService.removeRangeSelectionDragEvent();
  eventBus?.$off('contextMenu:copyRawValue', copyRawValues);
  eventBus?.$off('contextMenu:copyFormattedValue', copyFormattedValues);
};

watch(() => props.height, () => {
  calculateTableHeight();
});
</script>
