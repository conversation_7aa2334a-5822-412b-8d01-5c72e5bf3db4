<template>
  <div
    :class="{ 'retention-heatmap': !isAgGridCohortRetentionEnabled }"
  >
    <div
      class="retention-heatmap-table"
      :class="{'h-full': fullHeight}"
    >
      <ag-grid-cohort-retention-wrapper
        v-if="isAgGridCohortRetentionEnabled"
        :data="handsomeData"
        :height="height"
        @render="onRender"
      />
      <data-table
        v-else
        ref="table"
        :data="handsomeData"
        :height="height"
        :auto-width="autoWidth"
        :no-stretch="noStretch"
        retention
        @render="onRender"
      />
    </div>
    <div
      v-if="!isPuppeteer"
      class="toggle-wrapper p-1"
    >
      <HSwitch
        v-if="settings.percentage_display"
        v-model="displayRawValues"
        class="ci-show-raw-values show-raw-values"
        label="Display Raw Values"
      />
    </div>
    <table-foot-note
      v-if="meta"
      ref="foot-note"
      :is-table-fully-visible="isFullyVisible"
      :record-count="recordCount"
      :page-size="pageSize"
    />
  </div>
</template>

<script>
import { HSwitch } from '@holistics/design-system';
import { ValueFormatter } from '@/raw/value_formatter_raw';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import DataTable from '@/modules/Table/components/DataTable.vue';
import AgGridCohortRetentionWrapper from '@/modules/Table/components/AgGridCohortRetentionWrapper.vue';
import TableFootNote from '@/modules/Table/components/sub/TableFootNote.vue';
import shouldResizeTable from '@/modules/Viz/utils/shouldResizeTable';
import { get } from 'lodash';
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import vizRenderingMixin from '../../mixins/vizRenderingMixin';

export default {
  name: 'RetentionHeatmap',
  components: {
    HSwitch,
    DataTable,
    AgGridCohortRetentionWrapper,
    TableFootNote,
  },
  mixins: [vizRenderingMixin],
  props: {
    fields: {
      type: Array,
      required: true,
    },
    values: {
      type: Object,
      required: true,
    },
    settings: {
      type: Object,
      required: true,
    },
    meta: {
      type: Object,
      default: null,
    },
    colors: {
      type: Object,
      required: true,
    },
    hasCohortSize: {
      type: Boolean,
      required: true,
    },
    autoWidth: {
      type: Boolean,
      default: false,
    },
    noStretch: {
      type: Boolean,
      default: false,
    },
    initialHeight: {
      type: Number,
      default: 400,
    },
    fullHeight: {
      type: Boolean,
      default: true,
    },
  },
  data () {
    return {
      valueFormatter: new ValueFormatter(),
      displayRawValues: false,
      height: this.initialHeight,
      isFullyVisible: false,
      previousContainerSize: { w: 0, h: 0 },
      isAgGridCohortRetentionEnabled: checkFeatureToggle('ag-grid:cohort-retention'),
    };
  },
  computed: {
    displayPercentage () {
      return this.settings.percentage_display && !this.displayRawValues;
    },
    colorsMap () {
      return this.settings.percentage_display ? this.colors.percentage : this.colors.raw;
    },
    handsomeData () {
      const { fields, settings, hasCohortSize } = this;
      return {
        fields,
        values: this.displayPercentage ? this.values.percentage : this.values.raw,
        formats: settings.formats,
        colors: this.colorsMap,
        displayPercentage: this.displayPercentage || (!this.displayPercentage && !this.settings.percentage_display),
        hasCohortSize,
      };
    },
    isPuppeteer () {
      return window.PHANTOM;
    },
    recordCount () {
      return get(this.meta, 'numRows', 0);
    },
    pageSize () {
      return this.recordCount;
    },
  },
  mounted () {
    ModuleRegistry.registerModules([AllCommunityModule]);
  },
  methods: {
    fitToContainer (container) {
      const containerHeight = container.clientHeight;
      const containerWidth = container.clientWidth;

      if (!shouldResizeTable(
        { width: containerWidth, height: containerHeight },
        { width: this.previousContainerSize.w, height: this.previousContainerSize.h },
      )) {
        return;
      }

      this.previousContainerSize.h = containerHeight;
      this.previousContainerSize.w = containerWidth;

      const toggleWrapper = this.$el.querySelector('.toggle-wrapper');
      const toggleHeight = toggleWrapper ? toggleWrapper.clientHeight : 0;
      const footNoteHeight = get(this.$refs['foot-note'], '$el.clientHeight', 0);
      const tableHeight = Math.max(containerHeight - toggleHeight - footNoteHeight, 0);
      if (!this.isAgGridCohortRetentionEnabled && this.height === tableHeight) {
        // this case happens when the container width is changed
        // call render() to reflow the width
        this.$refs.table.render();
      } else {
        // handsometable automatically handles tableHeight changes
        this.height = tableHeight;
      }
    },
    onRender ({ isFullyVisible } = {}) {
      this.isFullyVisible = isFullyVisible;
      this.finishRendering();
    },
  },
};
</script>
