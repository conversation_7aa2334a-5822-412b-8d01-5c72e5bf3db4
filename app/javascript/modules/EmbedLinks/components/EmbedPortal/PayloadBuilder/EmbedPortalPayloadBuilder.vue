<template>
  <div class="flex h-full flex-col">
    <div class="flex h-full flex-col overflow-hidden">
      <div class="flex justify-between border-b px-3">
        <div class="py-3 text-xs font-medium">
          Embed Payload
        </div>
        <div class="flex items-center">
          <HSegmentedControl
            v-model="payloadViewMode"
            :items="[
              { label: 'UI', value: 'ui' },
              { label: 'JSON', value: 'json' },
            ]"
            size="sm"
          />
        </div>
      </div>
      <div class="flex flex-1 flex-col justify-between overflow-y-auto">
        <div
          v-show="isUIMode"
          class="flex flex-1 flex-col"
        >
          <SelfServiceSettings
            v-if="isSSBIEnabled"
            v-model:embed-org-id="embedPayload.embed_org_id"
            v-model:embed-user-id="embedPayload.embed_user_id"
            v-model:permissions="embedPayload.permissions"
          />
          <UserAttributesSettings
            v-model:user-attributes="embedPayload.user_attributes"
            :embed-portal="embedPortal"
          />
          <GeneralSettings
            v-model:settings="embedPayload.settings"
            v-model:expiry-duration="expiryDuration"
          />
        </div>
        <div
          v-show="!isUIMode"
          class="flex-1"
        >
          <CodeHighlight
            :code="JSON.stringify(embedPayload, null, 2)"
            type="json"
            immediate
          />
        </div>
      </div>
    </div>
    <div class="flex items-center justify-center bg-gray-100 p-3">
      <HButton
        type="primary-highlight"
        icon="play-no-circle"
        block
        data-ci="preview-embed-portal-modal-preview-button"
        @click="emit('previewPortal')"
      >
        Preview
      </HButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EmbedPortal, EmbedPortalPayload } from '@/modules/EmbedPortal/types';
import { HSegmentedControl, HButton } from '@holistics/design-system';
import { computed, ref } from 'vue';
import * as FeatureToggle from '@/core/services/featureToggle';
import { CodeHighlight } from '@aml-studio/h/components';
import SelfServiceSettings from './SelfServiceSettings.vue';
import UserAttributesSettings from './UserAttributes/UserAttributesSettings.vue';
import GeneralSettings from './GeneralSettings.vue';

type PayloadViewMode = 'ui' | 'json';

defineProps<{
  embedPortal: EmbedPortal,
}>();

const isSSBIEnabled = FeatureToggle.check('embed_portal:ssbi_enabled');

const embedPayload = defineModel<EmbedPortalPayload>({ required: true });
const expiryDuration = defineModel<number | null>('expiryDuration', { required: true });

const emit = defineEmits(['previewPortal']);

const payloadViewMode = ref<PayloadViewMode>('ui');
const isUIMode = computed(() => payloadViewMode.value === 'ui');
</script>
