<template>
  <div>
    <div
      v-if="permissionRules.length === 0"
      class="ml-4"
    >
      No affected Permission Rules
    </div>
    <div
      v-else
      class="flex cursor-pointer align-middle"
      :class="{ 'text-orange-800': isEmptyAttributeValues }"
      @click="shown = !shown"
    >
      <HIcon :name="shown ? 'caret-down' : 'caret-right'" />
      <HIcon
        v-if="isEmptyAttributeValues"
        class="mr-1"
        name="exclamation-triangle"
        size="sm"
      />
      Affected Permission Rules ({{ permissionRules.length }})
    </div>
    <div
      v-if="shown"
      class="ml-4 mt-[6px]"
    >
      <div v-if="permissionRules.length">
        <div v-if="isEmptyAttributeValues">
          User can't see any data associated with
        </div>
        <div v-else>
          User can see data with
        </div>
        <div
          v-for="(permissionRule, index) in permissionRules"
          :key="index"
          class=" mt-[10px] border-l border-gray-400"
        >
          <div class="ml-1">
            <HIcon name="data-set" />
            {{ readableDatasets[permissionRule.data_set_id].title }}
            >
            <HIcon name="data-model" />
            {{ permissionRule.field_path.model_id }}
            >
            {{ permissionRule.field_path.field_name }}
          </div>
          <div
            v-if="attributeValues === '__ALL__'"
            class="ml-1 mt-1"
          >
            is any value
          </div>
          <div
            v-else-if="!isEmptyAttributeValues"
            class="ml-1 mt-1"
          >
            is {{ attributeValues }}
          </div>
        </div>
      </div>
      <div v-else>
        This attribute isn't used in any permission rules.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import RowLevelPermissionRule from '@/modules/DataSets/models/RowLevelPermissionRule';
import { EmbedPortal } from '@/modules/EmbedPortal/types';
import { HIcon } from '@holistics/design-system';
import { isArray, keyBy } from 'lodash';
import { computed, defineProps, ref } from 'vue';

export type AttributeValue = string | boolean | number | (string | boolean | number)[];

const props = defineProps<{
  embedPortal: EmbedPortal,
  permissionRules: RowLevelPermissionRule[],
  attributeValues: AttributeValue,
}>();

const shown = ref(false);

const readableDatasets = computed(() => {
  return keyBy(props.embedPortal.readableDatasets, 'id');
});

const isEmptyAttributeValues = computed(() => isArray(props.attributeValues) && props.attributeValues.length === 0);

</script>
