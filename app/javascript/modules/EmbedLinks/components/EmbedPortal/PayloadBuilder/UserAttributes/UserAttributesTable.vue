<template>
  <div>
    <!-- HEADER -->
    <div class="flex text-gray-800">
      <div class="w-[144px] px-2 py-1">
        Attributes
      </div>
      <div class="flex-grow px-2 py-1">
        Values
      </div>
    </div>

    <!-- ATTRIBUTES -->
    <div
      v-for="(attributeValues, attributeName) of userAttributesPayload"
      :key="attributeName"
      class="mt-2"
    >
      <hr>
      <UserAttributeValuesInput
        :embed-portal="embedPortal"
        :related-permission-rules="permissionRulesByUserAttribute(attributeName)"
        :attribute-name="attributeName"
        :attribute-type="attributeType(attributeName)"
        :values="attributeValues"
        :is-required="isRequiredAttribute(attributeName)"
        @update-value="values => updatePayloadUserAttribute(attributeName, values)"
        @delete="deletePayloadUserAttribute(attributeName)"
      />
      <RelatedPermissionRules
        class="ml-[6px] mt-2"
        :embed-portal="embedPortal"
        :permission-rules="permissionRulesByUserAttribute(attributeName)"
        :attribute-values="attributeValues"
      />
    </div>
    <hr class="mt-4">

    <!-- ADD ATTRIBUTE BUTTON -->
    <HDropdown
      class="mt-4"
      content-class="w-[200px]"
      :options="unsetUserAttributeOptions"
      @select="a => updatePayloadUserAttribute((a as DropdownOptionDefault).key, '__ALL__')"
    >
      <HButton
        type="secondary-default"
        icon="add"
        size="md"
      >
        Add user attribute
      </HButton>
      <template #footer>
        <div class="overflow-hidden px-2 py-1 text-gray-600">
          These user attributes may be used in permission rules, dynamic data source/schema, column-level permissions or AQL
        </div>
      </template>
      <template #empty>
        <div class="overflow-hidden px-2 py-1 text-gray-600">
          All attributes are already in the payload
        </div>
      </template>
    </HDropdown>
  </div>
</template>

<script setup lang="ts">
import type { EmbedPortal, EmbedPortalPayload, EmbedUserAttribute } from '@/modules/EmbedPortal/types';
import {
  DropdownOption,
  DropdownOptionDefault,
  HButton, HDropdown, IconName,
} from '@holistics/design-system';
import {
  difference, get, isNil, keyBy, keys, omit,
} from 'lodash';
import { computed } from 'vue';
import { CORE_TYPES_MAP } from '@/modules/DataModels/constants';
import RowLevelPermissionRule from '@/modules/DataSets/models/RowLevelPermissionRule';
import UserAttributeValuesInput from './UserAttributeValuesInput.vue';
import RelatedPermissionRules from './RelatedPermissionRules.vue';

const props = defineProps<{
  embedPortal: EmbedPortal,
}>();

const userAttributesPayload = defineModel<EmbedPortalPayload['user_attributes']>('userAttributesPayload', { required: true });
const embedUserAttributes = computed<Record<string, EmbedUserAttribute>>(() => {
  return keyBy(props.embedPortal.embedUserAttributes, 'attributeName');
});

const attributeType = (attributeName: string): string => {
  return embedUserAttributes.value[attributeName].attributeType;
};

const isRequiredAttribute = (attributeName: string): boolean => {
  const attribute = embedUserAttributes.value[attributeName];
  return attribute.required && isNil(attribute.defaultValues);
};

const updatePayloadUserAttribute = (attributeName: string, value: any) => {
  userAttributesPayload.value = {
    ...userAttributesPayload.value,
    [attributeName]: value,
  };
};

const unsetUserAttributeNames = computed(() => {
  return difference(keys(embedUserAttributes.value), keys(userAttributesPayload.value));
});

const iconFor = (userAttributeType: string): IconName => {
  return get(CORE_TYPES_MAP, [userAttributeType, 'icon'], 'unknown') as IconName;
};

const unsetUserAttributeOptions = computed<DropdownOption[]>(() => {
  const attributeOptions: DropdownOption[] = unsetUserAttributeNames.value.map(a => ({
    key: a,
    label: a,
    icons: iconFor(attributeType(a)),
  }));

  const attributeDescription: DropdownOption[] = [
    { type: 'divider' },
    { key: 'footer', slot: 'footer' },
  ];

  if (attributeOptions.length) {
    return attributeOptions.concat(attributeDescription);
  }

  const emptyDescription: DropdownOption[] = [{ key: 'empty', slot: 'empty' }];
  return emptyDescription.concat(attributeDescription);
});

const deletePayloadUserAttribute = (attributeName: string) => {
  userAttributesPayload.value = omit(userAttributesPayload.value, attributeName);
};

const permissionRulesByUserAttributes = computed(() => {
  const hash: Record<string, RowLevelPermissionRule[]> = {};
  props.embedPortal.rowLevelPermissionRules.forEach(rule => {
    if (rule.condition.operator !== 'matches_user_attribute') return;

    rule.condition.values.forEach(attributeName => {
      if (typeof attributeName !== 'string') return;

      let rulesByAttribute = hash[attributeName];
      if (!rulesByAttribute) rulesByAttribute = [];
      rulesByAttribute.push(rule);
      hash[attributeName] = rulesByAttribute;
    });
  });
  return hash;
});

const permissionRulesByUserAttribute = (attributeName: string) => {
  return permissionRulesByUserAttributes.value[attributeName] || [];
};
</script>
