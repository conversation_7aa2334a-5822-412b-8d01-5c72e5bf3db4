import type { TreeNode } from '@holistics/design-system';
import type { EmbedPortal as EmbedPortalDefinition } from '@holistics/aml-std';
import type RowLevelPermissionRule from '../DataSets/models/RowLevelPermissionRule';

export type SourceType = 'QueryReport' | 'Dashboard' | 'AmlStudio::Project';

export type EmbedDashboard = {
  type: 'Dashboard',
  id: number | string,
  uname: string,
  title: string,
}

export type EmbedDataset = {
  type: 'Dataset',
  id: number | string,
  uname: string,
  title: string,
}

export type EmbedObject = EmbedDashboard | EmbedDataset;
export type EmbedPortalConfig = {
  id: number,
  uname: string,
  projectId: string,
  definition?: EmbedPortalDefinition,
  embedObjects: {
    dashboards: EmbedDashboard[],
    datasets: EmbedDataset[],
    personalDashboards?: EmbedDashboard[],
    orgDashboards?: EmbedDashboard[],
  },
  actionBasedPermission: {
    orgWorkspace: {
      canView: boolean,
      canEdit: boolean,
    },
    personalWorkspace: {
      canView: boolean,
      canEdit: boolean,
    },
  }
};

export type CustomAttributesValuesType = (string | number | boolean)[];
export type AttributeValuesType = string | number | boolean | CustomAttributesValuesType; // including '__ALL__'

export type EmbedUserAttribute = {
  attributeName: string,
  attributeType: string,
  defaultValues: null | AttributeValuesType,
  required: boolean,
}

export type EmbedPortal = {
  id: number,
  uname: string,
  embedUserAttributes: EmbedUserAttribute[],
  readableDatasets: { id: number, uname: string, title: string }[],
  rowLevelPermissionRules: RowLevelPermissionRule[],
};

export type EmbedLink = {
  id: number,
  sourceType: SourceType,
  sourceId: string,
  sourceToParam: string,
  sourceTitle: string,
  secretKey: string,
  hashCode: string,
  version: number,
  owner: string,
  title: string,
  filterValues: Record<string, unknown>[],
}

export type EmbedCredentials = EmbedLink & { sourceType: 'AmlStudio::Project' }

export type EmbedPortalPayload = {
  object_name: string,
  object_type: 'EmbedPortal',
  embed_org_id?: string;
  embed_user_id?: string;
  permissions?: {
    org_workspace_role: 'no_access' | 'viewer' | 'editor',
    enable_personal_workspace: boolean,
  },
  user_attributes?: Record<string, AttributeValuesType>,
  exp?: number,
  settings?: {
    default_timezone?: string | null,
    allow_to_change_dashboard_timezone?: boolean,
    allow_to_export_raw_data?: boolean,
  }
}

export interface NodePermission {
  canCreateChildren?: boolean,
  canEdit?: boolean,
  canDelete?: boolean
}

export interface EmbedObjectTreeNode extends TreeNode<EmbedObjectTreeNode> {
  key: string
  rawObject?: EmbedObject,
  permission: NodePermission
  isDeleting?: boolean
}

export type PreviewNode = Pick<EmbedObject, 'type' | 'id'>;
