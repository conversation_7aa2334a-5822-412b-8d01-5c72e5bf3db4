import type { EmbedObjectTreeNode } from '@/modules/EmbedPortal/types';
import { type DropdownOption, type TreeContextMenuOptions, HIcon } from '@holistics/design-system';
import { h } from 'vue';
import FloatingDeletePanel from '@/core/components/ui/FloatingDeletePanel.vue';
import { ORG_WORKSPACE_NODE_KEY, PERSONAL_WORKSPACE_NODE_KEY } from '@/modules/EmbedPortal/helpers/buildEmbedPortalTreeNodes';

export interface UseEmbedPortalContextMenuOptions {
  deleteDashboardFn: (node: EmbedObjectTreeNode) => Promise<void>;
  createDashboardFn: (isPersonal: boolean) => Promise<void>;
}

export const useEmbedPortalContextMenu = ({ createDashboardFn, deleteDashboardFn }: UseEmbedPortalContextMenuOptions) => {
  const hasNodeMenuAction = (node: EmbedObjectTreeNode) => {
    const { canDelete } = node.permission;

    return !!canDelete;
  };

  // dropdown option when clicking tree dot icon
  const buildNodeMenuActions = (node: EmbedObjectTreeNode): DropdownOption[] => {
    const dropdownOptions: DropdownOption[] = [];

    const { permission, rawObject } = node;

    if (permission.canDelete && rawObject?.type === 'Dashboard') {
      dropdownOptions.push({
        type: 'render',
        render: ({ select, option: dropdownOption }) => h(FloatingDeletePanel, {
          title: node.label,
          type: 'dashboard',
          onDeletePanel: async () => {
            deleteDashboardFn(node);
            // close the dropdown menu
            select(dropdownOption);
          },
          'data-ci': 'delete-embed-portal-node',
        }, {
          default: () => h('div', {
            class: 'flex items-start space-x-1 rounded p-2 cursor-pointer text-red-500 hover:bg-gray-100 active:bg-gray-400',
          }, [
            h(HIcon, { name: 'delete', class: 'mr-1' }),
            'Delete',
          ]),
        }),
        key: `delete-dashboard-${node.key}`,
        class: 'text-red-500',
      });
    }

    return dropdownOptions;
  };

  // right click a node
  const buildNodeContextMenu: TreeContextMenuOptions<EmbedObjectTreeNode> = node => {
    const options = buildNodeMenuActions(node);

    if (node.permission.canCreateChildren && node.key === ORG_WORKSPACE_NODE_KEY) {
      const creationOption: DropdownOption = {
        label: 'Add shared dashboard',
        key: 'create-new-dashboard',
        icons: 'add',
        action: () => createDashboardFn(false),
      };

      options.unshift(creationOption);
    }

    if (node.permission.canCreateChildren && node.key === PERSONAL_WORKSPACE_NODE_KEY) {
      const creationOption: DropdownOption = {
        label: 'Add personal dashboard',
        key: 'create-new-dashboard',
        icons: 'add',
        action: () => createDashboardFn(true),
      };

      options.unshift(creationOption);
    }

    return options;
  };

  return { hasNodeMenuAction, buildNodeContextMenu };
};
