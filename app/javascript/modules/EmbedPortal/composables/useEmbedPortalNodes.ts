import { onMounted, ref, watchEffect } from 'vue';
import { onBeforeRouteUpdate, RouteLocationNormalizedLoaded, useRouter } from 'vue-router';
import type { TreeNodeKey } from '@holistics/design-system';
import { useToasts } from '@/core/composables/useToasts';
import { errorMessageFromAjax } from '@/core/services/ajax';
import { useEmbedPortalStore } from '../store/embedPortalStore';
import type { EmbedObjectTreeNode, PreviewNode } from '../types';
import {
  buildDashboardCreationNode,
  buildEmbedPortalTreeNodes,
  buildRouteForNode,
  generateNodeKey,
  NEW_ORG_DASHBOARD_NODE_KEY,
  NEW_PERSONAL_DASHBOARD_NODE_KEY,
  ORG_WORKSPACE_NODE_KEY,
  PERSONAL_WORKSPACE_NODE_KEY,
} from '../helpers/buildEmbedPortalTreeNodes';

export function useEmbedPortalNodes () {
  const embedPortalStore = useEmbedPortalStore();
  const { hashcode, secretToken } = embedPortalStore;
  const { toast } = useToasts();

  const nodes = ref<EmbedObjectTreeNode[]>([]);
  watchEffect(() => {
    nodes.value = embedPortalStore.embedPortal ? buildEmbedPortalTreeNodes(embedPortalStore.embedPortal, secretToken as string) : [];
  });

  const router = useRouter();
  const selectedKey = ref<TreeNodeKey>();

  function goToDashboardCreationPage (isPersonal: boolean) {
    return router.push({
      name: isPersonal ? 'EmbedPortalCreatePersonalDashboard' : 'EmbedPortalCreateOrgDashboard',
      query: {
        _token: secretToken,
      },
    });
  }

  async function addDashboardCreationNode ({ sectionKey, dashboardKey }: { sectionKey: string; dashboardKey: string }) {
    // @ts-ignore
    const section = nodes.value.find(node => node.key === sectionKey);
    section?.children?.push(buildDashboardCreationNode(dashboardKey));
  }

  async function goDashboardEditPage (dashboardId: number) {
    return router.push({
      name: 'EmbedPortalDashboardEdit',
      params: { hashcode, dashboard_id: dashboardId },
      query: {
        _token: secretToken,
      },
    });
  }

  function removeDashboardCreationNode ({ sectionKey, dashboardKey }: { sectionKey: string; dashboardKey: string }) {
    // @ts-ignore
    const section = nodes.value.find(node => node.key === sectionKey);
    const index = (section?.children || []).findIndex(node => node.key === dashboardKey);

    if (index !== -1) {
      section?.children?.splice(index, 1);
    }
  }

  function handleRouteUpdate (to: RouteLocationNormalizedLoaded, from?: RouteLocationNormalizedLoaded) {
    // eslint-disable-next-line default-case
    switch (from?.name) {
      case 'EmbedPortalCreateOrgDashboard':
        removeDashboardCreationNode({
          sectionKey: ORG_WORKSPACE_NODE_KEY,
          dashboardKey: NEW_ORG_DASHBOARD_NODE_KEY,
        });
        break;
      case 'EmbedPortalCreatePersonalDashboard':
        removeDashboardCreationNode({
          sectionKey: PERSONAL_WORKSPACE_NODE_KEY,
          dashboardKey: NEW_PERSONAL_DASHBOARD_NODE_KEY,
        });
        break;
    }

    switch (to?.name) {
      case 'EmbedPortalDashboard':
      case 'EmbedPortalDashboardEdit':
        selectedKey.value = generateNodeKey({ type: 'Dashboard', id: parseInt(to.params.dashboard_id as string) });
        break;
      case 'EmbedPortalDataset':
        selectedKey.value = generateNodeKey({ type: 'Dataset', id: parseInt(to.params.dataset_id as string) });
        break;
      case 'EmbedPortalCreateOrgDashboard':
        selectedKey.value = NEW_ORG_DASHBOARD_NODE_KEY;
        addDashboardCreationNode({
          sectionKey: ORG_WORKSPACE_NODE_KEY,
          dashboardKey: NEW_ORG_DASHBOARD_NODE_KEY,
        });
        break;
      case 'EmbedPortalCreatePersonalDashboard':
        selectedKey.value = NEW_PERSONAL_DASHBOARD_NODE_KEY;
        addDashboardCreationNode({
          sectionKey: PERSONAL_WORKSPACE_NODE_KEY,
          dashboardKey: NEW_PERSONAL_DASHBOARD_NODE_KEY,
        });
        break;
      default:
        selectedKey.value = undefined;
    }
  }

  function goToNode (node: PreviewNode) {
    const route = buildRouteForNode(node, secretToken);
    router.push(route);
  }

  async function deleteDashboardNode (node: EmbedObjectTreeNode) {
    try {
      const nodeKey = node.key;
      node.isDeleting = true;
      await embedPortalStore.deleteEmbedDashboard(node.rawObject?.id as number);

      if (nodeKey === selectedKey.value) {
        router.push({
          name: 'DashboardEmbed',
          query: {
            _token: secretToken,
          },
        });
      }

      node.isDeleting = false;
      toast.success('Successfully delete dashboard');
    } catch (e: any) {
      node.isDeleting = false;
      toast.danger(errorMessageFromAjax((e)));
    }
  }

  onBeforeRouteUpdate(handleRouteUpdate);

  onMounted(() => {
    handleRouteUpdate(router.currentRoute.value);
  });

  return {
    nodes, selectedKey, goToDashboardCreationPage, goDashboardEditPage, goToNode, deleteDashboardNode,
  };
}
