import sanitizeHtml, { SAFE_HTML_TAGS } from '@holistics/utils/sanitizeHTML';

type RenderParams = {
  textToHighlight: string | RegExp | null,
  highlightClass: string | null,
};
type RenderFunction = (text: string, renderParams: RenderParams) => string;

const sanitizeHTML = ({ text, highlightClass }: { text: string, highlightClass?: string | null }) => {
  const allowedClasses = highlightClass ? highlightClass.split(' ') : [];

  return sanitizeHtml(
    text,
    SAFE_HTML_TAGS.concat(['mark']),
    { span: ['class'] },
    { allowedClassesMap: { mark: allowedClasses } },
  );
};

// Copied and modified from: https://codepen.io/brianmearns/pen/YVjZWw

const getTextSegments = (element: HTMLElement) => {
  const textSegments: { text: string; node: Node }[] = [];
  Array.from(element.childNodes).forEach((node) => {
    switch (node.nodeType) {
      case Node.TEXT_NODE:
        textSegments.push({ text: node.nodeValue || '', node });
        break;

      case Node.ELEMENT_NODE:
        if (node.nodeName === 'BR') {
          // Chrome uses &nbsp; to preserve double spaces while Firefox uses the <br > element and puts it at the end.
          // Therefore, must keep the BR node.
          textSegments.push({ text: (node as HTMLElement).outerHTML, node });
        } else {
          textSegments.splice(textSegments.length, 0, ...(getTextSegments(node as HTMLElement)));
        }
        break;

      default:
        throw new Error(`Unexpected node type: ${node.nodeType}`);
    }
  });
  return textSegments;
};

const renderHighlightedText = (text: string, renderParams: RenderParams) => {
  const { textToHighlight, highlightClass } = renderParams;
  if (!textToHighlight) {
    return text;
  }

  if (typeof textToHighlight === 'string') {
    const reg = new RegExp(textToHighlight.replace(/\s/g, '\\s'));
    return text.replace(reg, `<mark class="${highlightClass || ''}">${textToHighlight}</mark>`);
  } if (textToHighlight instanceof RegExp) {
    return text.replace(textToHighlight, `<mark class="${highlightClass || ''}">$1</mark>`);
  }

  return text;
};

const restoreSelection = (el: HTMLElement, absoluteAnchorIndex: number, absoluteFocusIndex: number) => {
  const sel = window.getSelection();
  const textSegments = getTextSegments(el);
  let anchorNode: HTMLElement = el;
  let anchorIndex = 0;
  let focusNode: HTMLElement = el;
  let focusIndex = 0;
  let currentIndex = 0;
  textSegments.forEach(({ text, node }) => {
    const startIndexOfNode = currentIndex;
    const endIndexOfNode = startIndexOfNode + text.length;
    if (startIndexOfNode <= absoluteAnchorIndex && absoluteAnchorIndex <= endIndexOfNode) {
      anchorNode = node as HTMLElement;
      anchorIndex = absoluteAnchorIndex - startIndexOfNode;
    }
    if (startIndexOfNode <= absoluteFocusIndex && absoluteFocusIndex <= endIndexOfNode) {
      focusNode = node as HTMLElement;
      focusIndex = absoluteFocusIndex - startIndexOfNode;
    }
    currentIndex += text.length;
  });

  if (sel) {
    sel.setBaseAndExtent(anchorNode, anchorIndex, focusNode, focusIndex);
  }
};

const highlight = ({
  el, renderParams, restoreSelectionAfterHighlighting, renderFunction = renderHighlightedText,
}: {
  el: HTMLElement,
  renderParams: RenderParams,
  restoreSelectionAfterHighlighting: boolean,
  renderFunction?: RenderFunction,
}): string => {
  const sel = window.getSelection();

  if (!sel) {
    return '';
  }

  const textSegments = getTextSegments(el);
  const textContent = textSegments.map(({ text }) => text).join('');
  let anchorIndex = null;
  let focusIndex = null;
  let currentIndex = 0;
  textSegments.forEach(({ text, node }) => {
    if (node === sel.anchorNode) {
      anchorIndex = currentIndex + sel.anchorOffset;
    }
    if (node === sel.focusNode) {
      focusIndex = currentIndex + sel.focusOffset;
    }
    currentIndex += text.length;
  });

  el.innerHTML = sanitizeHTML({
    text: renderFunction(textContent, renderParams),
    highlightClass: renderParams.highlightClass,
  });

  if (restoreSelectionAfterHighlighting && anchorIndex && focusIndex) {
    // restore the previous text selection to let user continue inputing
    restoreSelection(el, anchorIndex, focusIndex);
  }

  const normalizedText = textContent.replace(/\s/g, ' '); // normalize the output

  // Check if the content is just a BR tag (with potential whitespace)
  if (normalizedText.trim() === '<br>' || normalizedText.trim() === '<br/>') {
    return '';
  }

  return normalizedText;
};

const highlightTextWithRegex = ({
  el, regexToHighlight, highlightClass, restoreSelectionAfterHighlighting,
}: {
  el: HTMLElement,
  regexToHighlight: RegExp,
  highlightClass: string | null,
  restoreSelectionAfterHighlighting: boolean,
}): string => {
  return highlight({
    el,
    renderParams: { textToHighlight: regexToHighlight, highlightClass },
    restoreSelectionAfterHighlighting,
  });
};

const highlightText = ({
  el, textToHighlight, highlightClass, restoreSelectionAfterHighlighting,
}: {
  el: HTMLElement,
  textToHighlight: string,
  highlightClass: string | null,
  restoreSelectionAfterHighlighting: boolean,
}): string => {
  return highlight({
    el,
    renderParams: { textToHighlight, highlightClass },
    restoreSelectionAfterHighlighting,
  });
};

const updateHtml = ({
  el, htmlText,
}: {
  el: HTMLElement,
  htmlText: string,
}): string => {
  el.innerHTML = sanitizeHTML({ text: htmlText });

  const selection = window.getSelection();

  if (!selection) {
    return '';
  }
  const range = selection.getRangeAt(0);
  range.selectNodeContents(el);
  range.collapse(false); // set caret at end

  return htmlText.replace(/\s/g, ' '); // normalize the output
};

const updateTextAndHighlight = ({
  el, textToInsert, textToHighlight, highlightClass,
}: {
  el: HTMLElement,
  textToInsert: string,
  textToHighlight: string,
  highlightClass: string,
}): string => {
  const reg = new RegExp(textToHighlight.replace(/\s/g, '\\s'), 'g');
  const htmlText = sanitizeHTML({
    text: textToInsert
      .replace(/\s/g, '&nbsp;')
      .replace(reg, `<mark class="${highlightClass}">${textToHighlight}</mark>`),
    highlightClass,
  });

  return updateHtml({ el, htmlText });
};

const updateTextAndHighlightWithRegex = ({
  el, textToInsert, highlightRegex, highlightClass,
}: {
  el: HTMLElement,
  textToInsert: string,
  highlightRegex: RegExp,
  highlightClass: string,
}): string => {
  const htmlText = sanitizeHTML({
    text: textToInsert
      .replace(/\s/g, '&nbsp;')
      .replace(highlightRegex, `<mark class="${highlightClass}">$1</mark>`),
    highlightClass,
  });

  return updateHtml({ el, htmlText });
};

export {
  highlightText,
  highlightTextWithRegex,
  updateTextAndHighlight,
  updateTextAndHighlightWithRegex,
};
