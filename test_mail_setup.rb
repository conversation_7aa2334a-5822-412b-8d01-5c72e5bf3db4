#!/usr/bin/env ruby

# Test script to verify mail service setup
# Run this with: bundle exec rails runner test_mail_setup.rb

puts "Testing Mail Service Setup..."
puts "=" * 50

# Test 1: Check environment variables
puts "\n1. Checking Environment Variables:"
required_vars = %w[SMTP_HOST SMTP_PORT BASE_URL]
required_vars.each do |var|
  value = ENV[var]
  if value.present?
    puts "✅ #{var}: #{value}"
  else
    puts "❌ #{var}: MISSING"
  end
end

# Test 2: Check ActionMailer configuration
puts "\n2. Checking ActionMailer Configuration:"
puts "✅ Delivery method: #{Rails.application.config.action_mailer.delivery_method}"
puts "✅ SMTP settings: #{Rails.application.config.action_mailer.smtp_settings}"
puts "✅ Default URL options: #{Rails.application.config.action_mailer.default_url_options}"

# Test 3: Test SMTP connection
puts "\n3. Testing SMTP Connection:"
begin
  require 'net/smtp'
  smtp_host = ENV['SMTP_HOST'] || 'localhost'
  smtp_port = ENV['SMTP_PORT']&.to_i || 1025
  
  Net::SMTP.start(smtp_host, smtp_port) do |smtp|
    puts "✅ SMTP connection successful to #{smtp_host}:#{smtp_port}"
  end
rescue => e
  puts "❌ SMTP connection failed: #{e.message}"
  puts "   Make sure MailHog or another SMTP server is running on #{smtp_host}:#{smtp_port}"
end

# Test 4: Test sending a simple email
puts "\n4. Testing Email Delivery:"
begin
  # Create a simple test mailer
  test_mailer = Class.new(ApplicationMailer) do
    def test_email
      mail(
        to: '<EMAIL>',
        from: '<EMAIL>',
        subject: 'Test Email from Holistics',
        body: 'This is a test email to verify mail service setup.'
      )
    end
  end
  
  # Send the test email
  test_mailer.test_email.deliver_now
  puts "✅ Test email sent successfully!"
  puts "   Check MailHog at http://localhost:8025 to see the email"
  
rescue => e
  puts "❌ Email delivery failed: #{e.message}"
  puts "   Error details: #{e.backtrace.first(3).join("\n   ")}"
end

# Test 5: Check Sidekiq status
puts "\n5. Checking Sidekiq Status:"
begin
  require 'sidekiq/api'
  stats = Sidekiq::Stats.new
  puts "✅ Sidekiq is running"
  puts "   Processed jobs: #{stats.processed}"
  puts "   Failed jobs: #{stats.failed}"
  puts "   Enqueued jobs: #{stats.enqueued}"
  puts "   Queues: #{Sidekiq::Queue.all.map(&:name).join(', ')}"
rescue => e
  puts "❌ Sidekiq connection failed: #{e.message}"
end

# Test 6: Test Data Schedule email functionality
puts "\n6. Testing Data Schedule Email Setup:"
begin
  # Check if we have any email schedules
  email_schedule_count = EmailSchedule.count
  puts "✅ EmailSchedule model accessible"
  puts "   Total email schedules: #{email_schedule_count}"
  
  # Check if we have the required mailer
  if defined?(DataEmailMailer)
    puts "✅ DataEmailMailer is available"
  else
    puts "❌ DataEmailMailer not found"
  end
  
rescue => e
  puts "❌ Data Schedule setup issue: #{e.message}"
end

puts "\n" + "=" * 50
puts "Mail Service Setup Test Complete!"
puts "\nNext Steps:"
puts "1. If SMTP connection failed, start MailHog: docker run -d -p 1025:1025 -p 8025:8025 mailhog/mailhog"
puts "2. If test email was sent, check http://localhost:8025 to view it"
puts "3. If Sidekiq issues, restart with: bundle exec sidekiq"
puts "4. Test actual data schedule emails through the UI"
