# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Shareable Link Index Page', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:analyst) { get_test_analyst }
  let(:user) { get_test_user }
  let(:group) { FactoryBot.create :group }

  let(:shared_report) { FactoryBot.create :query_report, title: 'Has shareable links and is shared with other analysts', owner: admin }
  let(:shared_dashboard) { FactoryBot.create :dashboard, title: 'Has shareable links and is shared with other analysts', owner: admin }
  let(:r2) { FactoryBot.create :query_report, title: 'Has shareable links but not shared with anyone', owner: admin }
  let(:d2) { FactoryBot.create :dashboard, title: 'Has shareable links but not shared with anyone', owner: admin }

  before(:each) do
    FactoryBot.create :query_report, title: 'No shareable links', owner: admin
    FactoryBot.create :dashboard, title: 'No shareable links', owner: admin

    admin.share analyst, 'read', shared_report
    admin.share group, 'read', shared_report
    admin.share analyst, 'read', shared_dashboard
    admin.share group, 'read', shared_dashboard

    [shared_report, shared_dashboard, r2, d2].each do |resource|
      FactoryBot.create :shareable_link,
                         tenant_id: resource.tenant_id, owner_id: resource.owner_id,
                         resource_type: resource.class.name, resource_id: resource.id
    end

    safe_login(:admin, '/shareable_links')
    wait_for_element_load('.shareable-links-index .shareable-sources table')
  end

  it 'shows only reports / dashboards that have shareable links' do
    wait_expect(4) { page.all('.shareable-links-index .shareable-sources table tbody tr').size }
    wait_expect(true) do
      page.all(".shareable-links-index .shareable-sources table tbody tr td:nth-child(4) a")
          .map { |node| node.text.scan(/^(\d+)\s+/).first.first.to_i }
          .all? { |count| count > 0 }
    end
  end

  it 'allows navigation to the actual reports / dashboards' do
    safe_click('.shareable-links-index a.ci-source-url')
    sleep 2 # wait for navigation
    expect(page.current_path).to match /^\/(dashboards|queries)/
  end

  it 'displays and allows editing shared users / groups by clicking on Shared With column' do
    unshared_report = page.find(".shareable-links-index .ci-actor-#{r2.class.name}-#{r2.id}")
    wait_expect('Nobody') { unshared_report.text }
    unshared_report.click
    share_all
    wait_expect('All users') { unshared_report.text }
  end

  it 'displays and allows editing shareable links by clicking on link count' do
    link = page.find(".shareable-links-index .ci-link-#{r2.class.name}-#{r2.id}")
    wait_expect('1 link(s)') { link.text }

    link.click
    safe_click '.shareable-link-manage-modal .ci-new-shareable-link'
    safe_click '.shareable-link-edit-modal .ci-save-links'
    safe_click '.shareable-link-manage-modal .close-btn'
    wait_expect('2 link(s)') { link.text }
  end
end
