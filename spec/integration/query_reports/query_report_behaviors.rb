# typed: false
require 'rails_helper'

describe 'Query report behaviors', js: true, stable: true do
  include_context 'test_tenant'
  include_context 'data_set'

  let!(:query_report) { create :products_table_report, owner: admin, data_set: data_set }

  it 'press edit button on navigation node should navigate to right route' do
    safe_login(admin, query_report_path(query_report))

    page.find('.ci-navigation-node .active').hover
    safe_click('.ci-navigation-node .active .ci-navigation-node-action')
    safe_click('.navigation-node-action-popover a:first-child')

    wait_for_element_load('.h-data-set-report-editor')

    expect(current_path).to match(/edit_model/)
  end
end
