# typed: false
require 'rails_helper'

describe 'report show page single value', js: true, stable: true do
  let(:report) {
    FactoryBot.create :query_report,
      title: 'All-time Registrations',
      query: 'SELECT 10000 as "Total Registrations"'
  }

  it 'show single value table' do
    safe_login :admin, query_report_path(report)
    wait_expect(true) { page.has_css?('.table-single-value') }
  end
end
