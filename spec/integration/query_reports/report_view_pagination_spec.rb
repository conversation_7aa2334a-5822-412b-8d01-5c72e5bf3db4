# typed: false
require 'rails_helper'

describe 'report view pagination', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:table) { page.find('[data-ci="ci-ag-grid-data-table"]') }
  before do
    sql = <<-SQL.strip_heredoc
        select generate_series(1, 25) as num;
    SQL
    settings = {paging_page_size: 10}
    @report = FactoryBot.create :query_report, title: 'Paginated Table',
                                 query: sql, settings: settings
  end

  it 'works correctly when user uses pagination features' do
    qlogin(:admin, query_report_path(@report))
    sleep 1

    wait_for_element_load('.hui-pagination .hui-pagination-item')

    texts = page.all('.hui-pagination .hui-pagination-item').map(&:text)
    expect(texts[1..-2]).to eq(['1', '2', '3'])

    wait_for_report_load
    values = get_handsome_table_values(page)
    expect(values.map(&:first).map(&:to_i)).to eq([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])

    page.all('.hui-pagination .hui-pagination-item')[2].click
    sleep 1

    values = get_handsome_table_values(page)
    expect(values.map(&:first).map(&:to_i)).to eq([11, 12, 13, 14, 15, 16, 17, 18, 19, 20])

    expect(page.find('.ci-pagination-info').text).to eq('11 - 20 of 25')

    # takes me back to first page when i click sort
    open_context_menu_by_name(element: table, header_name: 'num')
    safe_click('.ci-sort-asc-btn')
    sleep 1

    expect(page.find('.ci-pagination-info').text).to eq('1 - 10 of 25')
  end
end
