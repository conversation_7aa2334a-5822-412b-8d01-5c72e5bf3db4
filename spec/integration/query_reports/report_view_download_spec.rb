# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Download Report Data', js: true, stable: true do
  let(:query) do
    <<-SQL.strip_heredoc
      VALUES ('2013-01-01', 100, 200),
             ('2013-01-02', 200, 400),
             ('2013-01-03', 300, 600)
    SQL
  end
  let(:expected_body) do
    <<-CSV.strip_heredoc
          column1,column2,column3
          2013-01-01 Tue,100,200
          2013-01-02 Wed,200,400
          2013-01-03 Thu,300,600
    CSV
  end
  let(:report) do
    FactoryBot.create :query_report, title: 'Viki vs Non-viki', query: query
  end

  def open_query_report
    path = query_report_path(report)
    safe_login(get_test_admin, path)
  end

  def wait_for_exporting
    wait_for_element_load('.ci-export-success', 30)
  end

  def submit_export_csv
    safe_click('.ci-toggle-download')
    safe_click('.ci-download-csv')
    wait_for_exporting
  end

  it 'return csv file if download as csv' do
    open_query_report
    submit_export_csv
    metadata = test_download_link
    expect(metadata.base_uri.to_s).to include '1-viki-vs-non-viki-qr_execute:t1-qr1:d1611f7cb64753a4dbcc823bd32ab832.csv'
  end

  context 'use secure bucket' do
    before do
      FeatureToggle.toggle_global('exports:secure_bucket', true)
    end
    after do
      FeatureToggle.toggle_global('exports:secure_bucket', false)
    end
    it 'return csv file if download as csv' do
      open_query_report
      submit_export_csv
      metadata = test_download_link
      expect(metadata.base_uri.to_s).to match %r{query_reports/.+\.csv}
    end
  end

  it 'return excel file if download as excel' do
    open_query_report
    safe_click('.ci-toggle-download')
    safe_click('.ci-download-xlsx')
    wait_for_exporting

    metadata = test_download_link
    expect(metadata.base_uri.to_s).to include '1-viki-vs-non-viki-qr_execute:t1-qr1:d1611f7cb64753a4dbcc823bd32ab832.xlsx'
  end

  context 'download pdf file' do
    let(:report) { FactoryBot.create :query_report_new }

    before do
      FeatureToggle.toggle_tenant(ImageExporters::PuppeteerRunner::FT_PDF_EXPORT, report.tenant_id, true)
    end

    it 'returns pdf file' do
      open_query_report
      safe_click('.ci-toggle-download')
      safe_click('.ci-download-pdf')
      wait_for_exporting

      metadata = test_download_link
      expect(metadata.base_uri.to_s).to match /1-random-new-report-qr_execute:t1-qr1:a11894dc45aa2b55f98ee858fd7b995e.*\.pdf$/

      file_content = metadata.read.force_encoding('utf-8')
      test_pdf_content!(file_content)
    end
  end
end
