# typed: false
require 'rails_helper'

describe 'Lazy Dropdown Filter', js: true, stable: true do
  context 'created with 2 options (sg, Singapore) and (vn, Viet Nam)' do
    let (:filter_name) { 'drop' }
    let (:test_db) { get_test_ds }
    let (:report_query) { "select * from (values ('sg'), ('vn'), ('sg')) t where [[column1 = {{ #{ filter_name } }}]]" }

    after {
      page.evaluate_script 'window.onbeforeunload = undefined'
      page.evaluate_script 'window.onpopstate = undefined'
    }

    before do
      # create data for filter
      fqname = FQName.parse('public.test_data')
      columns = [{column_name: 'value', data_type: 'varchar'}, {column_name: 'text', data_type: 'varchar'}]
      rows = [['sg', 'Singapore'], ['vn', 'Viet Nam'], ['sg', 'Singapore']]
      ds_create_table(connector: Connectors.from_ds(test_db), columns: columns, fqname: fqname, rows: rows)
    end

    def prefill_dropdown_filter_modal
      wait_for_element_load '.add-filter-btn'
      first('.add-filter-btn').click

      # Open Adhoc Filter modal
      page.find('.hui-dropdown-floating .ci-filter-type-2').click

      sleep 2
      wait_for_element_load '.ci-modal-edit-filter'

      # prefill basic info for dropdown filter
      page.all('.ci-modal-edit-filter .ci-sf-name').first.set(filter_name)
      select_h_select_option('.ci-filter-type-select', value: 'dropdown')
      page.find('.ci-dropdown-lazy-loading').set(true)

      # set default value
      page.find('input.ci-dropdown-filter-default').set('vn')
    end

    def search_singapore_in_dropdown_filter(from_data_source: false, with_unique_values_ft: false)
      safe_click '.filter-item'
      wait_for_element_load '.select2-selection'
      sleep 1
      safe_click('.select2-selection')
      sleep 1
      fill_text 'input.select2-search__field', 'si'

      if from_data_source || with_unique_values_ft
        wait_expect(1) { page.all('.select2-results li').count }
      else
        wait_expect(2) { page.all('.select2-results li').count }
      end

      wait_expect('Singapore') { page.first('.select2-results li')&.text }
      page.first('.select2-results li').click
    end

    def expect_preview_results_to_have_2_rows
      # submit
      page.find('.ci-btn-run').click

      wait_expect (2) { page.all('.ci-table-report-data .ag-row')&.size }
    end

    def setup_manual_data
      safe_click('.ci-dropdown-manual-entries')
      h_radio_check(value: 'manual')
      wait_for_element_load('.table-filter-manual-entries')
      page.find('.table-filter-manual-entries thead:nth-child(1) th:nth-child(1)').double_click # enter table edit mode
      page.find('textarea.ci-dropdown-manual-textarea').set("sg,Singapore\nvn,Viet Nam\nsg,Singapore") # manual csv entries
      safe_click('.ci-dropdown-manual-entries')
      page.find('.ci-shared-filter-save').click # submit
    end

    def select_test_data_source
      page.find('.ci-datasource-select').click
      page.find('.ci-datasource-select option:nth-child(2)').click
    end

    def setup_sql_data
      safe_click('.ci-dropdown-sql')
      select_test_data_source
      fill_in_ace '#dropdown-sql-editor', "select * from (values ('sg', 'Singapore'), ('vn', 'Viet Nam'), ('sg', 'Singapore')) t" # sql for data

      scroll_to('.ci-shared-filter-save')
      page.find('.ci-shared-filter-save').click # submit
    end

    def setup_db_records
      safe_click('.ci-dropdown-db')
      select_test_data_source

      # fill column name, refer to data created in before block for column names
      page.find('input.ci-dropdown-table-name').set('test_data')
      page.find('input.ci-dropdown-value-col').set('value')
      page.find('input.ci-dropdown-text-col').set('text')

      scroll_to('.ci-shared-filter-save')
      wait_for_element_load('.ci-shared-filter-save')
      page.find('.ci-shared-filter-save').click # submit
    end

    context 'when `filters:unique_values_in_dropdown_filters` feature toggle is off' do
      context 'during report creation' do
        def visit_report_create_page
          qlogin :admin, '/queries/new'

          # prefill query
          fill_in_ace '', report_query
        end

        it 'should work for manual records' do
          visit_report_create_page
          prefill_dropdown_filter_modal
          setup_manual_data
          search_singapore_in_dropdown_filter
          expect_preview_results_to_have_2_rows
        end

        it 'should work for sql records' do
          visit_report_create_page
          prefill_dropdown_filter_modal
          setup_sql_data
          search_singapore_in_dropdown_filter
          expect_preview_results_to_have_2_rows
        end

        it 'should work for database records' do
          visit_report_create_page
          prefill_dropdown_filter_modal
          setup_db_records
          search_singapore_in_dropdown_filter(from_data_source: true)
          expect_preview_results_to_have_2_rows
        end
      end

      context 'during report edit' do
        def visit_report_edit_page
          report = FactoryBot.create :query_report_new, query: report_query
          qlogin :admin, edit_query_report_path(report)
        end

        it 'should work for manual records' do
          visit_report_edit_page
          prefill_dropdown_filter_modal
          setup_manual_data
          search_singapore_in_dropdown_filter

          expect_preview_results_to_have_2_rows
        end

        it 'should work for sql records' do
          visit_report_edit_page
          prefill_dropdown_filter_modal
          setup_sql_data
          search_singapore_in_dropdown_filter

          expect_preview_results_to_have_2_rows
        end

        it 'should work for database records' do
          visit_report_edit_page
          prefill_dropdown_filter_modal
          setup_db_records
          search_singapore_in_dropdown_filter(from_data_source: true)

          expect_preview_results_to_have_2_rows
        end
      end
    end

    context 'when `filters:unique_values_in_dropdown_filters` feature toggle is on' do
      before { FeatureToggle.toggle_global('filters:unique_values_in_dropdown_filters', true) }

      context 'during report creation' do
        def visit_report_create_page
          qlogin :admin, '/queries/new'

          # prefill query
          fill_in_ace '', report_query
        end

        it 'should work for manual records' do
          visit_report_create_page
          prefill_dropdown_filter_modal
          setup_manual_data
          search_singapore_in_dropdown_filter(with_unique_values_ft: true)
          expect_preview_results_to_have_2_rows
        end

        it 'should work for sql records' do
          visit_report_create_page
          prefill_dropdown_filter_modal
          setup_sql_data
          search_singapore_in_dropdown_filter(with_unique_values_ft: true)
          expect_preview_results_to_have_2_rows
        end

        it 'should work for database records' do
          visit_report_create_page
          prefill_dropdown_filter_modal
          setup_db_records
          search_singapore_in_dropdown_filter(from_data_source: true, with_unique_values_ft: true)
          expect_preview_results_to_have_2_rows
        end
      end

      context 'during report edit' do
        def visit_report_edit_page
          report = FactoryBot.create :query_report_new, query: report_query
          qlogin :admin, edit_query_report_path(report)
        end

        it 'should work for manual records' do
          visit_report_edit_page
          prefill_dropdown_filter_modal
          setup_manual_data
          search_singapore_in_dropdown_filter(with_unique_values_ft: true)

          expect_preview_results_to_have_2_rows
        end

        it 'should work for sql records' do
          visit_report_edit_page
          prefill_dropdown_filter_modal
          setup_sql_data
          search_singapore_in_dropdown_filter(with_unique_values_ft: true)

          expect_preview_results_to_have_2_rows
        end

        it 'should work for database records' do
          visit_report_edit_page
          prefill_dropdown_filter_modal
          setup_db_records
          search_singapore_in_dropdown_filter(from_data_source: true, with_unique_values_ft: true)

          expect_preview_results_to_have_2_rows
        end
      end
    end
  end
end
