# typed: false
require 'rails_helper'

describe 'report view show workbooks', js: true, legacy: true do
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }
  let(:user) { get_test_user }
  let(:cat) { FactoryBot.create :report_category, is_workbook: true }
  let(:r1) {
    FactoryBot.create :query_report, title: 'Test Report Inside Workbook', category: cat, tenant: tenant
  }
  let(:r2) {
    FactoryBot.create :query_report, title: 'Report 2', category: cat, tenant: tenant
  }
  let(:r3) {
    FactoryBot.create :query_report, title: 'Report 3', category: cat, tenant: tenant
  }
  let(:r4) {
    FactoryBot.create :query_report, title: 'Report 4 (not shared)', category: cat, tenant: tenant
  }

  before do
    # share reports to business user
    admin.share(user, :read, r1)
    admin.share(user, :read, r2)
    admin.share(user, :read, r3)
  end

  it 'has a tab that shows all reports of the same workbook' do
    # login bizuser
    safe_login user, query_report_path(r1)

    # view report
    # check if workbook tabs are available
    # make sure unshared reports are not displayed
    wait_expect(3) { page.all('.report-tab').count }
  end
end
