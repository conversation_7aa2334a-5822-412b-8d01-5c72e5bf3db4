# typed: false
require 'rails_helper'

describe 'report with some filter names that use rails keywords', js: true, stable: true do
  let!(:ds) { get_test_ds }

  before do
    query = <<-SQL.strip_heredoc
      WITH R AS (
        select column1 as a FROM (
          values ('vn'), ('sg'), ('us'), ('id')
        ) R1
      )
      select a, {{controller}} from R
      where [[ a IN ({{action}}) ]]
    SQL
    sf1 = FactoryBot.create :shared_filter, settings: {
      type: 'dropdown',
      dropdown_source: 'manual',
      dropdown_manual_entries: "sg,Singapore\nvn,Vietnam\nus,US\nid,Indonesia",
      dropdown_multiselect: true
    }
    sf2 = FactoryBot.create :shared_filter
    sf3 = FactoryBot.create :shared_filter
    @qr = FactoryBot.create :query_report, title: 'Test Text Input Report',
                             query: query, data_source_id: ds.id
    fo1 = FactoryBot.create :filter_ownership, filterable: @qr, shared_filter: sf1, var_name: 'action', default: 'sg'
    fo2 = FactoryBot.create :filter_ownership, filterable: @qr, shared_filter: sf2, var_name: 'controller', default: 'test'
    fo3 = FactoryBot.create :filter_ownership, filterable: @qr, shared_filter: sf3, var_name: 'v', default: 'real string'
  end

  it 'can read the filter value just fine' do
    qlogin(:admin, "/queries/#{@qr.id}?action[]=vn&controller=Huy&v=somestring&action[]=sg")
    wait_for_report_load
    sleep 0.5
    values = get_handsome_table_values page
    expect(values.first.first).to eq 'vn'
    expect(values.first[1]).to eq 'Huy'
  end

end
