# typed: false
# frozen_string_literal: true

require 'rails_helper'

PREDEFINED_COLORS = ['rgba(181, 227, 205, 1)', 'rgba(250, 210, 209, 1)', 'rgba(240, 207, 173, 1)'].freeze

describe 'Conditional formatting', js: true do
  include_context 'test_tenant'

  let (:query) do
    <<-SQL.strip_heredoc
    select column1 as country, column2 as product, column3 as name, column4 as time, column5 as rev
      from (
        values
          ('Viet Nam', 'Book', 'Laura', '2019-11-01' :: date, 50::numeric),
          ('Viet Nam', 'Book', 'Britney', '2019-12-01' :: date, 150 ),
          ('Viet Nam', 'Pen', 'Laura', '2019-12-01' :: date, 50),
          ('Viet Nam', 'Pen', 'Britney', '2019-12-01' :: date, 80),
          ('Viet Nam', 'Book', 'Laura', '2019-12-01' :: date, 50),
          ('Viet Nam', 'Pen', 'Laura', '2019-11-01' :: date, 50),
          ('Singapore', 'Book', 'Laura', '2019-12-01' :: date, 70),
          ('Singapore', 'Book', 'Britney', '2019-12-01' :: date, 40),
          ('Singapore', '<PERSON>', '<PERSON>', '2019-12-01' :: date, 60),
          ('Singapore', 'Pen', 'Britney', '2019-12-01' :: date, 50),
          ('Singapore', 'Book', '<PERSON>', '2019-11-01' :: date, 30),
          ('Singapore', 'Pen', '<PERSON>', '2019-11-01' :: date, 70),
          ('Viet Nam', 'Pen', '<PERSON>', '2019-10-01' :: date, '23845621290950143'),
          ('Viet Nam', 'Pen', 'Britney', '2019-10-01' :: date, '23845621290950144')
      ) R
    SQL
  end

  def choose_style_tab
    safe_click('.viz-setting-tabs .ci-tab-toggle:nth-child(2)')
  end

  def edit_rule(rule_number:, rule_value:, color_selector_nth: 1, is_new_cf: false, rule_operator: '')
    safe_click(".ci-rule-row:nth-child(#{rule_number}) .ci-rule-setting-value") unless is_new_cf

    select_h_select_option('.ci-rule-setting-operator', value: rule_operator) if rule_operator.present?
    page.find('.ci-rule-setting-popover .ci-rule-setting-value').set(rule_value)
    safe_click('.ci-predefined-template')
    safe_click(".ci-single-predefined-color-preview:nth-child(#{color_selector_nth})")
    safe_click('.ci-apply-rule')
  end

  def expect_result(td_value:, expected_match_cells:, color: PREDEFINED_COLORS[0])
    tds = page.all(:css, '.ag-cell', text: /^#{td_value}$/)
    # filter cells which is on total aggregation
    tds = tds.reject { |td| td['class']&.include? 'h-aggregated-cell' }

    color_match_cells = tds.select { |td| td.native.css_value('background-color') == color }
    expect(color_match_cells.length).to eq expected_match_cells
  end

  def get_uuid_from_table_field(table_field_index)
    vs = VizSetting.last
    until (uuid = vs.fields.dig(:table_fields, table_field_index, :uuid)).present?
      sleep 0.1
      vs.reload
    end
    uuid
  end

  def get_uuid_from_pivot_field(value_index)
    vs = VizSetting.last
    until (uuid = vs.fields.dig(:pivot_data, :values, value_index, :uuid)).present?
      sleep 0.1
      vs.reload
    end
    uuid
  end

  context 'query editor' do
    before do
      safe_login(admin, '/adhoc/query_editor')
      wait_expect('') { ace_get_text }
      ace_set_text('#adhoc-editor', query)
      safe_click('.ci-run-query')
      wait_for_viz_load
      @adhoc_query_model = DataModel.where(backend_type: 'PgcacheModel').first
    end

    context 'data_table' do
      it 'should works' do
        choose_style_tab
        select_h_select_option('.conditional-formatter-field-select', value: "#{@adhoc_query_model.id}$!rev")
        edit_rule(rule_number: 1, rule_value: 50, is_new_cf: true)
        expect_result(td_value: 50, expected_match_cells: 5)
      end

      it 'should only allow apply rule to columns which had been choosen' do
        select_h_select_option('.table-fields .ci-empty-field', value: "#{@adhoc_query_model.id}$!rev")
        select_h_select_option('.table-fields .ci-empty-field', value: "#{@adhoc_query_model.id}$!rev")

        choose_style_tab

        uuid = get_uuid_from_table_field(0)

        select_h_select_option('.conditional-formatter-field-select', value: uuid)
        edit_rule(rule_number: 1, rule_value: 50, is_new_cf: true)
        expect_result(td_value: 50, expected_match_cells: 5)
      end

      it 'should works with multiple rules' do
        choose_style_tab
        select_h_select_option('.conditional-formatter-field-select', value: "#{@adhoc_query_model.id}$!rev")
        edit_rule(rule_number: 1, rule_value: 50, is_new_cf: true)
        select_h_select_option('.conditional-formatter-field-select', value: "#{@adhoc_query_model.id}$!rev")
        edit_rule(rule_number: 2, rule_value: 150, color_selector_nth: 2, is_new_cf: true)
        select_h_select_option('.conditional-formatter-field-select', value: "#{@adhoc_query_model.id}$!rev")
        edit_rule(rule_number: 3, rule_value: '23845621290950143', color_selector_nth: 3, is_new_cf: true)

        expect_result(td_value: 50, expected_match_cells: 5)
        expect_result(td_value: 150, expected_match_cells: 1, color: PREDEFINED_COLORS[1])
        expect_result(td_value: '23845621290950143', expected_match_cells: 1, color: PREDEFINED_COLORS[2])
      end
    end

    context 'pivot_table' do
      it 'should works with different measures' do
        safe_click('.ci-viz-type-pivot_table')
        safe_click('.btn-format')
        # sleep 100
        select_h_select_option('.pivot-section:nth-child(1) .ci-empty-field', value: "#{@adhoc_query_model.id}$!country")
        select_h_select_option('.pivot-section:nth-child(1) .ci-empty-field', value: "#{@adhoc_query_model.id}$!name")

        select_h_select_option('.pivot-section:nth-child(2) .ci-empty-field', value: "#{@adhoc_query_model.id}$!product")
        select_h_select_option('.pivot-section:nth-child(2) .ci-empty-field', value: "#{@adhoc_query_model.id}$!time")

        select_h_select_option('.pivot-section:nth-child(3) .ci-empty-field', value: "#{@adhoc_query_model.id}$!rev")
        select_h_select_option('.pivot-section:nth-child(3) .ci-empty-field', value: "#{@adhoc_query_model.id}$!rev")
        select_h_select_option('.pivot-section:nth-child(3) .ci-viz-field-select:nth-child(2)', value: 'count')
        choose_style_tab

        uuid_sum = get_uuid_from_pivot_field(0)
        select_h_select_option('.conditional-formatter-field-select', value: uuid_sum)
        edit_rule(rule_number: 1, rule_value: 50, is_new_cf: true)

        uuid_count = get_uuid_from_pivot_field(1)
        select_h_select_option('.conditional-formatter-field-select', value: uuid_count)
        edit_rule(rule_number: 2, rule_value: 4, color_selector_nth: 2, is_new_cf: true)

        select_h_select_option('.conditional-formatter-field-select', value: uuid_sum)
        edit_rule(rule_number: 3, rule_value: '23845621290950144', color_selector_nth: 3, is_new_cf: true)
        wait_for_all_ajax_requests

        expect_result(td_value: 50, expected_match_cells: 5)
        expect_result(td_value: '23845621290950144', expected_match_cells: 1, color: PREDEFINED_COLORS[2])
        expect_result(td_value: 4, expected_match_cells: 0, color: PREDEFINED_COLORS[1])
      end

      it 'should works with display empty cell as 0' do
        safe_click('.ci-viz-type-pivot_table')
        safe_click('.btn-format')

        select_h_select_option('.pivot-section:first-child .ci-empty-field', value: "#{@adhoc_query_model.id}$!country")
        select_h_select_option('.pivot-section:first-child .ci-empty-field', value: "#{@adhoc_query_model.id}$!name")

        select_h_select_option('.pivot-section:nth-child(2) .ci-empty-field', value: "#{@adhoc_query_model.id}$!product")
        select_h_select_option('.pivot-section:nth-child(2) .ci-empty-field', value: "#{@adhoc_query_model.id}$!time")

        select_h_select_option('.pivot-section:nth-child(3) .ci-empty-field', value: "#{@adhoc_query_model.id}$!rev")
        select_h_select_option('.pivot-section:nth-child(3) .ci-empty-field', value: "#{@adhoc_query_model.id}$!rev")
        select_h_select_option('.pivot-section:nth-child(3) .ci-viz-field-select:nth-child(2)', value: 'count')
        choose_style_tab
        page.find('[data-hui-comp="switch"]', text: 'Display empty cell as 0').click

        uuid = get_uuid_from_pivot_field(0)

        select_h_select_option('.conditional-formatter-field-select', value: uuid)
        edit_rule(rule_number: 1, rule_value: 0, is_new_cf: true)
        wait_for_all_ajax_requests

        expect_result(td_value: 0, expected_match_cells: 6)
      end
    end
  end

  context 'data set' do
    include_context 'data_set'

    let(:data_table_vs) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        source: data_set,
        fields: {
          table_fields: [
            {
              custom_label: nil,
              format: {
                format: {},
                type: 'timestamp',
              },
              path_hash: {
                field_name: 'created_at',
                model_id: 1,
              },
              type: 'datetime',
              uuid: '7413fed8-a107-410e-bd09-b8f6b84f735a',
            },
            {
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'id',
                model_id: 1,
              },
              type: 'number',
              uuid: 'a46e0059-5d29-432a-b00b-eaea0b7d849e',
            },
            {
              custom_label: nil,
              format: {
                format: {},
                type: 'string',
              },
              path_hash: {
                field_name: 'status',
                model_id: 1,
              },
              type: 'text',
              uuid: '418b4753-d0d0-48fe-ad89-0356fa165744',
            },
            {
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'price',
                model_id: 1,
              },
              type: 'number',
              uuid: '21cb9c8a-4155-45ec-a254-2eb0b6f4eb46',
            },
            {
              custom_label: nil,
              format: {
                format: {},
                type: 'string',
              },
              path_hash: {
                field_name: 'name',
                model_id: 1,
              },
              type: 'text',
              uuid: '40948ced-8b90-4594-b785-c12e55c19556',
            },
          ],
        },
        settings: {
          aggregation: {
            show_average: false,
            show_total: false,
          },
          conditional_formatting: [],
          misc: {},
          others: {
            include_empty_children_rows: false,
          },
          pop_settings: nil,
          quick_pivot: false,
          sort: {},
        },
        filters: [],
      )
    end

    let(:pivot_vs) do
      create(
        :viz_setting,
        viz_type: 'pivot_table',
        source: data_set,
        fields: {
          pivot_data: {
            columns: [
              {
                custom_label: nil,
                format: {
                  format: {
                    pattern: 'inherited',
                  },
                  type: 'number',
                },
                path_hash: {
                  field_name: 'id',
                  model_id: 1,
                },
                transformation: nil,
                type: 'number',
                uuid: '9ad18fb0-b135-45eb-933d-31c0b3f8e624',
              },
            ],
            rows: [
              {
                custom_label: nil,
                format: {
                  format: {
                    pattern: 'LLL yyyy',
                  },
                  type: 'date',
                },
                path_hash: {
                  field_name: 'created_at',
                  model_id: 1,
                },
                transformation: 'datetrunc month',
                type: 'datetime',
                uuid: '26dee886-6e45-4be1-990c-36affc18c8ed',
              },
            ],
            values: [
              {
                aggregation: 'sum',
                custom_label: nil,
                format: {
                  format: {
                    pattern: 'inherited',
                  },
                  type: 'number',
                },
                path_hash: {
                  field_name: 'price',
                  model_id: 1,
                },
                type: 'number',
                uuid: 'b2cf21a2-1ad5-4637-823b-7622686e207a',
              },
            ],
          },
        },
        settings: {
          conditional_formatting: [],
          misc: {},
          others: {
            column_total: true,
            include_empty_children_rows: false,
            row_total: true,
            sub_total: false,
          },
          pop_settings: nil,
          quick_pivot: false,
          sort: {},
        },
        filters: [],
      )
    end

    it 'works with data_table' do
      safe_login admin, "#{data_set_path(data_set)}/#{data_table_vs.hashid}"
      safe_click('.ci-explorer-control-get-results')
      choose_style_tab

      select_h_select_option('.conditional-formatter-field-select', value: '418b4753-d0d0-48fe-ad89-0356fa165744') # status
      edit_rule(rule_number: 1, rule_value: 'available', is_new_cf: true)

      safe_click('.ci-explorer-control-get-results')

      expect_result(td_value: 'available', expected_match_cells: 3)
    end

    it 'works with pivot_table' do
      safe_login admin, "#{data_set_path(data_set)}/#{pivot_vs.hashid}"
      safe_click('.ci-explorer-control-get-results')
      choose_style_tab

      select_h_select_option('.conditional-formatter-field-select', value: 'b2cf21a2-1ad5-4637-823b-7622686e207a') # sum of price
      edit_rule(rule_number: 1, rule_value: 2.25, is_new_cf: true)

      safe_click('.ci-explorer-control-get-results')

      wait_for_all_ajax_requests
      wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
      expect_result(td_value: 2.25, expected_match_cells: 2)
    end
  end

  context 'with AQL data set' do
    include_context 'data_explore_ctx'
    include_context 'aml_studio_dataset'

    let(:viz_setting_aql) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        source: aml_ecommerce_aql_data_set_record,
        fields: {
          table_fields: [
            {
              custom_label: nil,
              format: {
                sub_type: 'mmm yyyy',
                type: 'date',
              },
              path_hash: {
                field_name: 'created_at',
                model_id: 'data_modeling_orders',
              },
              transformation: 'datetrunc month',
              type: 'date',
              uuid: 'd0c387a6-210f-47e1-896a-bd95df9efe61',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: nil,
              format: {
                format: {
                  pattern: 'inherited',
                },
                type: 'number',
              },
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: '2fc79547-86dd-4a5a-9e3c-e00233b3b248',
            },
            {
              aggregation: 'sum',
              color: 'auto',
              custom_label: 'pot',
              analytic: {
                type: 'percentage',
                of_all: 'grand_total',
              },
              format: {},
              path_hash: {
                field_name: 'quantity',
                model_id: 'data_modeling_orders',
              },
              type: 'number',
              uuid: 'bc34a79b-eb67-43fb-8ead-8577bbffc864',
            },
          ],
        },
        settings: {},
        filters: [],
      )
    end

    before do
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global('data_models:explore_controls', true)
      FeatureToggle.toggle_global(DataModel::FT_AQL, true)

      DataSource.first.synchronize_schema
      connector = Connectors.from_ds(get_test_ds)

      insert_orders = <<~SQL
        truncate data_modeling.orders;
        insert into data_modeling.orders values
        (1, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-01-16 20:38:40'),
        (2, 1, 1, 10, 500, 'delivered', TIMESTAMP '2021-02-16 20:38:40'),
        (3, 1, 1, 20, 500, 'cancelled', TIMESTAMP '2021-02-16 20:38:40');
      SQL

      connector.exec_sql(insert_orders)
    end

    it 'can set and apply cf to advanced analytic field' do
      safe_login admin, "#{data_set_path(aml_ecommerce_aql_data_set_record)}/#{viz_setting_aql.hashid}"

      safe_click('.ci-explorer-control-get-results')
      choose_style_tab

      select_h_select_option('.conditional-formatter-field-select', value: 'bc34a79b-eb67-43fb-8ead-8577bbffc864') # pot field
      edit_rule(rule_number: 1, rule_value: 0.5, is_new_cf: true, rule_operator: 'less_than')

      safe_click('.ci-explorer-control-get-results')

      wait_for_all_ajax_requests
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      expect_result(td_value: 0.333, expected_match_cells: 1)
    end
  end

  context 'old conditional formatting spec' do
    it '#pivot_table' do
      viz_setting = FactoryBot.create(:viz_setting,
                                       viz_type: 'pivot_table',
                                       fields: {
                                         "pivot_data": {
                                           "rows": [
                                             { "type": 'text',
                                               "format": { "type": 'string', "sub_type": 'string' },
                                               "path_hash": { "field_name": 'country' },
                                               "custom_label": nil,
                                               "transformation": nil, },
                                             {
                                               "type": 'text',
                                               "format": { "type": 'string', "sub_type": 'string' },
                                               "path_hash": { "field_name": 'name' },
                                               "custom_label": nil,
                                               "transformation": nil,
                                             },
                                           ],
                                           "values": [
                                             {
                                               "type": 'number',
                                               "format": { "type": 'number', "sub_type": 'auto' },
                                               "path_hash": { "field_name": 'rev' },
                                               "aggregation": 'sum',
                                               "custom_label": nil,
                                             },
                                           ],
                                           "columns": [
                                             {
                                               "type": 'text',
                                               "format": {
                                                 "type": 'string',
                                                 "sub_type": 'string',
                                               },
                                               "path_hash": {
                                                 "field_name": 'product',
                                               },
                                               "custom_label": nil,
                                               "transformation": nil,
                                             },
                                             {
                                               "type": 'date',
                                               "format": {
                                                 "type": 'date',
                                                 "sub_type": 'mmm yyyy',
                                               },
                                               "path_hash": {
                                                 "field_name": 'time',
                                               },
                                               "custom_label": nil,
                                               "transformation": 'datetrunc month',
                                             },
                                           ],
                                         },
                                       },
                                       format: {
                                         "rev": { "type": 'number', "index": 4, "sub_type": 'auto' },
                                         "name": { "type": 'string', "index": 2, "sub_type": 'string' },
                                         "time": { "type": 'date', "index": 3, "sub_type": 'mmm dd yyyy' },
                                         "country": { "type": 'string', "index": 0, "sub_type": 'string' },
                                         "product": { "type": 'string', "index": 1, "sub_type": 'string' },
                                       },
                                       settings: {
                                         "misc": { "row_limit": nil, "pagination_size": 25, "show_row_number": true, "convert_nil_to_zero": false },
                                         "sort": {},
                                         "others": { "row_total": true, "sub_total": false, "column_total": true },
                                         "quick_pivot": false,
                                         "conditional_formatting": [
                                           {
                                             "mode": 'single',
                                             "name": 'rev',
                                             "type": 'number',
                                             "values": [
                                               '50',
                                             ],
                                             "modifier": nil,
                                             "operator": { "label": '=', "inputs": ['number'], "operator": 'equal', "modifiers": [] },
                                             "max_color": '#34BA5A',
                                             "mid_color": '#6FE562',
                                             "min_color": '#C3E8C2',
                                             "path_hash": {
                                               "field_name": 'rev',
                                             },
                                             "text_color": '#000000',
                                             "apply_to_row": false,
                                             "background_color": '#62ADD4',
                                           },
                                         ],
                                       },)
      report = FactoryBot.create(:pivot_table_report, query: query, viz_setting: viz_setting)

      safe_login(admin, query_report_path(report))

      wait_for_all_ajax_requests
      wait_for_element_load('.ci-table-report-data')

      expect_result(td_value: 50, expected_match_cells: 5, color: 'rgba(98, 173, 212, 1)')
    end

    it '#data_table' do
      viz_setting = FactoryBot.create(:viz_setting,
                                       "viz_type": 'data_table',
                                       "fields": {
                                         "table_fields": [],
                                       },
                                       "settings": {
                                         "misc": {
                                           "row_limit": nil,
                                           "pagination_size": 25,
                                           "show_row_number": true,
                                         },
                                         "sort": {},
                                         "aggregation": {
                                           "show_total": false,
                                           "show_average": false,
                                         },
                                         "quick_pivot": false,
                                         "conditional_formatting": [
                                           {
                                             "mode": 'single',
                                             "name": 'country',
                                             "type": 'text',
                                             "values": [
                                               'Viet Nam',
                                             ],
                                             "modifier": nil,
                                             "operator": {
                                               "label": 'is',
                                               "inputs": [
                                                 'text',
                                               ],
                                               "operator": 'is',
                                               "modifiers": [],
                                             },
                                             "max_color": '#34BA5A',
                                             "mid_color": '#6FE562',
                                             "min_color": '#C3E8C2',
                                             "path_hash": {
                                               "field_name": 'country',
                                             },
                                             "text_color": '#000000',
                                             "apply_to_row": false,
                                             "background_color": '#62ADD4',
                                           },
                                         ],
                                       },
                                       "format": {
                                         "rev": {
                                           "type": 'number',
                                           "index": 4,
                                           "sub_type": 'auto',
                                         },
                                         "name": {
                                           "type": 'string',
                                           "index": 2,
                                           "sub_type": 'string',
                                         },
                                         "time": {
                                           "type": 'date',
                                           "index": 3,
                                           "sub_type": 'mmm dd yyyy',
                                         },
                                         "country": {
                                           "type": 'string',
                                           "index": 0,
                                           "sub_type": 'string',
                                         },
                                         "product": {
                                           "type": 'string',
                                           "index": 1,
                                           "sub_type": 'string',
                                         },
                                       },
                                       "filters": [],
                                       "source_type": 'QueryReport',
                                       "source_id": 22,
                                       "adhoc_fields": [],)

      report = FactoryBot.create(:report_with_table_fields, query: query, viz_setting: viz_setting)

      safe_login(admin, query_report_path(report))

      wait_for_element_load('.ci-table-report-data')

      expect_result(td_value: 'Viet Nam', expected_match_cells: 8, color: 'rgba(98, 173, 212, 1)')
    end

    it 'percentage format' do
      viz_setting = FactoryBot.create(:viz_setting,
                                       "viz_type": 'data_table',
                                       "fields": {
                                         "table_fields": [
                                           {
                                             "path_hash": {
                                               "field_name": 'rev',
                                             },
                                             "custom_label": nil,
                                             "type": 'number',
                                             "format": {
                                               "type": 'number',
                                               "sub_type": '0.00%',
                                             },
                                           },
                                         ],
                                       },
                                       "settings": {
                                         "misc": {
                                           "row_limit": nil,
                                           "pagination_size": 25,
                                           "show_row_number": true,
                                         },
                                         "sort": {},
                                         "aggregation": {
                                           "show_total": false,
                                           "show_average": false,
                                         },
                                         "quick_pivot": false,
                                         "conditional_formatting": [
                                           {
                                             "path_hash": {
                                               "field_name": 'rev',
                                             },
                                             "name": 'rev',
                                             "type": 'number',
                                             "model": {
                                               "name": '"cache"."t10062"',
                                               "backend_type": 'PgcacheModel',
                                             },
                                             "aggregation": 'sum',
                                             "operator": {
                                               "operator": 'equal',
                                               "label": '=',
                                               "inputs": [
                                                 'number',
                                               ],
                                               "modifiers": [],
                                             },
                                             "values": [
                                               '50',
                                             ],
                                             "text_color": '#000000',
                                             "background_color": '#62ADD4',
                                             "min_color": '#C3E8C2',
                                             "mid_color": '#6FE562',
                                             "max_color": '#34BA5A',
                                             "mode": 'single',
                                             "apply_to_row": false,
                                             "modifier": nil,
                                           },
                                         ],
                                       },
                                       "format": {
                                         "rev": {
                                           "type": 'number',
                                           "index": 4,
                                           "sub_type": '0.00%',
                                         },
                                         "name": {
                                           "type": 'string',
                                           "index": 2,
                                           "sub_type": 'string',
                                         },
                                         "time": {
                                           "type": 'date',
                                           "index": 3,
                                           "sub_type": 'mmm dd yyyy',
                                         },
                                         "country": {
                                           "type": 'string',
                                           "index": 0,
                                           "sub_type": 'string',
                                         },
                                         "product": {
                                           "type": 'string',
                                           "index": 1,
                                           "sub_type": 'string',
                                         },
                                       },
                                       "filters": [],
                                       "source_type": 'QueryReport',
                                       "source_id": 22,
                                       "adhoc_fields": [],)

      report = FactoryBot.create(:report_with_table_fields, query: query, viz_setting: viz_setting)

      safe_login(admin, query_report_path(report))

      wait_for_element_load('.ci-table-report-data')

      expect_result(td_value: '5000.00%', expected_match_cells: 5, color: 'rgba(98, 173, 212, 1)')
    end

    it 'while editing sql report, path_hash contain field_name and model_id' do
      viz_setting = FactoryBot.create(:viz_setting,
                                       "viz_type": 'data_table',
                                       "fields": {
                                         "table_fields": [],
                                       },
                                       "settings": {
                                         "misc": {
                                           "row_limit": nil,
                                           "pagination_size": 25,
                                           "show_row_number": true,
                                         },
                                         "sort": {},
                                         "aggregation": {
                                           "show_total": false,
                                           "show_average": false,
                                         },
                                         "quick_pivot": false,
                                         "conditional_formatting": [
                                           {
                                             "mode": 'single',
                                             "name": 'country',
                                             "type": 'text',
                                             "values": [
                                               'Viet Nam',
                                             ],
                                             "modifier": nil,
                                             "operator": {
                                               "label": 'is',
                                               "inputs": [
                                                 'text',
                                               ],
                                               "operator": 'is',
                                               "modifiers": [],
                                             },
                                             "max_color": '#34BA5A',
                                             "mid_color": '#6FE562',
                                             "min_color": '#C3E8C2',
                                             "path_hash": {
                                               "field_name": 'country',
                                               "model_id": 1,
                                             },
                                             "text_color": '#000000',
                                             "apply_to_row": false,
                                             "background_color": '#62ADD4',
                                           },
                                         ],
                                       },
                                       "format": {
                                         "rev": {
                                           "type": 'number',
                                           "index": 4,
                                           "sub_type": 'auto',
                                         },
                                         "name": {
                                           "type": 'string',
                                           "index": 2,
                                           "sub_type": 'string',
                                         },
                                         "time": {
                                           "type": 'date',
                                           "index": 3,
                                           "sub_type": 'mmm dd yyyy',
                                         },
                                         "country": {
                                           "type": 'string',
                                           "index": 0,
                                           "sub_type": 'string',
                                         },
                                         "product": {
                                           "type": 'string',
                                           "index": 1,
                                           "sub_type": 'string',
                                         },
                                       },
                                       "filters": [],
                                       "source_type": 'QueryReport',
                                       "source_id": 22,
                                       "adhoc_fields": [],)

      report = FactoryBot.create(:report_with_table_fields, query: query, viz_setting: viz_setting)

      safe_login(admin, query_report_path(report))

      safe_click '.ci-edit-report-link'

      safe_click '.ci-btn-run'

      choose_style_tab

      edit_rule(rule_number: 1, rule_value: 'Singapore')

      wait_for_all_ajax_requests
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      expect_result(td_value: 'Singapore', expected_match_cells: 6, color: PREDEFINED_COLORS[0])
    end

    it 'while editing sql report, path_hash contain field_name only' do
      viz_setting = FactoryBot.create(:viz_setting,
                                       "viz_type": 'data_table',
                                       "fields": {
                                         "table_fields": [],
                                       },
                                       "settings": {
                                         "misc": {
                                           "row_limit": nil,
                                           "pagination_size": 25,
                                           "show_row_number": true,
                                         },
                                         "sort": {},
                                         "aggregation": {
                                           "show_total": false,
                                           "show_average": false,
                                         },
                                         "quick_pivot": false,
                                         "conditional_formatting": [
                                           {
                                             "mode": 'single',
                                             "name": 'country',
                                             "type": 'text',
                                             "values": [
                                               'Viet Nam',
                                             ],
                                             "modifier": nil,
                                             "operator": {
                                               "label": 'is',
                                               "inputs": [
                                                 'text',
                                               ],
                                               "operator": 'is',
                                               "modifiers": [],
                                             },
                                             "max_color": '#34BA5A',
                                             "mid_color": '#6FE562',
                                             "min_color": '#C3E8C2',
                                             "path_hash": {
                                               "field_name": 'country',
                                             },
                                             "text_color": '#000000',
                                             "apply_to_row": false,
                                             "background_color": '#62ADD4',
                                           },
                                         ],
                                       },
                                       "format": {
                                         "rev": {
                                           "type": 'number',
                                           "index": 4,
                                           "sub_type": 'auto',
                                         },
                                         "name": {
                                           "type": 'string',
                                           "index": 2,
                                           "sub_type": 'string',
                                         },
                                         "time": {
                                           "type": 'date',
                                           "index": 3,
                                           "sub_type": 'mmm dd yyyy',
                                         },
                                         "country": {
                                           "type": 'string',
                                           "index": 0,
                                           "sub_type": 'string',
                                         },
                                         "product": {
                                           "type": 'string',
                                           "index": 1,
                                           "sub_type": 'string',
                                         },
                                       },
                                       "filters": [],
                                       "source_type": 'QueryReport',
                                       "source_id": 22,
                                       "adhoc_fields": [],)

      report = FactoryBot.create(:report_with_table_fields, query: query, viz_setting: viz_setting)

      safe_login(admin, query_report_path(report))

      safe_click '.ci-edit-report-link'

      safe_click '.ci-btn-run'

      choose_style_tab

      edit_rule(rule_number: 1, rule_value: 'Singapore')

      wait_for_all_ajax_requests
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      expect_result(td_value: 'Singapore', expected_match_cells: 6, color: PREDEFINED_COLORS[0])
    end

    it 'while editing sql report, cf.model is not null' do
      viz_setting = FactoryBot.create(:viz_setting,
                                       "viz_type": 'data_table',
                                       "fields": {
                                         "table_fields": [],
                                       },
                                       "settings": {
                                         "misc": {
                                           "row_limit": nil,
                                           "pagination_size": 25,
                                           "show_row_number": true,
                                         },
                                         "sort": {},
                                         "aggregation": {
                                           "show_total": false,
                                           "show_average": false,
                                         },
                                         "quick_pivot": false,
                                         "conditional_formatting": [
                                           {
                                             "mode": 'single',
                                             "name": 'country',
                                             "type": 'text',
                                             "values": [
                                               'Viet Nam',
                                             ],
                                             "modifier": nil,
                                             "operator": {
                                               "label": 'is',
                                               "inputs": [
                                                 'text',
                                               ],
                                               "operator": 'is',
                                               "modifiers": [],
                                             },
                                             "max_color": '#34BA5A',
                                             "mid_color": '#6FE562',
                                             "min_color": '#C3E8C2',
                                             "path_hash": {
                                               "field_name": 'country',
                                               "model_id": 100,
                                             },
                                             "model": {
                                               "backend_type": 'PgcacheModel',
                                               "name": 'cache',
                                             },
                                             "text_color": '#000000',
                                             "apply_to_row": false,
                                             "background_color": '#62ADD4',
                                           },
                                         ],
                                       },
                                       "format": {
                                         "rev": {
                                           "type": 'number',
                                           "index": 4,
                                           "sub_type": 'auto',
                                         },
                                         "name": {
                                           "type": 'string',
                                           "index": 2,
                                           "sub_type": 'string',
                                         },
                                         "time": {
                                           "type": 'date',
                                           "index": 3,
                                           "sub_type": 'mmm dd yyyy',
                                         },
                                         "country": {
                                           "type": 'string',
                                           "index": 0,
                                           "sub_type": 'string',
                                         },
                                         "product": {
                                           "type": 'string',
                                           "index": 1,
                                           "sub_type": 'string',
                                         },
                                       },
                                       "filters": [],
                                       "source_type": 'QueryReport',
                                       "source_id": 22,
                                       "adhoc_fields": [],)

      report = FactoryBot.create(:report_with_table_fields, query: query, viz_setting: viz_setting)

      safe_login(admin, query_report_path(report))

      safe_click '.ci-edit-report-link'

      safe_click '.ci-btn-run'

      choose_style_tab

      edit_rule(rule_number: 1, rule_value: 'Singapore')

      wait_for_all_ajax_requests
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      expect_result(td_value: 'Singapore', expected_match_cells: 6, color: PREDEFINED_COLORS[0])
    end
  end
end
