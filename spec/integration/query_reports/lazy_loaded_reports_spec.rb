# typed: false
require 'rails_helper'

describe 'Lazy loaded reports', js: true, stable: true do
  before do
    @cat = FactoryBot.create :report_category, name: 'Taxi'
    sql = <<-SQL.strip_heredoc
        select generate_series(1, 25) as num;
    SQL
    settings = {
      paging_page_size: 10,
      disable_auto_load: true
    }

    @report = FactoryBot.create :query_report, title: 'Lazy-loaded report',
                                                query: sql,
                                                settings: settings,
                                                category_id: @cat.id
  end
  before do
    @cat = FactoryBot.create :report_category, name: 'Taxi'
    sql = <<-SQL.strip_heredoc
        select generate_series(1, 25) as num;
    SQL
    settings = {
      paging_page_size: 10,
      disable_auto_load: true
    }

    @report = FactoryBot.create :query_report, title: 'Lazy-loaded report',
                                                query: sql,
                                                settings: settings,
                                                category_id: @cat.id
  end

  PREVIEW_TABLE_CLASS = '.columns-preview-container .columns-preview-table'

  TABLE_REPORT_DATA_CLASS = '.ci-table-report-data'

  it 'should only load report data when Load Report Data button is clicked' do
    safe_login :admin, "/queries/#{@report.to_param}"
    wait_expect(true) { page.has_no_css?(TABLE_REPORT_DATA_CLASS) }

    safe_click('.ci-load-report-data-button')
    wait_expect(true) { page.has_css?(TABLE_REPORT_DATA_CLASS) }
  end

  context 'Report already columns metadata information' do
    before do
      @report.settings[:column_options] = [
        { name: 'column_name_1' },
        { name: 'column_name_2' }
      ]
      @report.save
    end

    it 'shows report with existing column preview' do
      safe_login :admin, "/queries/#{@report.to_param}"
      wait_expect(true) { page.has_css?(PREVIEW_TABLE_CLASS) }
      wait_expect(['column_name_1', 'column_name_2']) {
        page.all("#{PREVIEW_TABLE_CLASS} th").map(&:text)
      }
    end
  end

  context 'Report not yet previewed' do
    it 'shows report without column preview' do
      safe_login :admin, "/queries/#{@report.to_param}"
      wait_expect(true) { page.has_no_css?(PREVIEW_TABLE_CLASS) }
    end
  end
end
