# typed: false
require 'rails_helper'

describe 'create query templates descriptions', js: true, legacy: true do
  let(:admin) { get_test_admin }
  let(:report) { FactoryBot.create :query_report }
  let!(:query_template) { FactoryBot.create :query_template, name: 'test_template' }
  let(:new_template) do
    {
      name: "editedqr",
      description: "Edited description.",
      content: "select 1"
    }
  end

  it 'should return exactly only one query template' do
    safe_login(:admin, '/query_templates')
    wait_expect(1) { page.all('.ci-table-templates tbody tr').length }
  end

  def edit_query_template(qr)
    page.find('.ci-table-templates .ci-qr-template-name').click
    wait_for_element_load('.ci-new-qr-template')
    page.find('.ci-tp-name').set(qr[:name])
    page.find('.ci-edit-desc').set(qr[:description])
    page.find('.ace_content').click
    fill_in_ace '', qr[:content]
    page.find('.ci-save-qr-template').click
    expect_notifier_content('query template saved!')
  end

  it 'should return exactly only one query template edited' do
    safe_login(:admin, '/query_templates')
    edit_query_template(new_template)
    wait_expect(1) { page.all('.ci-table-templates tbody tr').length }
  end

  it 'should render used_in' do
    safe_login(:admin, query_report_path(report))
    wait_for_element_load('.ci-toggle')
    page.find('.ci-toggle').click
    page.find('.ci-edit-report-link').click
    safe_click('.query-template-dropdown')
    safe_click('.hui-dropdown-floating .ci-name')
    page.all('.ci-save').last.click
    expect_notifier_content('saved successfully')
    visit '/query_templates'
    wait_for_element_load('.ci-table-templates')
    sleep 0.5
    wait_expect('1 reports') { page.find('.ci-used-reports').text }
  end
end
