# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting metric sheet values', js: true, stable: true do
  let(:admin) { users(:admin) }

  def wait_for_viz_setting_load
    wait_for_element_load('.viz-setting-form .viz-type-select-wrapper', 10)
  end

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_viz_setting_load
      safe_click('.ci-viz-type-metric_sheet')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-empty-field', value: "#{orders_model.id}$!created_at"
      select_h_select_option '.viz-section:nth-child(2) .ci-empty-field', value: "#{orders_model.id}$!quantity"
      select_h_select_option '.viz-section:nth-child(3) .ci-empty-field', group: 'Other fields', value: "#{orders_model.id}$!created_at"

      select_h_select_option('.ci-operator-select', value: 'between')
      date_picker = page.find('.ci-date-picker-input')
      date_picker.send_keys('2021-01-16 - 2021-04-16')
      safe_click('.ci-explorer-control-get-results')
      wait_for_element_load('.ci-table-report-data [data-ci="ci-ag-grid-metric-sheet"]')
      page.first('.viz-section-header').click
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_metric_sheet_values(['Sum of Quantity', '', '0.50K', '0.50K', '0.50K', '0.60K'])
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_metric_sheet_values(['Sum of Quantity', '', '500', '500', '500', '604'])

      select_h_select_option('.format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      expect_metric_sheet_values(['Sum of Quantity', '', '0.50K', '0.50K', '0.50K', '0.60K'])
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_viz_setting_load
      safe_click('.ci-viz-type-metric_sheet')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-empty-field', value: "#{orders_model.id}$!created_at"
      select_h_select_option '.viz-section:nth-child(2) .ci-empty-field', value: "#{orders_model.id}$!quantity"
      select_h_select_option '.viz-section:nth-child(3) .ci-empty-field', group: 'Other fields', value: "#{orders_model.id}$!created_at"

      select_h_select_option('.ci-operator-select', value: 'between')
      date_picker = page.find('.ci-date-picker-input')
      date_picker.send_keys('2021-01-16 - 2021-04-16')
      sleep 0.5
      safe_click('.ci-explorer-control-get-results')
      wait_for_element_load('.ci-table-report-data [data-ci="ci-ag-grid-metric-sheet"]')
      page.first('.viz-section-header').click
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_metric_sheet_values(['Sum of Quantity', '', '500', '500', '500', '604'])
    end
  end
end

def expect_metric_sheet_values(values)
  # show more data by collapsing panel
  if page.has_css?('.ci-collapse-panel')
    safe_click('.ci-collapse-panel')
  end
  if page.has_css?('.btn-toggle.left')
    page.first('.btn-toggle.left').click
  end

  wait_for_element_load('[data-ci="ci-ag-grid-metric-sheet"]')
  wait_expect(values) do
    page.all('[data-ci="ci-ag-grid-metric-sheet"] .ag-cell').map(&:text)
  end
end
