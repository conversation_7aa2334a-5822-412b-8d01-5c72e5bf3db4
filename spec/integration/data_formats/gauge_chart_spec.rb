# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting gauge chart values', js: true, stable: true do
  let(:admin) { users(:admin) }

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_element_load('.ci-viz-field-select')
      safe_click('.ci-viz-type-solid_gauge')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_value_and_max_value('60.0', '2.10K')
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_value_and_max_value('60', '2,104')

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      wait_for_submit_generate
      expect_value_and_max_value('60.0', '2.10K')
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-solid_gauge')
      page.find('span', text: 'Visualizations').click # close popover
      wait_for_element_load('.ci-viz-field-select')
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
      safe_click('.ci-explorer-control-get-results')
    end

    it 'should display values correctly' do
      expect_value_and_max_value('60', '2104')
    end
  end
end

def expect_value_and_max_value(value, max_value)
  wait_for_element_load('.highcharts-data-label')
  highchart_value = page.find('.highcharts-data-label > span').text
  expect(highchart_value).to eq(value)

  highchart_max_value = page.all('.highcharts-yaxis-labels > text')
                            .last
                            .text
  expect(highchart_max_value).to eq(max_value)
end
