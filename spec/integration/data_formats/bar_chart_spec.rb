# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting bar chart values', js: true, stable: true do
  let(:admin) { users(:admin) }

  def wait_for_viz_setting_load
    wait_for_element_load('.viz-setting-form .viz-type-select-wrapper', 10)
  end

  def wait_for_x_axis_load
    wait_for_element_load('.ci-x-axis-row .ci-viz-field-select', 10)
  end

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_viz_setting_load
      safe_click('.ci-viz-type-bar_chart')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
      select_h_select_option '.viz-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_highcharts_tooltip("$4 20.0 Sum of Quantity: 0.00K")
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(3) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_highcharts_tooltip("4 20 Sum of Quantity: 2")

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(3) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      wait_for_submit_generate
      expect_highcharts_tooltip("$4 20.0 Sum of Quantity: 0.00K")
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_viz_setting_load
      safe_click('.ci-viz-type-bar_chart')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
      select_h_select_option '.viz-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_highcharts_tooltip("4 20 Sum of Quantity: 2")
    end
  end
end
