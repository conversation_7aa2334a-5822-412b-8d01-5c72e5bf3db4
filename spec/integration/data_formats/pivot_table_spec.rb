# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting pivot table values', js: true do
  include_context 'test_tenant'

  def wait_for_viz_setting_load
    wait_for_element_load('.viz-setting-form .viz-type-select-wrapper', 10)
  end

  def wait_for_field_select_load
    wait_for_element_load('.ci-viz-field .ci-viz-field-select', 10)
  end

  def wait_for_pivot_table_load
    wait_for_viz_load
    wait_for_element_load('.viz-data-table .pivot-table-wrapper', 10)
  end

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_viz_setting_load
      safe_click('.ci-viz-type-pivot_table')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.pivot-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.pivot-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!created_at"
      select_h_select_option '.pivot-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      wait_for_pivot_table_load
      expected_values = ['$2', '', '0.50K', '', '', '0.50K']
      expected_totals = ['Total', '0.50K', '0.50K', '0.50K', '0.60K', '2.10K']
      expect_values_totals(expected_values, expected_totals)
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(3) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      wait_for_submit_generate
      expected_values = ['2', '', '500', '', '', '500']
      expected_totals = ['Total', '500', '500', '500', '604', '2,104']
      expect_values_totals(expected_values, expected_totals)

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(3) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      wait_for_submit_generate
      expected_values = ['$2', '', '0.50K', '', '', '0.50K']
      expected_totals = ['Total', '0.50K', '0.50K', '0.50K', '0.60K', '2.10K']
      expect_values_totals(expected_values, expected_totals)
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_viz_setting_load
      safe_click('.ci-viz-type-pivot_table')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.pivot-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.pivot-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!created_at"
      select_h_select_option '.pivot-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      expected_values = ['2', '', '500', '', '', '500']
      expected_totals = ['Total', '500', '500', '500', '604', '2104']
      expect_values_totals(expected_values, expected_totals)
    end
  end
end

def expect_values_totals(values, totals)
  all_row_fields = page.all('.ci-table-report-data .ag-pinned-left-cols-container .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }
  all_value_fields = page.all('.ci-table-report-data .ag-viewport .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }

  wait_expect(values) do
    [*all_row_fields[1], *all_value_fields[1]]
  end
  wait_expect(totals) do
    page.all('.ag-floating-bottom .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }.flatten
  end
end
