# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting custom modeling measures', js: true, stable: true do
  include_context 'data_set'
  let(:admin) { users(:admin) }

  context 'are having predefined format' do
    before do
      FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
      FeatureToggle.toggle_global('data_models:aml', true)

      products_model.external_fields << DataModeling::Explores::Measure.new(
        name: 'aml',
        label: 'AML',
        sql: "count(#{products_model.name}.id)",
        syntax: 'aml',
        type: 'number',
        format: { pattern: '#,###0.0' },
        description: '',
        aggregation_type: 'custom',
        is_external: true,
      )
      products_model.external_fields << DataModeling::Explores::Measure.new(
        name: 'sql',
        label: 'SQL',
        sql: 'count({{ #THIS.id }})',
        syntax: 'sql',
        type: 'number',
        format: { pattern: '#,###0.00' },
        description: '',
        aggregation_type: 'custom',
        is_external: true,
      )
      products_model.save!
      get_test_ds.reload
    end

    it 'should display formatted values correctly' do
      safe_login(admin, "/data_models?ds=#{get_test_ds.id}&model=#{products_model.id}")
      wait_for_element_load('.ci-model-details')
      safe_click('.ci-tab-toggle', text: 'Preview')
      select_h_select_option '.ci-viz-field-select .ci-empty-field', value: "#{products_model.id}$!aml"
      select_h_select_option '.ci-viz-field-select .ci-empty-field', value: "#{products_model.id}$!sql"
      safe_click('.ci-explorer-control-get-results')
      wait_for_element_load('.ci-table-report-data')

      wait_expect(['4.0', '4.00'].sort) do
        page.all('.ci-table-report-data .ag-cell:not(.ag-column-first)').map(&:text).sort
      end
    end
  end
end
