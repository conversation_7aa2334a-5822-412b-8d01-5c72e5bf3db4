# frozen_string_literal: true

# typed: false
require 'rails_helper'

describe 'formatting conversion funnel chart', js: true, stable: true do
  let(:admin) { users(:admin) }

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-new_conversion_funnel')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      expect_cf_tooltip_and_data_label('2.10K', '2.10K')
      expect_table_data(['$1', '0.50K (100%)'])
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      expect_cf_tooltip_and_data_label('2,104', '2,104')
      expect_table_data(['1', '500 (100%)'])

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      wait_for_all_holistics_loadings
      wait_for_viz_load
      expect_cf_tooltip_and_data_label('2.10K', '2.10K')
      expect_table_data(['$1', '0.50K (100%)'])
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-new_conversion_funnel')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      expect_cf_tooltip_and_data_label('2104', '2104')
      expect_table_data(['1', '500 (100%)'])
    end
  end
end

def expect_cf_tooltip_and_data_label(data_label, tooltip_value)
  highcharts_data_label = page.all('.highcharts-data-label tspan', exact_text: data_label)
                              .first
  expect(highcharts_data_label.text).to eq(data_label)
  reliable_hover('.highcharts-series', '.highcharts-tooltip text')
  tooltip = page.find('.highcharts-tooltip text').text
  expect(tooltip).to include(tooltip_value)
end

def expect_table_data(values)
  wait_expect(values) do
    page.all('table tbody tr:nth-child(2) > td')
        .map(&:text)
  end
end
