# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting data table values', js: true do
  include_context 'test_tenant'

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_element_load('.ci-viz-field-select')
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
    end

    it 'should update default format accordingly to type' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      expect_type_picker_value('Number')
      expect_format_picker_value('Inherited from Modeling')
      page.find('.ci-tab-toggle', exact_text: 'Settings')
          .click
      page.all('.ci-viz-field-select .h-icon[data-icon="cancel"]').last.click
      select_h_select_option '.viz-section:nth-child(1) .ci-empty-field', value: "#{orders_model.id}$!created_at"
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      expect_type_picker_value('Timestamp')
      select_h_select_option '.type-picker', label: 'Date'
      expect_type_picker_value('Date')
      expect_format_picker_value('Default')
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_table_values(['$1', '$2', '$3', '$4', '$5', '$6'])
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_table_values(%w[1 2 3 4 5 6])
      page.find('.format-picker.hui-select-trigger').click
      expect(page.find('.hui-select-floating.viz-column-format-picker-popover').rect.width).to eq 350

      select_h_select_option('.format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      expect_table_values(['$1', '$2', '$3', '$4', '$5', '$6'])
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_element_load('.viz-section:nth-child(1) .ci-viz-field-select')
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      safe_click('.ci-explorer-control-get-results')
    end

    it 'should display values correctly' do
      expect_table_values(['1', '2', '3', '4', '5', '6'])
    end
  end
end

def expect_table_values(values)
  wait_for_all_ajax_requests
  wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
  wait_expect(values.sort) do
    page.all('[data-ci="ci-ag-grid-data-table"] .ag-row').map{ |row| row.all('.ag-column-last').map(&:text) }.flatten.sort
  end
end

def expect_type_picker_value(expected)
  type_picker_value = page.find('.type-picker.hui-select-trigger')
  expect(type_picker_value.text).to eq(expected)
end

def expect_format_picker_value(expected)
  format_picker_value = page.find('.format-picker.hui-select-trigger')
  expect(format_picker_value.text).to eq(expected)
end
