# frozen_string_literal: true

# typed: false
require 'rails_helper'

describe 'formatting radar chart', js: true, stable: true do
  let(:admin) { users(:admin) }

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-radar_chart')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_highcharts_tooltip("$4 ● Sum of Quantity: 0.00K")
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_highcharts_tooltip("4 ● Sum of Quantity: 2")

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      wait_for_submit_generate
      expect_highcharts_tooltip("$4 ● Sum of Quantity: 0.00K")
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-radar_chart')
      page.find('span', text: 'Visualizations').click # close popover
      safe_click('.ci-viz-type-select .collapse-toggler')
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_highcharts_tooltip("4 ● Sum of Quantity: 2")
    end
  end
end
