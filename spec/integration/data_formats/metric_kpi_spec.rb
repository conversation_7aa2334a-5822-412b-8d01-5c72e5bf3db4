# frozen_string_literal: true

require 'rails_helper'

describe 'formatting metric kpi', js: true, stable: true do
  let(:admin) { users(:admin) }
  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    context 'display value is abbreviated' do
      before do
        safe_login(admin, "/datasets/#{data_set.id}")
        safe_click('.ci-viz-type-metric_kpi')
        page.find('span', text: 'Visualizations').click # close popover
        select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
        select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
        page.find('.ci-tab-toggle', exact_text: 'Styles')
            .click
        page.find('.h-radio', text: 'Compare By Number')
            .click
      end

      it 'should display values correctly' do
        safe_click('.ci-explorer-control-get-results')
        expect_display_value_and_tooltip('2.10K', '2.10K')
        expect_comparison_details('2.10K', '60.0', '2.04K')
      end

      it 'should re-format values correctly' do
        page.find('.ci-tab-toggle', exact_text: 'Format')
            .click
        select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
        select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
        safe_click('.ci-explorer-control-get-results')
        expect_display_value_and_tooltip('2,104', '2,104')
        expect_comparison_details('2,104', '60', '2,044')

        select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
        select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
        safe_click('.ci-explorer-control-get-results')
        wait_for_submit_generate
        expect_display_value_and_tooltip('2.10K', '2.10K')
        expect_comparison_details('2.10K', '60.0', '2.04K')
      end
    end

    context 'display value is NOT abbreviated' do
      before do
        safe_login(admin, "/datasets/#{data_set.id}")
        safe_click('.ci-viz-type-metric_kpi')
        page.find('span', text: 'Visualizations').click # close popover
        select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
        select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
        page.find('.ci-tab-toggle', exact_text: 'Styles')
            .click
        page.find('.h-radio', text: 'Compare By Number')
            .click
      end

      it 'should display values correctly' do
        safe_click('.ci-explorer-control-get-results')
        expect_display_value_and_tooltip('60.0', '60.0')
        expect_comparison_details('60.0', '2.10K', '2,044.0')
      end

      it 'should re-format values correctly' do
        page.find('.ci-tab-toggle', exact_text: 'Format')
            .click
        select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
        select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
        safe_click('.ci-explorer-control-get-results')
        expect_display_value_and_tooltip('60', '60')
        expect_comparison_details('60', '2,104', '2,044')
      end
    end
  end

  context 'display value is abbreviated, others are default formatted' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-metric_kpi')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!discount"
      page.find('.ci-tab-toggle', exact_text: 'Styles')
          .click
      page.find('.h-radio', text: 'Compare By Number')
          .click
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      expect_display_value_and_tooltip('2.10K', '2104')
      expect_comparison_details('2104', '60', '2.04K')
    end
  end

  context 'dashboard v3 should not have new setting' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-metric_kpi')
      page.find('.ci-tab-toggle', exact_text: 'Styles').click
    end

    it 'should not display the alignment setting' do
      expect(page).not_to have_selector('.ci-multi-mode-switch-row .viz-field-label .label-wrapper > :first-child', text: 'Alignment')
    end

    it 'should not display the new comparison setting section' do
      expect(page).not_to have_selector('viz-section-header-label', text: 'Comparison Type')
    end
  end
end

def expect_display_value_and_tooltip(value, tooltip_vlaue)
  wait_for_element_load('.ci-friendly-number')
  display_value = page.find('.ci-friendly-number')
  expect(display_value.text).to eq(value)

  display_value.hover
  expect(page.find('.friendly-number-tooltip').text).to eq(tooltip_vlaue)
end

def expect_comparison_details(value, comp_value, diff)
  wait_for_element_load('.ci-friendly-diff')
  page.find('.ci-export-btn')
      .hover
  display_value = page.find('.ci-friendly-diff')
  expect(display_value.text).to eq(diff)
  display_value.hover
  expect(page.find('.hui-tooltip-floating.friendly-diff-tooltip').text).to include(value)
  expect(page.find('.hui-tooltip-floating.friendly-diff-tooltip').text).to include(comp_value)
end
