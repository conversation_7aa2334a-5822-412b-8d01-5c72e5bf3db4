# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting word cloud values', js: true, stable: true do
  let(:admin) { users(:admin) }

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_element_load('.ci-viz-field-select')
      safe_click('.ci-viz-type-wordcloud')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_value_and_tooltip('$5', '$5 ● Sum of quantity: 0.60K')
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_value_and_tooltip('5', '5 ● Sum of quantity: 600')

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      wait_for_submit_generate
      expect_value_and_tooltip('$5', '$5 ● Sum of quantity: 0.60K')
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-wordcloud')
      page.find('span', text: 'Visualizations').click # close popover
      wait_for_element_load('.ci-viz-field-select')
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
      safe_click('.ci-explorer-control-get-results')
    end

    it 'should display values correctly' do
      expect_value_and_tooltip('5', '5 ● Sum of quantity: 600')
    end
  end
end

def expect_value_and_tooltip(value, tooltip_value)
  wait_for_element_load('.highcharts-wordcloud-series')
  word_cloud = page.all('.highcharts-wordcloud-series text')
                   .first
  expect(word_cloud.text).to eq(value)

  word_cloud.hover
  tooltip = page.find('.highcharts-tooltip')
  expect(tooltip.text).to eq(tooltip_value)
end
