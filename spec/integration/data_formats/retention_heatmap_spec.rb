# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Formatting retention heatmap values', js: true, stable: true do
  let(:admin) { users(:admin) }

  context 'values are pre-formatted' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-retention_heatmap')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!created_at"
      select_h_select_option '.viz-section:nth-child(4) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_values(['$1', '$1', '50000%', '', '', ''])
      safe_click('.ci-show-raw-values')
      expect_values(['$1', '$1', '0.50K', '', '', ''])
    end

    it 'should re-format values correctly' do
      page.find('.ci-tab-toggle', exact_text: 'Format')
          .click
      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Number (Rounded)')
      select_h_select_option('.viz-section:nth-child(4) .format-picker', label: 'Number (Rounded)')
      safe_click('.ci-explorer-control-get-results')
      expect_values(['1', '1', '50000%', '', '', ''])
      safe_click('.ci-show-raw-values')
      expect_values(['1', '1', '500', '', '', ''])

      select_h_select_option('.viz-section:nth-child(1) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(2) .format-picker', label: 'Inherited from Modeling')
      select_h_select_option('.viz-section:nth-child(4) .format-picker', label: 'Inherited from Modeling')
      safe_click('.ci-explorer-control-get-results')
      expect_values(['$1', '$1', '0.50K', '', '', ''])
    end
  end

  context 'values are pre-formatted by default viz setting format' do
    include_context 'format is not defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      safe_click('.ci-viz-type-retention_heatmap')
      page.find('span', text: 'Visualizations').click # close popover
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(2) .ci-viz-field-select', value: "#{orders_model.id}$!id"
      select_h_select_option '.viz-section:nth-child(3) .ci-viz-field-select', value: "#{orders_model.id}$!created_at"
      select_h_select_option '.viz-section:nth-child(4) .ci-viz-field-select', value: "#{orders_model.id}$!quantity"
    end

    it 'should display values correctly' do
      safe_click('.ci-explorer-control-get-results')
      expect_values(['1', '1', '50000%', '', '', '',])
      safe_click('.ci-show-raw-values')
      expect_values(['1', '1', '500', '', '', '',])
    end
  end
end

def expect_values(values)
  wait_expect(values) do
    page.all('[data-ci="ci-ag-grid-cohort-retention"] .ag-row-first .ag-cell')
        .map(&:text)
  end
end
