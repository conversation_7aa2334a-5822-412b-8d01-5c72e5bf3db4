# typed: ignore

require 'rails_helper'
require './spec/services/viz/exporting/writers/dummy_dest'

describe 'Google spreadsheet schedule (v3.0)', js: true, stable: true do
  include_context 'query_model_dataset_based_report'

  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)

    setup_mock_sheet
  end

  let(:tenant) { get_test_tenant }
  let(:ds) { get_test_ds }
  let(:test_user) { get_test_admin }

  let(:dashboard) { FactoryBot.create(:dashboard, tenant: tenant, version: 3) }
  let!(:query_report) { query_model_dataset_based_report }
  let!(:widget) { FactoryBot.create(:dashboard_widget, dashboard: dashboard, source: query_report) }
  let(:manual_filter_definition) { FactoryBot.create :dynamic_filter_definition }
  let(:field_path) { DataModeling::Values::FieldPath.new(field_name: 'name', model_id: query_data_model.id) }
  let!(:lonely_filter) do
    FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, order: 1,
                                       dynamic_filter_definition: manual_filter_definition
  end
  let!(:mapping) do
    FactoryBot.create :dynamic_filter_mapping, viz_conditionable: widget,
                                               dynamic_filter: lonely_filter, field_path: field_path
  end

  let(:sheet_url) { 'https://docs.google.com/a/holistics.io/spreadsheets/d/173CtpqBDVYndxF2vcj9DoB3KPZzLMi6hAkpUE_TZoTU/edit' }
  let(:new_worksheet_id) { 51_417_787 }
  let(:schedule_mapping) { [{ widget_id: widget.id, widget_title: 'google sheet widget', worksheet_id: 0 }] }
  let(:sheet_meta) do
    {
      'spreadsheetUrl' => sheet_url,
      'spreadsheetId' => '173CtpqBDVYndxF2vcj9DoB3KPZzLMi6hAkpUE_TZoTU',
      'properties' => {
        'title' => 'Sheet title 123',
      },
      'sheets' => [{
        'properties' => {
          'index' => 0,
          'sheetId' => 0,
          'sheetType' => 'GRID',
          'title' => 'This is my sheet',
        },
      }, {
        'properties' => {
          'index' => 1,
          'sheetId' => new_worksheet_id,
          'sheetType' => 'GRID',
          'title' => 'test limit',
        },
      },],
    }
  end
  let(:dest) { FactoryBot.create(:gsheet_dest, mapping: schedule_mapping) }
  let!(:data_schedule) { FactoryBot.create(:email_schedule, tenant: tenant, dest: dest, source: dashboard) }
  let!(:ga) { FactoryBot.create(:google_authentication_export_v3, owner: test_user) }

  def setup_mock_sheet
    mock_sheet = double
    allow(mock_sheet).to receive(:is_a?).with(Google::GoogleSheet).and_return(true) # bypass Sorbet type checking
    allow(Google::GoogleSheet).to receive(:new).and_return mock_sheet
    allow(mock_sheet).to receive(:get_info).and_return sheet_meta
    allow(mock_sheet).to receive(:worksheets).and_return(
      { new_worksheet_id => GoogleApis::Sheets::Worksheet.new(id: new_worksheet_id, name: 'anymore', index: 0,
                                                              props: nil,) },
    )
  end

  def setup_mock_writer
    mock = double
    expect(mock).to receive(:pre_write).once
    expect(mock).to receive(:post_write).once

    allow(mock).to receive(:write_row) do |*args|
      expect(args[1]).to be_in(
        [
          ['alice'],
          ['bob'],
        ],
      )
    end
    dest = DummyDest.new(mock)
    allow(::Viz::Exporting::Dest::GoogleSheet).to receive(:new).and_return dest
  end

  it 'edits a google sheet schedule and run it with filter correctly' do
    qlogin(test_user, "/dashboards/v3/#{dashboard.id}")

    safe_click('.ci-schedule-dropdown')
    safe_click('.ci-manage-schedules')
    wait_for_element_load('.ci-actions')

    page.first('.ci-actions').click
    safe_click('.ci-es-edit')
    wait_for_element_load('.widget-select')
    tree_select_dropdown('.google-sheet-select .tree-select', value: '51417787')
    expect do
      safe_click('.ci-submit-btn')
    end.not_to change { data_schedule.dynamic_filter_presets.count }
    ds = EmailSchedule.find(data_schedule.id)
    expect(ds.dest.mapping[0]['worksheet_id']).to eql(new_worksheet_id)

    # Run the schedule and assert the value

    setup_mock_writer
    ds = EmailSchedule.find(data_schedule.id)

    job = FactoryBot.create(:job, user_id: test_user.id, data: { user_id: test_user.id })
    ds.async_options = { job_id: job.id }

    with_fake_async_context(job) do
      ds.execute
    end
  end
end
