# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'fit the table and pivot vertically', js: true do
  let(:admin) { get_test_admin }
  let(:pivot) { page.find('[data-ci="ci-ag-grid-pivot-table"]') }

  context 'when rendering in exploration' do
    include_context 'format is defined in data modeling'

    before do
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('ag-grid:pivot-table', true)
      FeatureToggle.toggle_global('table:single_row', true)
      FeatureToggle.toggle_global(DataSet::FT_CUSTOM_EXPRESSION, true)
      safe_login(admin, "/datasets/#{data_set.id}")

      safe_click('.ci-collapse-panel')
    end

    it 'fits the table vertically' do
      click_on_first_fields(8)
      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      check_horizontal_scrollbar_exist

      check_vertical_scrollbar_not_exist
    end

    it 'fits the pivot vertically' do
      page.find('.ci-viz-type-pivot_table').click
      create_pivot_viz_setting
      wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
      page.all('.btn-toggle-icon').first.click

      check_horizontal_scrollbar_exist

      check_vertical_scrollbar_not_exist
    end

    it 'fits the pivot when turning on text wrap' do
      page.find('.ci-viz-type-pivot_table').click
      create_pivot_viz_setting(has_row_fields: false)
      wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

      # make long column fields
      select_h_select_option('.pivot-section-columns .ci-empty-field', label: 'Add Business Calculation')
      page.find('.ci-label-input').set('custom column')
      safe_click_element(page.first('.view-line'))
      page.send_keys 'concat (data_modeling_orders.status, "longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong")'
      safe_click('.ci-submit')

      safe_click('[data-ci="ci-viz-setting-tab-styles"]')
      safe_click('[data-ci="ci-text-wrap"]')
      safe_click('[data-ci="ci-explorer-control-get-results"]')

      # expect still the only row
      wait_expect('1.10K') do
        cell = find_cell_element(table_element: pivot, row_id: 0, col_index: 2)
        cell.text
      end
    end
  end
end

def check_horizontal_scrollbar_exist
  horizontal_scrollbar = page.find('.ag-body-horizontal-scroll-viewport')
  expect(horizontal_scrollbar[:style].include?('width: 0px;')).to be false
  expect(horizontal_scrollbar[:style].include?('min-width: 0px;')).to be false
  expect(horizontal_scrollbar[:style].include?('max-width: 0px;')).to be false
end

def check_vertical_scrollbar_not_exist
  expect(page).to have_no_css('.ag-body-vertical-scroll-viewport')
end
