# frozen_string_literal: true

# typed: false

require 'rails_helper'

describe 'Geo Heatmap testing', js: true, stable: true do
  include_context 'test_tenant'

  before do
    FeatureToggle.toggle_global('data_models:manager', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
  end
  let(:limit) { 2000 }
  before do
    query = <<~SQL.strip_heredoc
      select
        (
          '{"LA", "NY", "SF", "CA", "DL"}' :: text []
        ) [ceil(random() * 5)] as state,
        round((random() * 1 + 4) :: numeric) as weight,
        round((random() * 1 + 10) :: numeric, 6) as lat,
        round((random() * 1 + 10) :: numeric, 6) as long
      from
        (
          select
            generate_series(1, #{limit}, 1) as i
        ) s
    SQL

    DataModels::CreateQueryModelService.async(tenant_id: admin.tenant_id, user_id: admin.id).execute(admin.id, ds.id,
                                                                                                     'new_sql_model', query, 0,)
    model = DataModel.last
    @data_set = DataSet.create_from_models(
      data_models: [model],
      title: 'New dataset',
      category_id: 0,
      data_source_id: model.data_source.id,
      owner_id: admin.id,
      tenant_id: tenant.id,
    )
    @data_set.save!

    @report = QueryReport.create!(
      title: 'DS based report',
      root_model_id: nil,
      is_adhoc: false,
      query: 'select 1',
      settings: {},
      data_set_id: @data_set.id,
      data_source_id: get_test_ds.id,
      tenant_id: get_test_tenant.id,
      owner_id: get_test_admin.id,
      viz_setting: FactoryBot.create(:viz_setting,
                                     viz_type: 'geo_heatmap',
                                     fields: {
                                       geo_label: {
                                         type: 'text',
                                         format: {
                                           type: 'string',
                                           sub_type: 'string',
                                         },
                                         path_hash: {
                                           model_id: model.id,
                                           field_name: 'state',
                                         },
                                         custom_label: nil,
                                         transformation: nil,
                                       },
                                       geo_latitude: {
                                         type: 'number',
                                         format: {
                                           type: 'number',
                                           sub_type: 'auto',
                                         },
                                         path_hash: {
                                           model_id: model.id,
                                           field_name: 'lat',
                                         },
                                         custom_label: nil,
                                         transformation: nil,
                                       },
                                       geo_longitude: {
                                         type: 'number',
                                         format: {
                                           type: 'number',
                                           sub_type: 'auto',
                                         },
                                         path_hash: {
                                           model_id: model.id,
                                           field_name: 'long',
                                         },
                                         custom_label: nil,
                                         transformation: nil,
                                       },
                                       geo_weighting: {
                                         type: 'number',
                                         format: {
                                           type: 'number',
                                           sub_type: 'auto',
                                         },
                                         path_hash: {
                                           model_id: model.id,
                                           field_name: 'weight',
                                         },
                                         custom_label: nil,
                                         transformation: nil,
                                       },
                                     },
                                     settings: {
                                       misc: {
                                         row_limit: nil,
                                         pagination_size: 25,
                                         show_row_number: true,
                                       },
                                       map_settings: {
                                         zoom: 12,
                                         auto_center: true,
                                         center_point: '0; 0',
                                         circle_radius: 20,
                                         show_markers: true,
                                       },
                                     },
                                     format: {},
                                     filters: [],
                                     source_type: 'QueryReport',
                                     source_id: 11,
                                     adhoc_fields: [],),
    )
  end

  it 'should show marker clustering' do
    safe_login(admin, query_report_path(@report))

    wait_for_element_load '.map-renderer .cluster'

    expect(page).to have_selector('.map-renderer .cluster')
  end

  it 'should not show legacy geo heatmap icon' do
    safe_login(admin, data_set_path(@data_set))
    wait_for_element_load '.viz-type-select'
    expect(page).not_to have_selector '.ci-viz-type-geo_heatmap'
  end

  context 'FT_DISABLE_LEGACY_HEATMAP is enabled' do
    it 'should show migration notice in edit mode' do
      FeatureToggle.toggle_global('viz_setting:disable_legacy_heatmap', true)

      safe_login(admin, "#{query_report_path(@report)}/edit_model")
      safe_click('.ci-explorer-control-get-results')

      wait_for_element_load '.ci-viz-result'

      expect(page.find('.ci-viz-result .geo-heatmap-migrating-alert').text.gsub("\n",
                                                                                ' ',)).to eq "We've just update our Geo Heatmap. Learn more Switch to the new version"

      safe_click('.ci-viz-result .ci-migrate-legacy-geo-heatmap-btn')

      wait_for_element_load '.ci-viz-result'

      expect(page).to have_selector('.ci-viz-result .heatmap-canvas')
    end

    it 'should not show migration notice while not in edit mode' do
      FeatureToggle.toggle_global('viz_setting:disable_legacy_heatmap', true)

      safe_login(admin, "#{query_report_path(@report)}")

      wait_for_element_load '.ci-viz-result'

      expect(page).not_to have_selector('.ci-viz-result .info-message .migration-legacy-viz-btn')
    end
  end
end
