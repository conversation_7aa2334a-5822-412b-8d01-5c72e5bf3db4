# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'conversion funnel', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:report) do
    sql = <<~SQL.strip_heredoc
      select column1 as "Code Editor",
             column2 as "Visited",
             column3 as "Sign Up",
             column4 as "Trial",
             column5 as "Activation"
      from (
        values ('Ruby Mine', 15600, 8700, 6300, 3500),
               ('Visual Studio Code', 23000, 15700, 8600, 6200),
               ('Atom', 9500, 7600, 5200, 3500)
      ) as R
    SQL
    FactoryBot.create :query_report, query: sql, data_source: ds, viz: {
      has_viz: true,
      viz_type: 'conversion_funnel',
      conversion_funnel_settings: { color: 'auto', circle_color: '#FF9E1A' },
    }
  end
  let(:rendered_rows) do
    [
      [],
      ['All', '48,100 (100%)', '32,000 (66.53%)', '20,100 (41.79%)', '13,200 (27.44%)'],
      ['Ruby Mine', '15,600 (100%)', '8,700 (55.77%)', '6,300 (40.38%)', '3,500 (22.44%)'],
      ['Visual Studio Code', '23,000 (100%)', '15,700 (68.26%)', '8,600 (37.39%)', '6,200 (26.96%)'],
      ['Atom', '9,500 (100%)', '7,600 (80%)', '5,200 (54.74%)', '3,500 (36.84%)'],
    ]
  end

  def check_rendered_content
    wait_for_element_load '.ci-conversion-funnel'
    expect(page).to have_selector('.ci-conversion-funnel .chart-container')
    expect(page).to have_selector('.ci-conversion-funnel table')
    rendered_table = page.all('.ci-conversion-funnel table tr').map do |row|
      row.all('td').map(&:text)
    end
    expect(rendered_table).to eq rendered_rows
  end

  it 'should create/edit conversion funnel' do
    safe_login(:admin, "#{query_report_path report}/edit")
    wait_for_element_load '.ci-report-preview:not([disabled])'
    safe_click'.ci-report-preview'
    wait_for_report_load
    safe_click'.ci-viz-tab'
    check_rendered_content

    page.first('.ci-column-color').find("option[value='#B5CA92']").select_option
    page.first('.ci-circle-color').find("option[value='#FF333D']").select_option
    safe_click'button#upper-save-only-button'

    sleep 1 # wait for save
    report.reload
    expect(report.viz['conversion_funnel_settings']['color']).to eq '#B5CA92'
    expect(report.viz['conversion_funnel_settings']['circle_color']).to eq '#FF333D'
  end

  it 'should show conversion funnel correctly at report show page' do
    safe_login :admin, query_report_path(report)
    check_rendered_content
  end

  context 'in dashboard' do
    let(:dashboard) { FactoryBot.create :dashboard, dashboard_widgets: [dashboard_widget] }
    let(:dashboard_widget) { FactoryBot.create :dashboard_widget, source: report, data: { view_mode: 'viz' } }
    it 'should show conversion funnel correctly at dashboard' do
      safe_login :admin, dashboard_path(dashboard)
      check_rendered_content
    end
  end
end
