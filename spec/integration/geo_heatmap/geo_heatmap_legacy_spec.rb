# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Geo Heatmap testing', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:current_tenant) { get_test_tenant }
  let(:ds) { get_test_ds }
  let(:test_label) { 'restaurant 101' }
  let(:report) do
    sql = <<~SQL.strip_heredoc
      with dummy as (
        VALUES
          (10.783, 106.664, 25, 'restaurant 101'),
          (10.776, 106.703, 30, 'restaurant 101'),
          (10.778, 106.702, 10, 'restaurant 101'),
          (10.767, 106.691, 55, 'restaurant 101'),
          (10.795, 106.672, 42, 'restaurant 101'),
          (10.774, 106.702, 22, 'restaurant 101')
        )
      select
        column1 as "lat",
        column2 as "lng",
        column3 as "Rating",
        column4 as "label"
      from dummy
    SQL

    FactoryBot.create :query_report, query: sql,
                                      viz: {
                                        has_viz: true,
                                        viz_type: 'geo_heatmap',
                                        geo_heatmap_settings: { zoom: 13, auto_center: true, radius: 20 },
                                      },
                                      data_source: ds, tenant: current_tenant
  end

  def wait_for_map_load
    wait_for_element_load('.map-renderer > div')
    sleep 2
  end

  def wait_for_editor_load
    wait_for_element_load('#editor')
    sleep 2
  end

  # to check the map rendered correctly
  # - enable the tooltip
  # - click on any tooltip
  # - compare the content of the tooltip
  def check_map_renderer
    # show the tooltips
    h_checkbox_check(selector: '.ci-geoheatmap-tooltip')
    page.find('.widget [data-icon=refresh]').click if page.has_css?('.widget [data-icon=refresh]') # work around for dashboard widget
    # select any tooltip
    image_selector = 'img[src*="https://maps.gstatic.com/mapfiles/transparent.png"]'
    wait_for { page.has_css?(image_selector, visible: false) } # markers are shown on ui, but not visible in rspec :(
    page.execute_script("document.querySelectorAll('#{image_selector}')[0].click()")
    wait_expect(true) do
      page.evaluate_script("document.querySelector('.map-renderer').innerText").include?(test_label)
    end
  end

  context 'with query report' do
    it 'should create/edit geo heatmap' do
      safe_login(:admin, edit_query_report_path(report))
      wait_for_editor_load
      safe_click('.ci-report-preview')
      wait_for_report_load

      safe_click('.ci-viz-tab')
      expect(page).to have_selector('.map-renderer')

      page.find('.ci-radius').set(30)
      h_checkbox_check(selector: '.ci-auto-center')

      wait_for_element_load('.ci-zoom-level')
      page.find('.ci-zoom-level').set(10)
      safe_click('button#upper-save-only-button')
      sleep 1 # wait for save

      report.reload
      expect(report.viz['geo_heatmap_settings']['zoom']).to eq 10
    end

    it 'should show geo heatmap correctly at report show page' do
      safe_login(:admin, query_report_path(report))
      wait_for_map_load
      check_map_renderer
    end
  end

  context 'with dashboard' do
    let(:dw) { FactoryBot.create :dashboard_widget, source: report, data: { view_mode: 'viz' } }
    let(:dashboard) { FactoryBot.create :dashboard, dashboard_widgets: [dw] }
    it 'should show geo heatmap correctly' do
      safe_login(:admin, dashboard_path(dashboard))
      wait_for_map_load
      check_map_renderer
    end
  end
end
