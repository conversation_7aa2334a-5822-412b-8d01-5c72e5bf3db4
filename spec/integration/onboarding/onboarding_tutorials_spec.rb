# frozen_string_literal: true
# typed: false

require 'rails_helper'

describe 'onboarding tutorials', js: true, stable: true do
  let(:tenant) { get_tenant('Onboarding Tutorials') }
  let(:user) { create(:user, role: User::ROLE_ADMIN, tenant_id: tenant.id) }
  let(:modal_selector) { '.onboarding-tutorials-modal' }

  def create_ot_survey_answer(question_key, data)
    create(
      :survey_answer,
      question_key: question_key,
      data: data,
      user_id: user.id,
      tenant_id: tenant.id,
    )
  end

  # Shared Example
  tab_details =
    [
      {
        label: 'Build Reports & Dashboard',
      },
      {
        label: '4-step Setup',
        description: 'The entire Holistics workflow can be boiled down into a 4-step Setup process. This Setup will get your data ready to be explored and shared across the team.',
        activable: true,
      },
      {
        label: 'Create data model and dataset',
        description: "Let's create some data models from our database, then we can group them into datasets and start exploring! Learn more",
        activable: true,
      },
      {
        label: 'Build a report',
        description: 'Reports in Holistics are built on top of a dataset. You can add a new report from a dashboard or save a new report from an exploration.',
        activable: true,
      },
      {
        label: 'Build a dashboard',
        description: 'Build your dashboard with widgets and then enrich it with Filters, Drill-through, Cross-filtering, etc. Learn more',
        activable: true,
      },
      {
        label: 'Interact with Dashboard',
      },
      {
        label: 'Find a report / dashboard',
        description: 'You can find your dashboards by searching, navigating to the left panel, or heading to Favorite/Recent box.',
        activable: true,
      },
      {
        label: 'Interact with data',
        description: "Holistics has many interactive features to help you get the most out of your data. Let's explore all of them. Learn more",
        activable: true,
      },
      {
        label: 'Share data with others',
        description: "There're so many ways you can share your data with others in Holistics. Let's find out how! Learn more",
        activable: true,
      },
    ]

  role_to_recommended_tab_details = {
    User::ROLE_ADMIN =>
      [tab_details[1], tab_details[2], tab_details[3],
       tab_details[4], tab_details[8],],
    User::ROLE_ANALYST =>
    [tab_details[1], tab_details[2], tab_details[3],
     tab_details[4], tab_details[8],],
    User::ROLE_EXPLORER =>
    [tab_details[1], tab_details[6], tab_details[7]],
    User::ROLE_BUSINESS_USER =>
    [tab_details[1], tab_details[6], tab_details[7]],
  }

  shared_examples 'onboarding tutorials modal' do
    activable_tab_details = tab_details.filter { |tab_detail| tab_detail[:activable] }

    let(:modal) { page.find('.onboarding-tutorials-modal') }
    let(:tabs) { modal.find('.onboarding-tutorials-modal__vertical-tabs') }
    let(:tab_selector) { '.vertical-tab--child' }
    let(:user_role) { User::ROLE_ADMIN }

    def assert_tab_active(tab_detail)
      tab_items = tabs.find_all(tab_selector)

      tab_items.each_with_index do |tab_item, _index|
        styles = tab_item.style('background-color', 'font-weight')

        if tab_item.has_content?(tab_detail[:label], wait: 0)
          expect(styles).to match(
            'background-color' => 'rgba(232, 242, 253, 1)',
            'font-weight' => '500',
          )
        else
          expect(styles).to match(
            'background-color' => 'rgba(0, 0, 0, 0)',
            'font-weight' => '400',
          )
        end
      end
    end

    describe 'vertical tabs' do
      it 'has correct tabs' do
        expect(
          tabs.find_all('span.flex-1').map(&:text),
        ).to eq(tab_details.map { |tab_detail| tab_detail[:label] })
      end

      it 'has the first tab active' do
        assert_tab_active(activable_tab_details[0])
      end

      it 'shows correct recommendation icons for current role' do
        recommended_list_items = tabs.find_all(tab_selector) do |li|
          li.has_css?('span[data-icon="favorite-solid"]', wait: 0)
        end

        # Reverse exhausted check
        expect(
          role_to_recommended_tab_details[user_role].map { |tab_detail| tab_detail[:label] },
        ).to(include(*recommended_list_items.map(&:text)))
      end
    end

    describe 'tab content' do
      let(:tab_content) { modal.find('.onboarding-tutorials-modal__tab-content') }

      it 'shows correct content of tabs when selected' do
        activable_tab_details.each do |tab_detail|
          li = tabs.find(tab_selector) { |li| li.has_content?(tab_detail[:label], wait: 0) }
          li.click

          expect(tab_content.has_content?(tab_detail[:label], wait: 0)).to be_truthy
          expect(tab_content.has_content?(tab_detail[:description], wait: 0)).to be_truthy
        end
      end

      next_button_label = 'Next'
      back_button_label = 'Back'
      it "conditionally shows and be able to navigate tabs via #{next_button_label}/#{back_button_label} buttons" do
        # Next
        activable_tab_details.each_with_index do |tab_detail, index|
          assert_tab_active(tab_detail)

          if index < activable_tab_details.size - 1
            tab_content.click_button(next_button_label, wait: 0)
          else
            expect { tab_content.find_button(next_button_label, wait: 0) }.to raise_error Capybara::ElementNotFound
          end
        end

        # Back (after navigating to the end via `Next`)
        activable_tab_details.reverse.each_with_index do |tab_detail, index|
          assert_tab_active(tab_detail)

          if index < activable_tab_details.size - 1
            tab_content.click_button(back_button_label, wait: 0)
          else
            expect { tab_content.find_button(back_button_label, wait: 0) }.to raise_error Capybara::ElementNotFound
          end
        end
      end
    end

    context 'when closing' do
      let(:hotspot_popover_trigger_selector) { '.header-help-center__hotspot-popover-trigger' }
      let(:hotspot_popover_selector) { '.header-help-center__hotspot-popover' }
      let(:hotspot_popover_trigger) { page.find(hotspot_popover_trigger_selector) }
      let(:hotspot_popover) { page.find(hotspot_popover_selector) }

      before do
        modal.click_button(class: 'ci-modal-close')
        sleep 0.5 # TODO(ajax, animation)
      end

      it "won't show again" do
        page.refresh

        expect do
          wait_for_element_load(modal_selector, 1)
        end.to raise_error(/Timeout occurred. Failed to wait for (.*)?./)
      end

      it 'shows popover + hotspot' do
        wait_for_element_load(hotspot_popover_trigger_selector)
        wait_expect(/You can always watch the\s*Tutorials again from inside this\s*icon\s*Got it/) do
          hotspot_popover.text
        end
      end

      it 'hides popover + hotspot when clicking close button' do
        hotspot_popover.click_button('Got it')
        page.assert_no_selector(hotspot_popover_selector)
        page.assert_no_selector(hotspot_popover_trigger_selector)
      end
    end
  end

  before do
    ftg = FeatureToggleGroups::UpsertVersions.new.call!('Version 3.0')
    ftg.toggle_tenants!([tenant.id], FeatureToggleGroup::TOGGLE_MODE_ADD)
  end

  context 'when user is admin' do
    before do
      create_ot_survey_answer('onboarding:welcome_modal', { seen: true })
      create_ot_survey_answer('onboarding:welcome_screen', { passed: true })
      create(:data_source, name: 'some_data_source', tenant_id: tenant.id)
      create_ot_survey_answer('onboarding:has_data_model', { created: true })
      create_ot_survey_answer('onboarding:first_data_set', { created: true })
      create_ot_survey_answer('onboarding:data_set_explore_tour', { is_done: true })

      safe_login(user, '/home')

      wait_for_element_load(modal_selector, 1)
    end

    it_behaves_like 'onboarding tutorials modal'
  end
end
