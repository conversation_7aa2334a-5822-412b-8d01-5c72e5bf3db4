# typed: false
require 'rails_helper'

describe 'edit category', js: true, stable: true do
  let(:admin) { get_test_admin }
  let!(:category) { create :report_category }

  it 'edit category' do
    safe_login(admin, '/browse')
    wait_and_click('.ci-actions')

    open_modal_by_click('.ci-edit-folder')
    fill_in 'folder_name', with: 'Rename Category'
    page.find('.ci-submit').click

    expect(page.has_content?('Rename Category'))
  end

  it 'check whether edit category with empty name' do
    safe_login(admin, '/browse')
    wait_and_click('.ci-actions')

    open_modal_by_click('.ci-edit-folder')
    fill_in 'folder_name', with: ''
    page.find('.ci-submit').click

    expect(page.has_content?('Name is required'))
  end

  it 'delete category' do
    safe_login(admin, '/browse')
    wait_and_click('.ci-actions')

    page.find('.ci-delete').click
    page.find('.ci-confirm-delete').click

    expect_notifier_content 'folder deleted successfully'
  end
end
