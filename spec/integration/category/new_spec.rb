# typed: false
require 'rails_helper'

describe 'new category', js:true, stable: true do
  it 'create a new category' do
    qlogin :admin, '/browse'
    wait_for_element_load('.ci-create')
    page.first('.ci-create').click
    page.first('.ci-create-new-folder').click
    fill_in 'category-name', with: 'New Category'
    page.first('.ci-submit-btn').click
    expect(page.has_content?('New Category'))
  end

  it 'check whether create a new category with empty name' do
    qlogin :admin, '/browse'
    wait_for_element_load('.ci-create')
    page.first('.ci-create').click
    page.first('.ci-create-new-folder').click
    fill_in 'category-name', with: ''
    page.first('.ci-submit-btn').click
    sleep 5
    expect(page.has_content?('Name is required'))
  end
end
