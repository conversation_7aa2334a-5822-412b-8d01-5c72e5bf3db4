# typed: false
require 'rails_helper'

describe 'Model from Sql query', js: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:cat) { DataModelCategory.find_by(data_source_id: ds.id) }

  before do
    FeatureToggle.toggle_global('schema_synchronization', true)
    FeatureToggle.toggle_global('data_models:manager', true)

    @dummy = DataModel.create_from_query(name: 'Dummy', query: '', data_source: ds, owner_id: admin.id)
  end

  after do
    @dummy.destroy
  end

  def set_name_and_query(name, query)
    wait_for_element_load '.ci-editor'

    if name.present? # dont set name if not provided
      wait_and_click('.ci-name .title')
      wait_and_set('.ci-name input', name)
    end
    safe_click '.ace_content'
    ace_set_text '#editor', query
    wait_and_click('.ci-preview')
    sleep 1
    wait_and_click('.ci-save')

    wait_for {
      safe_click('.ci-confirm') if page.has_css?('.ci-confirm')
      page.all('.ci-editor').count == 0
    }
  end

  def assert_query_model(expected_name, expected_fields)
    wait_for_element_load '.ci-model-meta'

    name = page.first('.ci-model-name').text
    expect(name).to eq(expected_name)

    fields = page.all('.ci-model-structure tbody tr td:first-child').map(&:text)
    expect(fields).to eq(expected_fields)
  end

  it 'can create new query model' do
    qlogin admin, "/data_models?ds=#{ds.id}&cat=#{cat.id}"
    wait_and_click('.ci-create')
    wait_and_click('.ci-create-data-transform')

    name, query = 'test_model', 'select 1'
    set_name_and_query(name, query)

    visit "/data_models?ds=#{ds.id}&model=#{DataModel.last.id}"
    wait_for { page.current_path == '/data_models' }
    assert_query_model name, %w(column)
  end

  it 'can update an existing query model' do
    qlogin admin, "/data_models?ds=#{ds.id}&model=#{@dummy.id}"
    wait_and_click('.ci-edit-sql')
    sleep 2

    # page.execute_script "$('.ace_text-input').val('')"
    query = 'select \'Hoang Do\' as name, 1994 as birth_year'
    set_name_and_query nil, query
    sleep 2

    assert_query_model 'dummy', ['birth_year', 'name']
  end

  context 'new sql gen is enabled' do
    before do
      FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
      FeatureToggle.toggle_global('viz:table_v2', true)
    end
    let(:sql) do
      <<~SQL
          select * from
          ( VALUES
            ('bread', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('milk', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('egg', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('bread', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('eggplant', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('cucumber', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('apple', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('pen', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('pinapple', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('cow', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('pig', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('chicken', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('sponge', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('chili', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('fish', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('crab', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('bread', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('milk', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('eggplant', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('cucumber', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('apple', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('samsung', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('fish', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('cow', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('eggplant', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('cucumber', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
            ('apple', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
            ('samsung', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
            ('fish', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
            ('cow', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z')
          ) as products
      SQL
    end

    it 'can create new query model' do
      qlogin admin, "/data_models?ds=#{ds.id}&cat=#{cat.id}"
      wait_and_click('.ci-create')
      wait_and_click('.ci-create-data-transform')

      name = 'test_model'
      set_name_and_query(name, sql)

      visit "/data_models?ds=#{ds.id}&model=#{DataModel.last.id}"
      wait_for { page.current_path == '/data_models' }
      assert_query_model name, ["column1", "column2", "column3", "column4", "column5", "column6"]
      safe_click '.ci-model-tabs .ci-tab-toggle:last-child'
      wait_for_element_load('.ci-table-report-data')
      expect(page.find_all('.ci-table-report-data .ag-row').length).to eq(25)
      safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')
      wait_expect(false, 10) { page.has_css?('.ci-viz-result .v-loading-wrapper') }
      expect(page.find_all('.ci-table-report-data .ag-row').length).to eq(5)
    end
  end
end
