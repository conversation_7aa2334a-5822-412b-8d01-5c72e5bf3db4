# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'data model import', js: true do
  let (:postgre_ds) { get_test_ds }
  let (:mysql_ds) { mysql_testdb_ds }
  let (:ga_ds) { google_analytics_testdb_ds }
  let (:from_fqname) { FQName.parse 'public.foobar' }
  let (:from_fqname2) { FQName.parse 'public.barfoo' }
  let (:columns) do
    [
      { column_name: 'col_0', data_type: 'varchar(25)' },
      { column_name: 'col_1', data_type: 'varchar(25)' },
      { column_name: 'col_2', data_type: 'varchar(25)' },
      { column_name: 'col_3', data_type: 'varchar(25)' }
    ]
  end
  let (:rows) do
    [
      %w[1 2 3 4],
      %w[5 6 7 8]
    ]
  end
  let(:current_user) { users(:admin) }

  before do
    FeatureToggle.toggle_global('schema_synchronization', true)
    FeatureToggle.toggle_global('data_models:manager', true)
    FeatureToggle.toggle_global('data_sources:google_analytics', true)

    postgre_ds.settings[:default_schema] = from_fqname.schema_name
    postgre_ds.save!

    ga_ds.save!

    Cache.clear postgre_ds.cacher.all_columns_cache_key
    Cache.clear mysql_ds.cacher.all_columns_cache_key
    ds_create_table(connector: Connectors.from_ds(postgre_ds), columns: columns, fqname: from_fqname, rows: rows)
    ds_create_table(connector: Connectors.from_ds(mysql_ds), columns: columns, fqname: from_fqname, rows: rows)
    ds_create_table(connector: Connectors.from_ds(mysql_ds), columns: columns, fqname: from_fqname2, rows: rows)
  end

  after do
    ds_drop_table(connector: Connectors.from_ds(postgre_ds), fqname: from_fqname)
    ds_drop_table(connector: Connectors.from_ds(mysql_ds), fqname: from_fqname)
    ds_drop_table(connector: Connectors.from_ds(mysql_ds), fqname: from_fqname2)
  end

  def test_preview_data(values)
    wait_for_element_load '.data-table'
    wait_expect(values) do
      page.all('.data-table .htCore tbody tr td').map { |td| td.text.gsub(/,/, '') }
    end
  end

  def goto_import_modal(ds_id)
    qlogin(current_user, "/data_models?ds=#{ds_id}")
    safe_click('.ci-create')
    safe_click('.ci-create-data-import')
    wait_for_element_load '.add-model'
  end

  def attach_csv(file_name = 'test_import_csv')
    wait_expect(1) { page.all('.ready').count }
    execute_script("document.querySelector('input[name=\"fileInput\"]').classList.remove('!hidden')")
    attach_file('fileInput', Rails.root + "spec/fixtures/uploads/#{file_name}.csv")
  end

  def expect_disabled(selector)
    expect(%w[true disabled]).to include(page.find(selector)[:disabled])
  end

  context 'from database' do
    it 'can search' do
      goto_import_modal mysql_ds.id
      page.find('button', text: 'PostgreSQL').click

      wait_for_element_load '.import-data-model .dropdown-item .ci-item-name'
      set_text('.import-data-model .h-search-box .h-input', from_fqname.table_name)

      item = page.find('.import-data-model .dropdown-item .ci-item-name', match: :first)
      expect(item).to have_content from_fqname.to_unquoted_s
    end

    it 'generate model name, table name and load preview data' do
      goto_import_modal postgre_ds
      page.find('button', text: 'MySQL').click

      wait_for_element_load '.import-data-model .dropdown-item .ci-item-name'
      expect(page.find('.ci-source-table-group-title').text).to eq('Create Data Models from')

      expect(page.find('.import-data-model')).to have_button('Create', disabled: true)
      table = page.all('.import-data-model .dropdown-item .ci-item-name').find do |item|
        item.text == from_fqname2.table_name
      end
      table.click

      expected_rows = rows.flatten
      test_preview_data(expected_rows)

      expect(page.find('.ci-name')).to have_content from_fqname2.table_name.underscore
      expected_fq_name = FQName.new(schema_name: from_fqname.schema_name, table_name: "persisted_#{from_fqname2.table_name.underscore}").to_unquoted_s
      expect(page.first('.advanced-setting .ci-fq-name')).to have_content expected_fq_name
      expect(page.find('.import-data-model')).to have_button('Create', disabled: false)
    end

    it 'can create multi data models succesfully' do
      goto_import_modal postgre_ds
      page.find('button', text: 'MySQL').click

      wait_for_element_load '.import-data-model .dropdown-item .ci-item-name'
      page.all('.import-data-model .dropdown-item .ci-item-name').each do |item|
        item.click if [from_fqname.table_name, from_fqname2.table_name].include? item.text
        sleep 1
      end

      sleep 1
      safe_click('.import-data-model .ci-create')

      wait_for_element_load '.node-browse .draggable-node'
      model_names = page.all('.node-browse .draggable-node .node-title').map(&:text)
      expect(model_names).to match_array([from_fqname.table_name, from_fqname2.table_name])
    end

    it 'should ask for confirmation if the table name is already existed' do
      goto_import_modal postgre_ds
      page.find('button', text: 'MySQL').click

      wait_for_element_load '.import-data-model .dropdown-item .ci-item-name'
      table = page.all('.import-data-model .dropdown-item .ci-item-name').find do |item|
        item.text == from_fqname2.table_name
      end
      table.click

      wait_for_element_load '.data-table'
      page.find('.edit-fq-name .ci-edit').click
      fill_text('.edit-fq-name .ci-table-name', from_fqname.table_name)
      page.find('.edit-fq-name .ci-apply').click
      message = page.find('.overwrite-table-model .h-modal-body').text
      expect(message).to include "The table #{from_fqname.to_unquoted_s} already exists."
      expect_disabled('.ci-confirm')

      fill_text('.ci-confirmation-input', 'alibaba')
      expect_disabled('.ci-confirm')

      fill_text('.ci-confirmation-input', from_fqname.to_unquoted_s)
      safe_click('.ci-confirm')
      wait_for_element_load '.ci-fq-name'
      expect(page.find('.ci-fq-name').text).to eq from_fqname.to_unquoted_s
    end
  end

  context 'from csv' do
    it 'generate model name, table name and load preview data' do
      goto_import_modal postgre_ds
      page.find('button', text: 'CSV').click

      wait_for_element_load '.import-data-model-from-file'
      expect(page.find('.import-data-model-from-file')).to have_button('Create', disabled: true)
      attach_csv

      lines = CSV.parse(fixture_read_file('uploads/test_import_csv.csv'))
      expected_rows = lines[1..-1].flatten
      test_preview_data(expected_rows)

      expect(page.find('.ci-name')).to have_content 'test_import_csv'
      expected_fq_name = FQName.new(schema_name: from_fqname.schema_name, table_name: 'persisted_test_import_csv').to_unquoted_s
      expect(page.first('.import-data-model-from-file .ci-fq-name')).to have_content expected_fq_name
      expect(page.find('.import-data-model-from-file')).to have_button('Create', disabled: false)
    end

    it 'preview table should show type on header' do
      goto_import_modal postgre_ds
      page.find('button', text: 'CSV').click

      wait_for_element_load '.import-data-model-from-file'
      expect(page.find('.import-data-model-from-file')).to have_button('Create', disabled: true)
      attach_csv

      lines = CSV.parse(fixture_read_file('uploads/test_import_csv.csv'))
      expected_rows = lines[1..-1].flatten
      test_preview_data(expected_rows)

      headers = page.all('.ht_clone_top thead tr:first-child th')

      headers.map do |th|
        expect th.should have_selector :css, '.h-icon'
      end
    end

    it 'can create data model succesfully' do
      goto_import_modal postgre_ds
      page.find('button', text: 'CSV').click

      wait_for_element_load '.import-data-model-from-file'
      attach_csv

      wait_for_element_load '.data-table'
      sleep 1
      safe_click('.import-data-model-from-file .ci-create')

      wait_for_element_load '.ci-model-details'
      dm = DataModel.last
      expect(page.first("#navigation-node-DataModel-#{dm.id} .title")).to have_content 'test_import_csv'
    end

    it 'with one column can create data model succesfully' do
      goto_import_modal postgre_ds
      page.find('button', text: 'CSV').click

      wait_for_element_load '.import-data-model-from-file'
      attach_csv('test_import_csv_one_column')

      wait_for_element_load '.data-table'
      sleep 1
      safe_click('.import-data-model-from-file .ci-create')

      wait_for_element_load '.ci-model-details'
      dm = DataModel.last
      expect(page.first("#navigation-node-DataModel-#{dm.id} .title")).to have_content 'test_import_csv_one_column'
    end

    it 'can re-select CSV' do
      goto_import_modal postgre_ds
      page.find('button', text: 'CSV').click

      wait_for_element_load '.import-data-model-from-file'
      attach_csv
      expect(page.find('.import-data-model-from-file .ci-name')).to have_content 'test_import_csv'

      page.first('.import-data-model-from-file .ci-clear-file').click
      expect(page.find('.import-data-model-from-file')).to have_button('Create', disabled: true)
      attach_csv('test_import_csv_postgres')

      expect(page.find('.import-data-model-from-file .ci-name')).to have_content 'test_import_csv_postgres'
      expect(page.find('.import-data-model-from-file')).to have_button('Create', disabled: false)
    end
  end

  context 'from google_analytics' do
    it 'generate model name, table name and load preview data' do
      VcrHelper.ignore_hosts('127.0.0.1', 'localhost')

      VCR.use_cassette('google/google_analytics_data_import') do
        goto_import_modal postgre_ds
        page.find('button', text: 'Google Analytics').click

        wait_for_element_load '.google-analytics-form .ci-ga-view'

        # select metric
        wait_for_element_load '.ci-ga-metric'
        select_ga_checkbox('.ci-ga-metric .tree-select-control', 'Users')
        page.first('.import-data-model').click # click outside to close select dropdown

        # select dimension
        wait_for_element_load '.ci-ga-metric'
        select_ga_checkbox('.ci-ga-dimension .tree-select-control', 'User Type')
        page.first('.import-data-model').click

        # Change date to match VCR date
        page.first('.ci-ga-start-date .ci-date-select').click
        sleep 0.5
        page.first('.ci-date-input').set('2018-07-11')
        page.first('.ci-ga-start-date .ci-date-select').click

        page.find('.import-data-model').click # click outside to close popover
        wait_for { page.all('.ci-date-input').count == 0 }

        page.first('.ci-ga-end-date .ci-date-select').click
        sleep 0.5
        page.first('.ci-date-input').set('2018-07-17')
        page.first('.ci-ga-end-date .ci-date-select').click

        # Validate query
        page.first('button.self-center').click
        wait_for_element_load '.data-table'
        sleep 3

        # Create data model
        safe_click('.import-data-model .ci-create')
        wait_for_element_load '.node-browse .draggable-node'

        dm = DataModel.last
        model_name = page.first('.node-browse .draggable-node .node-title').text
        expect(model_name).to eql dm.name
      end
    end
  end
end
