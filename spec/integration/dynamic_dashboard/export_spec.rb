# typed: ignore
require 'rails_helper'

describe 'Export from dashboard', js:true do
  include_context 'query_model_based_report'

  before do
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(::Dashboard::FT_V3_CONVERSION, true)
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    DataSourceVersions::SchemaSynchronizationService.new(ds).execute
  end

  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:qr) { query_model_based_report }

  def wait_for_widget_load
    wait_for_all_holistics_loadings
  end

  context 'Dynamic dashboard (3.0) ' do
    let!(:dashboard) { FactoryBot.create :dashboard, version: 3}
    let!(:text_filter) { FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard }
    let!(:report_widget) { FactoryBot.create :dashboard_widget, source: qr, dashboard: dashboard }
    let!(:mapping) {
      # match the field in query_model_based_report
      field_path = DataModeling::Values::FieldPath.new(field_name: 'name')
      FactoryBot.create :dynamic_filter_mapping,
      viz_conditionable: report_widget,
      dynamic_filter: text_filter,
      field_path: field_path
    }

    context 'dashboard timezone' do
      include_context 'timezone_dynamic_dashboard'

      context 'allow to change timezone' do
        it 'should export widget to csv using selected timezone' do
          qlogin(admin, "/dashboards/v3/#{timezone_dashboard.id}")
          wait_for_widget_load
          search_h_select('.ci-query-processing-timezone', text: 'Asia/Tokyo')
          select_h_select_option('.ci-query-processing-timezone', value: 'Asia/Tokyo')
          wait_for_widget_load

          page.find('.ci-report-widget').hover

          safe_click('.ci-widget-controls-trigger')
          page.find('.ci-download').hover
          safe_click('.ci-export-csv-widget')

          wait_expect(true, 30) do
            raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

            page.all('.ci-download-link').present?
          end

          download_link = page.first('.ci-download-link')['href'].to_s.presence ||
                          page.first('.ci-download-link')['data-download-url'].to_s
          cookie = page.driver.browser.manage.cookie_named('_holistics_session')[:value]

          file = URI.open(download_link, 'Cookie' => "_holistics_session=#{cookie}")

          # explicit timezone should be applied
          expect(file.read).to eql("Val,Time,Date\n10,2021-10-21 01:30:00 +0000,2021-10-20\n20,2021-10-21 08:00:00 +0000,2021-10-20\n30,2021-10-21 21:00:00 +0000,2021-10-21\n")
        end
      end

      it 'should export dashboard to excel' do
        qlogin(admin, "/dashboards/v3/#{dashboard.id}")
        wait_for_element_load '.ci-schedule-dropdown'
        safe_click('.ci-schedule-dropdown')
        page.find('.ci-download-excel').click

        wait_expect(true, 30) do
          raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

          page.all('.ci-download-link').present?
        end

        metadata = test_download_link
        expect(metadata.base_uri.to_s).to match /\.xlsx$/
      end
    end

    it 'should export dashboard with filter to pdf', require_puppeteer: true do
      qlogin(admin, "/dashboards/v3/#{dashboard.id}")
      wait_for_element_load '.ci-schedule-dropdown'
      safe_click('.ci-schedule-dropdown')
      page.find('.ci-download-pdf').click

      wait_expect(true, 30) do
        raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

        page.all('.ci-export-success').present?
      end

      metadata = test_download_link
      expect(metadata.base_uri.to_s).to match /\.pdf$/
    end

    it 'should export widget with filter' do
      qlogin(admin, "/dashboards/v3/#{dashboard.id}")
      wait_for_element_load('.ci-report-widget')
      page.find('.ci-report-widget').hover

      safe_click('.ci-widget-controls-trigger')
      page.find('.ci-download').hover
      safe_click('.ci-export-csv-widget')

      wait_expect(true, 30) do
        raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

        page.all('.ci-download-link').present?
      end

      download_link = page.first('.ci-download-link')['href'].to_s.presence ||
                      page.first('.ci-download-link')['data-download-url'].to_s
      cookie = page.driver.browser.manage.cookie_named('_holistics_session')[:value]

      file = URI.open(download_link, 'Cookie' => "_holistics_session=#{cookie}")

      # filter should be applied
      expect(file.read).to eql("name\nalice\n")
    end

    context 'export csv with formatted data' do
      let(:qr1) { create :report_with_data_formats }
      let(:dashboard1) { create :dashboard, version: 3 }
      let!(:report_widget1) { create :dashboard_widget, source: qr1, dashboard: dashboard1 }

      before do
        FeatureToggle.toggle_global('use_new_viz_exporter_to_export_csv', true)
        FeatureToggle.toggle_global(Excel::Formats::Value::FT_FORMAT_TIMESTAMP, true)
      end

      it 'applies data format' do
        qlogin(admin, "/dashboards/v3/#{dashboard1.id}")
        wait_for_element_load('.ci-report-widget')
        page.find('.ci-report-widget').hover

        safe_click('.ci-widget-controls-trigger')
        page.find('.ci-download').hover
        safe_click('.ci-export-formatted-data-csv-widget')

        wait_expect(true, 30) do
          raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

          page.all('.ci-download-link').present?
        end

        download_link = page.first('.ci-download-link')['href'].to_s.presence ||
                        page.first('.ci-download-link')['data-download-url'].to_s
        cookie = page.driver.browser.manage.cookie_named('_holistics_session')[:value]

        file = URI.open(download_link, 'Cookie' => "_holistics_session=#{cookie}")

        expect(file.read).to eql("date,number,timestamp\n\"May 03, 2024\",\"1,234.57\",2024-05-03 11:11:11\n")
      end
    end
  end

  context 'Dynamic Dashboard export include cross filter', require_puppeteer: true do
    include_context 'cross_filtering_on_dynamic_dashboard_context'
    before do
      FeatureToggle.toggle_global('crossfilter:enabled', true)
      FeatureToggle.toggle_global('exportings:pdf', true)
      dashboard.cross_filtering_enabled = true
      dashboard.save
    end

    def login_and_wait_for_dashboard_load
      qlogin(admin, "/dashboards/v3/#{dashboard.id}")
      wait_for_widget_load
    end

    def check_number_of_viz_conditions(viz_conditions_length)
      export_request = extract_ajax_requests({ url: /\/submit_export\.pdf/ }).last
      widget_viz_condtions_length = export_request['data']['widget_viz_conditions'].length
      expect(widget_viz_condtions_length).to be(viz_conditions_length)
    end

    def click_value_and_submit_cross_filter
      # Click value in column chart
      widget = page.find('.ci-report-widget', text: /Column Chart/)
      wait_for_element_load '#widget-1 .widget-viz-container .highcharts-container .highcharts-series-group .highcharts-series-0'
      wait_for_element_load '#widget-1 .highcharts-point.highcharts-color-0'
      column = widget.first('.highcharts-point.highcharts-color-0')
      column.hover # to remove the cover
      column.click
    end

    def login_and_click_cross_filter
      login_and_wait_for_dashboard_load
      click_value_and_submit_cross_filter
    end

    def click_option_to_export
      # click option to export
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-download-pdf')
    end

    context 'enable feature toggle' do
      before do
        FeatureToggle.toggle_global('exportings:allow_include_cross_filter_on_dynamic_dashboard', true)
      end

      it 'include cross filter viz conditions in export dashboard request' do
        login_and_click_cross_filter
        click_option_to_export
        # 3 widget same a dataset => should have 2 widget viz conditions
        check_number_of_viz_conditions(2)
      end

      it 'include cross filter viz conditions in export widget request' do
        login_and_click_cross_filter
        page.find("#widget-#{report_widget_pie.id}").hover

        safe_click('.ci-widget-controls-trigger')
        page.find('.ci-download').hover
        safe_click('.ci-export-pdf-widget')

        check_number_of_viz_conditions(1)
      end
    end

    context 'disable feature toggle' do
      before do
        FeatureToggle.toggle_global('exportings:allow_include_cross_filter_on_dynamic_dashboard', false)
      end

      it 'exclude cross filter viz conditions in export dashboard request' do
        login_and_click_cross_filter
        click_option_to_export
        # 3 widget same a dataset, however disabled and it not exist filter => should have 0 widget viz conditions
        check_number_of_viz_conditions(0)
      end

      it 'exclude cross filter viz conditions in export widget request' do
        login_and_click_cross_filter
        page.find("#widget-#{report_widget_pie.id}").hover

        safe_click('.ci-widget-controls-trigger')
        page.find('.ci-download').hover
        safe_click('.ci-export-pdf-widget')
        check_number_of_viz_conditions(0)
      end
    end
  end

  context 'Canvas dashboard (4.0) ' do
    include_context 'dashboards_v4'

    def login_and_wait_for_dashboard_load
      qlogin(admin, "/dashboards/v4/#{dashboard_with_filter.id}")
      wait_for_element_load do
        page.find_by_id('block-v1')
      end

      wait_for_element_load('.ci-table-report-data')
    end

    def click_export_pdf_option
      safe_click('[data-ci="ci-export-dropdown"]')
      safe_click('.ci-download-pdf')
    end

    def check_export_correct(file_type: 'pdf')
      wait_expect(true, 30) do
        raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

        page.all('.ci-export-success').present?
      end

      metadata = test_download_link
      expect(metadata.base_uri.to_s).to match /\.#{file_type}$/
    end

    context 'export dashboard', require_puppeteer: true do
      it 'should export dashboard with filter to pdf' do
        login_and_wait_for_dashboard_load
        click_export_pdf_option
        check_export_correct
      end

      context 'export include cross filter' do
        let(:vcr_match_cond) { %i[method uri host path body] }

        let(:query_model_sql) do
          <<~SQL
             SELECT *
              FROM (VALUES
                ('2020-01-01 23:00:00'::timestamp, 10),
                ('2020-02-01 23:00:00'::timestamp, 20),
                ('2020-02-29 22:30:00'::timestamp, 50),
                ('2020-03-01 23:00:00'::timestamp, 30),
                ('2020-03-31 22:00:00'::timestamp, 40)
              )
            AS t (date_and_time, value)
          SQL
        end

        let(:uname_dashboard) { 'dashboard_cross_filter'}

        let(:viz_table_fields) do
          [
            {
              **viz_field_datetime,
              'transformation' => 'datetrunc month',
            },
          ]
        end

        let(:blocks) do
          [viz_block_table, line_chart_viz_block]
        end

        let(:interactions) do
          [
            { 'to' => 'v2', 'from' => 'v1', 'type' => 'CrossFilterInteraction' },
            { 'to' => 'v1', 'from' => 'v2', 'type' => 'CrossFilterInteraction' },
          ]
        end

        let(:block_views) do
          {
            'v1' => { 'layer' => 0, 'position' => { 'h' => 320, 'w' => 600, 'x' => 0, 'y' => 0 }},
            'v2' => { 'layer' => 0, 'position' => { 'h' => 320, 'w' => 580, 'x' => 620, 'y' => 0 }},
          }
        end


        def click_value_and_submit_cross_filter
          # Click first value of table
          safe_click('.ag-cell:not(.ag-column-first)')

          # Submit cross filter
          safe_click('[data-ci="ci-submit-filters-btn"]')
        end

        def check_number_of_viz_conditions(viz_conditions_length)
          export_request = extract_ajax_requests({ url: /\/submit_export\.pdf/ }).last
          widget_viz_condtions_length = export_request['data']['widget_viz_conditions'].length
          expect(widget_viz_condtions_length).to be(viz_conditions_length)
        end

        before do
          FeatureToggle.toggle_global('crossfilter:enabled', true)
        end

        context 'enable feature toggle' do
          before do
            FeatureToggle.toggle_global('exportings:allow_include_cross_filter', true)
          end

          it 'can export to s3 successfully' do
            login_and_wait_for_dashboard_load
            click_value_and_submit_cross_filter
            click_export_pdf_option
            check_number_of_viz_conditions(1)
            check_export_correct
          end
        end

        context 'disable feature toggle' do
          before do
            FeatureToggle.toggle_global('exportings:allow_include_cross_filter', false)
          end

          it 'can export to s3 successfully' do
            login_and_wait_for_dashboard_load
            click_value_and_submit_cross_filter
            click_export_pdf_option
            check_number_of_viz_conditions(0)
            check_export_correct
          end
        end
      end
    end

    context 'export tabs' do
      include_context 'simple_image_dashboard_v4'
      let(:blocks) { [viz_block_table, viz_block_table_2, filter_block, text_block] }
      let(:views) { tab_layout }

      before do
        Timecop.return
        FeatureToggle.toggle_global('exporting:export_tabs_on_canvas_dashboard', true)
      end

      def login_and_wait_for_dashboard_load
        qlogin(admin, "/dashboards/v4/#{dashboard.id}")
        wait_for_element_load do
          page.find_by_id('block-v1')
        end

        wait_for_element_load('.ci-table-report-data')
      end

      it 'can export multiple pdf tabs', require_puppeteer: true do
        login_and_wait_for_dashboard_load
        safe_click('[data-ci="ci-export-dropdown"]')
        # Click export tab pdf
        safe_click('.ci-download-tab-pdf')
        safe_click('.ci-export-selected-tab-pdf')
        # Selected tab 2 and tab3
        wait_for_element_load('[data-ci="ci-export-tab-modal"]')
        # Already checked tab2 and tab3
        # Export submit button
        safe_click('[data-hui-section="resolve-button"]')
        # Check correct link
        check_export_correct
      end

      it 'can export multiple png tabs convert to zip files', require_puppeteer: true do
        login_and_wait_for_dashboard_load
        safe_click('[data-ci="ci-export-dropdown"]')
        # Click export tab pdf
        safe_click('.ci-download-tab-png')
        safe_click('.ci-export-selected-tab-png')
        # Selected tab 2 and tab3
        wait_for_element_load('[data-ci="ci-export-tab-modal"]')
        # Already checked tab2 and tab3
        # Export submit button
        safe_click('[data-hui-section="resolve-button"]')
        # Check correct link
        check_export_correct(file_type: 'zip')
      end

      it 'return png when single tab', require_puppeteer: true do
        login_and_wait_for_dashboard_load
        safe_click('[data-ci="ci-export-dropdown"]')
        safe_click('.ci-download-tab-png')
        safe_click('.ci-export-current-tab-png')
        check_export_correct(file_type: 'png')
      end

      it 'return xlsx when single tab' do
        login_and_wait_for_dashboard_load
        safe_click('[data-ci="ci-export-dropdown"]')
        safe_click('.ci-download-tab-xlsx')
        safe_click('.ci-export-current-tab-xlsx')
        check_export_correct(file_type: 'xlsx')
      end
    end
  end
end
