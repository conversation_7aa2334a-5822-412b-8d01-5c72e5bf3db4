# frozen_string_literal: true

# typed: ignore
require 'rails_helper'

describe 'New flow creating report >', js: true do
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
  end

  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
  let(:dashboard_link) { "/dashboards/v3/#{dashboard.id}" }
  let(:report_data) do
    {
      title: 'new report',
      edited_title: 'newer report',
    }
  end

  include_context 'data_modeling_schema_with_data'


  def select_viz_field(model_id, field_name, index = 0)
    wait_for_element_load('.ci-viz-field-select')
    select_h_select_option '.ci-viz-field-select .ci-empty-field', value: "#{model_id}$!#{field_name}", index: index
  end

  def select_sort_field(label, index = 0)
    wait_for_element_load('.ci-sort-column-select')
    select_h_select_option '.ci-sort-column-select', label: label, index: index
  end

  def create_widget(fields: ['price'], sorts: [])
    page.find('.ci-dataset-mode').click
    wait_for_element_load('.ci-data-set-select')

    select_h_select_option('.ci-data-set-select', value: "#{@dataset.id}ds")
    fields.each.with_index do |field, index|
      wait_for_element_load('.viz-section:nth-child(1) .ci-viz-field-select .ci-empty-field')
      select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select .ci-empty-field', value: "#{@model.id}$!#{field}"
    end

    sorts.each.with_index do |sort, index|
      select_h_select_option '.viz-sort .ci-sort-column-select', label: sort
    end

    safe_click('.ci-explorer-control-get-results')
    wait_for_report_data
    sleep 0.5

    safe_click('.ci-save-ds-based-report')
    wait_for_element_load('[data-hui-section="resolve-button"]')
    sleep 0.5

    fill_text('.h-input', report_data[:title])
    sleep 0.5
    page.find('[data-hui-section="resolve-button"]').click
    sleep 0.5
  end

  def wait_for_dashboard_widget_load
    wait_for_element_load('.h-dashboard-widget', 20)
    wait_for_widget_load
  end

  def edit_widget_report
    page.find('.h-dashboard-widget').hover
    safe_click('.ci-widget-controls-trigger')
    safe_click('.ci-edit-report-widget')
    wait_for_submit_generate
  end

  context 'Disabled dataset >' do
    before do
      FeatureToggle.toggle_global('data_sets:enabled', false)
      qlogin(admin, dashboard_link)
    end
    it 'should not show mode select' do
      expect(page).not_to have_selector('.ci-mode-select')
    end
  end

  context 'Enabled dataset >' do
    before do
      DataSourceVersions::SchemaSynchronizationService.new(ds).execute
      FeatureToggle.toggle_global('data_sets:enabled', true)
      FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
      FeatureToggle.toggle_global('data_models:new_sql_generation', true)
      FeatureToggle.toggle_global('data_models:manager', true)
      FeatureToggle.toggle_global(::Dashboard::FT_V3_CONVERSION, true)

      @model = create_data_modeling_model_from_table(ds, 'products')
      @dataset = DataSet.create_from_models(data_models: [@model], owner_id: admin.id, title: 'New dataset', category_id: 0,
                                            data_source_id: @model.data_source_id, tenant_id: admin.tenant_id,)
      @dataset.save!
    end

    context 'with dashboard timezone' do
      before do
        FeatureToggle.toggle_global(::Tenant::FT_NEW_TIMEZONE_CONFIG, true)
        FeatureToggle.toggle_global(::Timezone::Helper::FT_DASHBOARD_TIMEZONE, true)
        FeatureToggle.toggle_global('viz_setting:stricter_format_string_to_date', true)

        admin.tenant.settings[:time_zone] = 'Etc/UTC'
        admin.tenant.save!

        dashboard.settings[:timezone] = 'Asia/Singapore'
        dashboard.save!

        qlogin(admin, "#{dashboard_link}/add_report")
      end
      let(:values) do
        ['1', '2019-08-09 08:00:00', '2', '2019-08-09 08:00:00', '3', '2019-08-09 08:00:00', '4', '2019-08-09 08:00:00']
      end

      it 'should applies dashboard timezone' do
        page.find('.ci-dataset-mode').click
        wait_for_element_load('.ci-data-set-select')

        select_h_select_option('.ci-data-set-select', value: "#{@dataset.id}ds")

        expect(page.first('.query-processing-timezone').text).to eq('Asia/Singapore')
        select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select .ci-empty-field', value: "#{@model.id}$!id"
        select_h_select_option '.viz-section:nth-child(1) .ci-viz-field-select .ci-empty-field', value: "#{@model.id}$!created_at"
        select_h_select_option '.viz-sort .ci-sort-column-select', label: "Id"
        safe_click('.ci-explorer-control-get-results')

        wait_for_report_data

        wait_expect(values) do
          page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell:not(.ag-column-first)').map(&:text) }.flatten
        end
      end

      context 'view dashboard widget' do
        it 'should applies dashboard timezone' do
          create_widget(fields: ['id', 'created_at'], sorts: ['Id'])
          wait_for_widget_load

          accept_alert do
            visit dashboard_link
          end
          wait_for_dashboard_widget_load
          wait_for_report_data
          wait_expect(values) do
            page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell:not(.ag-column-first)').map(&:text) }.flatten
          end
        end
      end

      context 'explore dashboard widget' do
        include_context 'timezone_dynamic_dashboard'

        it 'should show data using the selected timezone' do
          FeatureToggle.toggle_global('viz_result:queue_update_v3', true)
          qlogin(admin, "/dashboards/v3/#{timezone_dashboard.id}")
          wait_for_widget_load
          search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
          select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
          wait_for_widget_load

          page.first('.ci-report-widget').hover
          safe_click('.hui-btn [data-icon="explore"]')
          expect(page.first('.ci-data-exploration-modal .query-processing-timezone').text).to eq('Asia/Tokyo')

          wait_for_element_load '.ci-data-exploration-modal .ci-table-report-data'
          rows = page.all('.ci-data-exploration-modal .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
          expect(rows).to match_array([
            '10 2021-10-21T01:30:00.000+00:00 2021-10-20',
            '20 2021-10-21T08:00:00.000+00:00 2021-10-20',
            '30 2021-10-21T21:00:00.000+00:00 2021-10-21',
          ])
        end

        context 'line chart' do
          let (:timezone_viz_setting) do
            create(
              :viz_setting,
              viz_type: 'line_chart',
              fields: {
                series: {},
                x_axis: {
                  path_hash: { model_id: query_data_model.id, field_name: 'time' },
                  format: { type: 'auto' },
                  type: 'datetime',
                  transformation: nil,
                },
                y_axes: [
                  {
                    columns: [
                      {
                        aggregation: 'count',
                        path_hash: { model_id: query_data_model.id, field_name: 'val' },
                        format: { type: 'auto' },
                        type: 'number',
                      },
                    ],
                  },
                ],
              },
              format: {},
              filters: timezone_viz_setting_filters,
              settings: {
                legend: {},
                y_axes: [{}],
              },
            )
          end

          it 'should show data in tabular data using the selected timezone' do
            FeatureToggle.toggle_global('viz_result:queue_update_v3', true)
            qlogin(admin, "/dashboards/v3/#{timezone_dashboard.id}")
            wait_for_widget_load
            search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
            select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
            wait_for_widget_load

            page.first('.ci-report-widget').hover
            safe_click('.hui-btn [data-icon="explore"]')
            expect(page.first('.ci-data-exploration-modal .query-processing-timezone').text).to eq('Asia/Tokyo')
            wait_for_all_holistics_loadings
            safe_click('.ci-tab-toggle', text: 'Table Data')

            wait_for_element_load '.ci-data-exploration-modal .__tab-content .ci-table-report-data'
            rows = page.all('.ci-data-exploration-modal .__tab-content .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
            expect(rows).to match_array([
              '2021-10-21T01:30:00.000+00:00 1',
              '2021-10-21T08:00:00.000+00:00 1',
              '2021-10-21T21:00:00.000+00:00 1',
            ])
          end
        end
      end


      context 'edit widget report' do
        it 'should applies dashboard timezone' do
          create_widget(fields: ['id', 'created_at'], sorts: ['Id'])
          wait_for_widget_load

          accept_alert do
            visit dashboard_link
          end
          wait_for_dashboard_widget_load

          edit_widget_report

          expect(page.first('.query-processing-timezone').text).to eq('Asia/Singapore')

          safe_click('.ci-explorer-control-get-results')
          wait_for_all_ajax_requests
          wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'

          wait_expect(values) do
            page.all('[data-ci="ci-ag-grid-data-table"] .ag-row .ag-cell:not(.ag-column-first)').map(&:text)
          end

          safe_click('.ci-save-ds-based-report')
          wait_for_all_ajax_requests
          wait_for_dashboard_widget_load
          wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'

          wait_expect(values) do
            page.all('[data-ci="ci-ag-grid-data-table"] .ag-row .ag-cell:not(.ag-column-first)').map(&:text)
          end
        end

        it 'should keep applied default row limit' do
          FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, true)

          create_widget(fields: ['id', 'created_at'], sorts: ['Id'])
          wait_for_widget_load

          accept_alert do
            visit dashboard_link
          end
          wait_for_dashboard_widget_load
          wait_for_report_data

          edit_widget_report

          expect(page.find('.ci-limit-select-toggle').text).to eq 'No limit'
        end

        context 'change dataset' do
          include_context 'query_model_dataset_based_report'

          let(:admin) { get_test_admin }
          let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
          let(:qr) { query_model_dataset_based_report }

          let!(:widget) do
            dw = DashboardWidget.from_adhoc_report(qr, dashboard.id)
            dw.save!
            dw
          end
          let(:url) { dashboard_path(dashboard) }

          before do
            FeatureToggle.toggle_global('data_models:explore_controls', true)
          end

          it 'can change dataset when enabled feature toggle' do
            safe_login(admin, url)
            edit_widget_report

            wait_for_element_load('.ci-data-set-select')

            select_h_select_option('.ci-data-set-select', value: "#{@dataset.id}ds")
            wait_expect(true) { page.has_content?('Changing the dataset will erase the current report content') }
          end

        end
      end
    end

    context 'products model with price field >' do
      before do
        qlogin(admin, "#{dashboard_link}/add_report")
      end

      it 'should show mode select' do
        expect(page).to have_selector('.ci-mode-select')
      end

      it 'should create and save widget successfully' do
        create_widget

        wait_expect(1) { DashboardWidget.count }

        new_widget = DashboardWidget.last
        expect(new_widget.source_id).to eq QueryReport.last.id
        expect(new_widget.dashboard_id).to eq dashboard.id
      end

      context 'Edit widget report' do
        it 'should be successfully saved and changed' do
          create_widget
          wait_for_widget_load

          accept_confirm do
            visit dashboard_link
          end
          wait_for_dashboard_widget_load
          wait_for_report_data
          expect(page).to have_content report_data[:title]
          expect(QueryReport.last.viz_setting.fields[:table_fields].map { |f| f[:path_hash].rsk }).to eq(
            [
              { model_id: @model.id, field_name: 'price' },
            ],
          )

          edit_widget_report
          safe_click('.editable-span')
          fill_text('.ci-title-input', report_data[:edited_title])
          page.all('.ci-viz-field-select .h-icon[data-icon="cancel"]').last.click
          select_h_select_option '.viz-section:nth-child(1) .ci-empty-field', value: "#{@model.id}$!name"
          safe_click('.ci-save-ds-based-report')
          expect_notifier_content /saved successfully/

          # redirected to dashboard after saved
          wait_for_dashboard_widget_load
          expect(page).to have_content report_data[:edited_title]

          wait_for_report_data
          expect(QueryReport.last.viz_setting.fields[:table_fields].map { |f| f[:path_hash].rsk }).to eq(
            [
              { model_id: @model.id, field_name: 'name' },
            ],
          )
        end

        it 'should not run new queries on page load' do
          create_widget
          wait_for_widget_load

          accept_alert do
            visit dashboard_link
          end
          wait_for_dashboard_widget_load
          wait_for_report_data

          PostgresCache.flushall
          jobs_count = Job.count

          edit_widget_report
          wait_for_all_ajax_requests

          expect(Job.count).to eq(jobs_count)
        end
      end
    end
  end
end
