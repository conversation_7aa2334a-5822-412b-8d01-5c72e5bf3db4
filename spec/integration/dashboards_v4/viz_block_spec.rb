# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Viz block', :js do
  context 'Export' do
    include_context 'dashboards_v4'

    let(:block_views) do
      {
        'v1' => { 'layer' => 0, 'position' => { 'h' => 330, 'w' => 300, 'x' => 100, 'y' => 100 } },
      }
    end

    def test_download_ext(ext, dashboard: dashboard_with_filter)
      qlogin(admin, "/dashboards/v4/#{dashboard.id}")
      wait_for_element_load do
        page.find_by_id('block-v1')
      end
      page.find_by_id('block-v1').hover
      safe_click('[data-ci="ci-more-dropdown"]')
      page.find('.ci-block-export').hover
      safe_click(".ci-#{ext}-export")
      wait_expect(true, 30) do
        raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

        page.all('.ci-export-success').present?
      end
    end

    shared_examples 'export pdf' do
      it 'export pdf success' do
        test_download_ext('pdf')
        metadata = test_download_link
        expect(metadata.base_uri.to_s).to match(/\.pdf$/)
      end
    end

    context 'pdf' do
      it_behaves_like 'export pdf'
      context 'should export pdf with hide label setting' do
        let(:filter_block) { nil }
        let(:setting_viz_block_table) do
          { 'hide_label' => true, 'hide_controls' => false }
        end

        it_behaves_like 'export pdf'
      end
    end

    it 'exports block to xlsx' do
      test_download_ext('xlsx')
      metadata = test_download_link
      expect(metadata.base_uri.to_s).to match(/\.xlsx$/)
    end

    context 'with csv export' do
      before do
        FeatureToggle.toggle_global('use_new_viz_exporter_to_export_csv', true)
        FeatureToggle.toggle_global(Excel::Formats::Value::FT_FORMAT_TIMESTAMP, true)
      end

      it 'exports and results raw data' do
        test_download_ext('csv', dashboard: dashboard_table_with_formatted_data)
        file = test_download_link
        expect(file.base_uri.to_s).to match(/\.csv$/)
        # header and first row
        expect(file.read.include?("Date And Time,Value,Date And Time\n2020-02-03 01:02:04 +0000,20,2020-02-03 01:02:04 +0000")).to be_truthy
      end

      it 'applies data format' do
        test_download_ext('csv-formatted-data', dashboard: dashboard_table_with_formatted_data)
        file = test_download_link

        # header and first row
        expect(file.read.include?("Date And Time,Value,Date And Time\n\"Feb 03, 2020\",20.00,2020-02-03 01:02:04\n")).to be_truthy
      end

      it 'can export with formatted data when exploring dataset' do
        qlogin(admin, "/dashboards/v4/#{dashboard_table_with_formatted_data.id}")
        wait_for_element_load do
          page.find_by_id('block-v1')
        end
        page.find_by_id('block-v1').hover
        safe_click('.hui-btn [data-icon="explore"]')

        safe_click '.ci-explorer-control-get-results'
        wait_for_all_holistics_loadings

        safe_click('.ci-export-btn')
        safe_click('.ci-csv-formatted-data')
        file = test_download_link

        # header and first row
        expect(file.read.include?("Date And Time,Value,Date And Time\n\"Feb 03, 2020\",20.00,2020-02-03 01:02:04\n")).to be_truthy
      end
    end
  end

  context 'with timezone' do
    include_context 'dashboard_v4_timezone_context'
    include_context 'timezone_dynamic_dashboard'

    let(:tz) { 'Asia/Bangkok' }
    let(:timezone_canvas_dashboard) do
      create_dashboard_form_viz_setting(timezone_viz_setting, query_model_data_set.id, tz)
    end

    before do
      FeatureToggle.toggle_global(Viz::Constants::FT_TABLE_V2, true)
    end

    it 'add viz block: should display data using the selected timezone' do
      def select_viz_field(model_name, field_name)
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', value: "#{model_name}$!#{field_name}")
      end

      safe_login(admin, dashboard_path(timezone_canvas_dashboard))

      safe_click('[data-ci="ci-edit-canvas-dashboard"]')

      wait_for_widget_load
      search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
      select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
      wait_for_widget_load

      wait_and_click('[data-ci="add-viz-block"]')
      wait_for_element_load('.h-data-set-report-editor')
      expect(page.first('.h-data-set-report-editor .query-processing-timezone').text).to eq('Asia/Tokyo')

      select_viz_field(query_data_model.id, 'val')
      select_viz_field(query_data_model.id, 'time')
      select_viz_field(query_data_model.id, 'date')

      safe_click('.h-data-set-report-editor [data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load '.h-data-set-report-editor .ci-table-report-data'
      rows = page.all('.h-data-set-report-editor .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
      expect(rows).to contain_exactly('10 2021-10-21 01:30:00 2021-10-20', '20 2021-10-21 08:00:00 2021-10-20',
                                      '30 2021-10-21 21:00:00 2021-10-21',)
    end
  end

  context 'with table theme' do
    include_context 'dashboard_v4_timezone_context'
    include_context 'timezone_dynamic_dashboard'

    let(:tz) { 'Asia/Bangkok' }
    let(:timezone_canvas_dashboard) do
      create_dashboard_form_viz_setting(timezone_viz_setting, query_model_data_set.id, tz)
    end

    before do
      FeatureToggle.toggle_global(Viz::Constants::FT_TABLE_V2, true)
      FeatureToggle.toggle_global('ag-grid:data-table', true)
      FeatureToggle.toggle_global('out-of-sync:show-banner', true)
    end

    it 'ables to edit undefined vizTheme' do
      def select_viz_field(model_name, field_name)
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', value: "#{model_name}$!#{field_name}")
      end

      safe_login(admin, dashboard_path(timezone_canvas_dashboard))

      safe_click('[data-ci="ci-edit-canvas-dashboard"]')

      wait_for_widget_load
      safe_click('#block-v1')
      safe_click('[data-ci="block-v1-controls"] [data-ci="edit-block-btn"]')
      wait_for_element_load('.h-data-set-report-editor')

      safe_click('.h-data-set-report-editor [data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load '.h-data-set-report-editor .ci-table-report-data'

      safe_click('[data-ci="ci-viz-setting-tab-styles"]')
      safe_click('[data-ci="viz-field-table-color"] .color-picker')
      safe_click('.colors [style="background-color: rgb(37, 93, 212);"]')
      wait_for_viz_load
      sleep(1) # Ensure styles are fully applied before checking elements

      table_header = page.first('.h-data-set-report-editor .ag-header')
      expect(table_header.style('background').to_s).to include 'rgb(21, 68, 166)'
    end

    it 'should pending render table theme when out-of-sync' do
      def select_viz_field(model_name, field_name)
        select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', value: "#{model_name}$!#{field_name}")
      end

      safe_login(admin, dashboard_path(timezone_canvas_dashboard))

      safe_click('[data-ci="ci-edit-canvas-dashboard"]')

      wait_for_widget_load
      safe_click('#block-v1')
      safe_click('[data-ci="block-v1-controls"] [data-ci="edit-block-btn"]')
      wait_for_element_load('.h-data-set-report-editor')

      safe_click('.h-data-set-report-editor [data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load '.h-data-set-report-editor .ci-table-report-data'

      # check initial color
      table_header = page.first('.h-data-set-report-editor .ag-header')
      expect(table_header.style('background').to_s).to include 'rgb(245, 248, 250)'

      # Make the viz out-of-syc
      source_field = page.find('.ci-normal-field', text: 'Time')
      target_field = page.find('.ci-normal-field', text: 'Date')
      source_field.drag_to target_field

      safe_click('[data-ci="ci-viz-setting-tab-styles"]')
      safe_click('[data-ci="viz-field-table-color"] .color-picker')
      safe_click('.colors [style="background-color: rgb(37, 93, 212);"]')
      wait_for_viz_load
      sleep(1)

      # color unchanged, because the viz is out-of-sync
      table_header = page.first('.h-data-set-report-editor .ag-header')
      expect(table_header.style('background').to_s).to include 'rgb(245, 248, 250)'
    end
  end
end

describe 'Edit Viz block', :js do
  include_context 'canvas_dashboard_aml'

  it 'workses' do
    content = <<~STR
      Dashboard canvas {
        title: 'Empty dashboard 4.0'
        view: LinearLayout {
        }
        block f1: FilterBlock {
          label: 'order status'
          type: 'field'
          source: FieldFilterSource {
            dataset: ecommerce
            field: ref('data_modeling_orders', 'status')
          }
          default {
            operator: 'is'
            value: []
          }
        }
        block v1: VizBlock {
          label: 'Test 2'
          viz: DataTable {
            dataset: ecommerce
            fields: [
              VizFieldFull {
                ref: ref('data_modeling_products', 'status')
                format {
                  type: 'text'
                }
              },
              VizFieldFull {
                ref: ref('data_modeling_products', 'name')
                format {
                  type: 'text'
                }
              },
              VizFieldFull {
                ref: ref('data_modeling_products', 'price')
                format {
                  type: 'text'
                }
                aggregation: 'sum'
              },
            ]
            settings {
              show_row_number: true
            }
          }
        }
        interactions: [
        ]
      }
    STR

    work_flow.write_file(canvas_filename, content, baseline: work_flow.read_file(canvas_filename)[:oid])
    safe_login(admin, canvas_dashboard_url)
    wait_for_element_load('[data-ci="aml-dashboard-gui"]')
    if page.has_css?("[data-ci='canvas-dashboard-outdated-code-warning']")
      page.find('[data-ci="ci-toggle-CODE-mode"]').click
      wait_for_element_load('.aml-editor')
      page.find('[data-ci="ci-toggle-VISUAL-mode"]').click
    end
    wait_for_element_load('.dac-linear-layout')
    wait_for_all_ajax_requests
    page.find_by_id('block-v1').hover
    safe_click('[data-ci="block-v1-controls"] [data-ci="edit-block-btn"]')
    wait_for_element_load('.ci-viz-field-select .h-icon[data-icon="cancel"]')
    page.all('.ci-viz-field-select .h-icon[data-icon="cancel"]').first.click
    page.find('.ci-save-ds-based-report').click
    wait_for_all_ajax_requests
    wait_expect(["Name", "Sum of Price"]) do
      page.all('#block-v1 .ag-header-cell:not(:first-child)').map(&:text)
    end
  end

  context 'with timezone' do
    include_context 'dashboard_v4_timezone_context'
    include_context 'timezone_dynamic_dashboard'

    let(:tz) { 'Asia/Bangkok' }
    let(:timezone_canvas_dashboard) do
      create_dashboard_form_viz_setting(timezone_viz_setting, query_model_data_set.id, tz)
    end

    it 'displays data using the selected timezone' do
      safe_login(admin, dashboard_path(timezone_canvas_dashboard))

      safe_click('[data-ci="ci-edit-canvas-dashboard"]')

      wait_for_widget_load
      search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
      select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
      wait_for_widget_load

      safe_click('#block-v1')
      safe_click('[data-ci="block-v1-controls"] [data-ci="edit-block-btn"]')

      wait_for_element_load('.h-data-set-report-editor')
      expect(page.first('.h-data-set-report-editor .query-processing-timezone').text).to eq('Asia/Tokyo')

      safe_click('.h-data-set-report-editor [data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load '.h-data-set-report-editor .ci-table-report-data'
      rows = page.all('.h-data-set-report-editor .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
      expect(rows).to contain_exactly('10 2021-10-21T01:30:00.000+00:00 2021-10-20',
                                      '20 2021-10-21T08:00:00.000+00:00 2021-10-20', '30 2021-10-21T21:00:00.000+00:00 2021-10-21',)
    end
  end
end
