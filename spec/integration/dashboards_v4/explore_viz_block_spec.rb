# frozen_string_literal: true

# typed: false

require 'rails_helper'

describe 'Canvas Dashboard Viz Block Exploration', :js do
  include_context 'canvas_dashboard'

  it 'applies dashboard filters' do
    canvas_dashboard.definition['blocks'][1]['filter']['default_condition'] =
      { 'operator' => 'is', 'values' => ['expired'] }
    canvas_dashboard.save!

    safe_login(admin, dashboard_path(canvas_dashboard))
    wait_for_element_load('#block-v1')
    wait_for_all_ajax_requests

    page.find_by_id('block-v1').hover
    safe_click('.hui-btn [data-icon="explore"]')
    wait_for_element_load('.dac-explore-viz-block-modal')
    expect(page.find('.viz-section.static-filters').text).to include("Status\n  is \"expired\"")
  end

  context 'with timezone' do
    include_context 'dashboard_v4_timezone_context'
    include_context 'timezone_dynamic_dashboard'

    let(:tz) { 'Asia/Bangkok' }
    let(:timezone_canvas_dashboard) do
      create_dashboard_form_viz_setting(timezone_viz_setting, query_model_data_set.id, tz)
    end

    it 'displays data using the selected timezone' do
      safe_login(admin, dashboard_path(timezone_canvas_dashboard))

      wait_for_widget_load
      search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
      select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
      wait_for_widget_load

      page.find_by_id('block-v1').hover
      safe_click('.hui-btn [data-icon="explore"]')
      wait_for_element_load('.dac-explore-viz-block-modal')
      expect(page.first('.dac-explore-viz-block-modal .query-processing-timezone').text).to eq('Asia/Tokyo')

      safe_click('.dac-explore-viz-block-modal [data-ci="ci-explorer-control-get-results"]')
      wait_for_element_load '.dac-explore-viz-block-modal .ci-table-report-data'
      rows = page.all('.dac-explore-viz-block-modal .ag-row').map{ |row| row.all('.ag-cell:not(.ag-column-first)').map(&:text).join(' ') }.flatten
      expect(rows).to contain_exactly('10 2021-10-21T01:30:00.000+00:00 2021-10-20',
                                      '20 2021-10-21T08:00:00.000+00:00 2021-10-20', '30 2021-10-21T21:00:00.000+00:00 2021-10-21',)
    end
  end
end
