require 'rails_helper'

describe 'DAC', :js do
  include_context 'aml_studio_dev_mode'
  include_context 'aml_studio_dashboard'
  let(:user) { get_test_admin }
  let(:dashboard_url) { "/studio/projects/1/explore/empty#{dashboard_extension}" }
  let(:dual_mode_selector) { '.ci-toggle-DUAL-mode' }
  let(:editor_selector) { '[data-ci="aml-monaco-editor"]' }

  before do
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    FeatureToggle.toggle_global('dynamic_filters:related_filters', true)
    FeatureToggle.toggle_global('viz_settings:explorer_control_v2', true)
  end

  it 'can enable linked filter interactions' do |_ex|
    safe_login user, dashboard_url
    wait_and_click(dual_mode_selector)
    wait_and_click('[data-ci="add-ic-block"]')

    filter_label = 'Test filter'
    fill_text('.ci-filter-label-input', filter_label)
    page.first('[data-ci="ci-data-set-model-field-select"]').click
    select_h_select_option('.ci-data-set-options', label: 'dataset_products')
    select_h_select_option('.ci-data-model-field-options', value: 'products$!id')
    page.find('.ci-data-set-model-field-select-button').click
    page.find('.ci-submit').click

    wait_and_click('[data-ci="add-ic-block"]')
    fill_text('.ci-filter-label-input', filter_label)
    page.first('[data-ci="ci-data-set-model-field-select"]').click
    select_h_select_option('.ci-data-set-options', label: 'dataset_products')
    select_h_select_option('.ci-data-model-field-options', value: 'products$!name')
    page.find('.ci-data-set-model-field-select-button').click

    page.find('.ci-related-filter-mappings [data-hui-comp="switch"]', text: 'Disabled').click
    safe_click('.ci-filter-mapping-select:nth-child(1) .ci-mapping-toggle')
    expect(page.find('.ci-filter-mapping-select:nth-child(1) .ci-mapped-field-select').text).to eq("Products\nName")
  end

  context 'reporting dashboard' do
    include_context 'dashboards_v4'

    context 'with timezone' do
      let!(:tenant) do
        tenant = get_test_tenant
        tenant.settings[:time_zone] = 'Asia/Singapore'
        tenant.save!
        tenant
      end

      before do
        FeatureToggle.toggle_global(Tenant::FT_NEW_TIMEZONE_CONFIG, true)
        FeatureToggle.toggle_global(Timezone::Helper::FT_DASHBOARD_TIMEZONE, true)
      end

      it 'return tenant timezone when dashboard not have timezone' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_no_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_expect(true) { page.first('[data-ci="ci-allow-change-timezone"]').text == 'Asia/Singapore' }
      end

      it 'return dashboard timezone when dashboard have timezone' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_expect(true) { page.first('[data-ci="ci-allow-change-timezone"]').text == 'Etc/UTC' }
      end

      it 'can change timezone dashboard' do
        dashboard_table_timezone.definition['settings']['allow_timezone_change'] = true
        dashboard_table_timezone.save

        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        # dashboard timezone is Etc/UTC
        wait_expect(true) { page.first('.ag-cell:not(.ag-column-first)').text == '2022-04-03 01:02:04' }
        search_h_select('.ci-dashboard-timezone', text: 'Asia/Singapore')
        select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Singapore')
        # after change timezone is Asia/Singapore
        wait_for_all_holistics_loadings # widget loaded
        wait_expect(true) { page.first('.ag-cell:not(.ag-column-first)').text == '2022-04-03 09:02:04' }
      end
    end

    context 'cache duration' do
      let!(:tenant) do
        tenant = get_test_admin.tenant
        # 6 hour
        tenant.settings[:report_cache_duration] = 360
        tenant.save!
        tenant
      end

      it 'default dashboard cache setting is tenant setting' do
        dashboardv4_with_settings.definition['settings']['autorun'] = true
        dashboardv4_with_settings.definition['settings']['cache_duration'] = nil
        dashboardv4_with_settings.save!

        safe_login(get_test_admin, dashboard_path(dashboardv4_with_settings))

        wait_for_element_load '.ci-table-report-data'

        wait_and_click('[data-ci="dashboard-preferences-button"]')
        wait_and_click('[data-ci="ci-tab-cache"]')
        wait_expect(true) { page.first('.ci-cache-duration').text == '6 hours' }
      end

      it 'show dashboard cache setting' do
        dashboardv4_with_settings.definition['settings']['autorun'] = true
        dashboardv4_with_settings.definition['settings']['cache_duration'] = 100
        dashboardv4_with_settings.save!

        safe_login(get_test_admin, dashboard_path(dashboardv4_with_settings))

        wait_for_element_load '.ci-table-report-data'

        wait_and_click('[data-ci="dashboard-preferences-button"]')
        wait_and_click('[data-ci="ci-tab-cache"]')
        wait_expect(true) { page.first('.ci-cache-duration').text == '100 minutes' }
      end

      context 'Report cache tenant setting is nil' do
        before do
          tenant.settings[:report_cache_duration] = nil
          tenant.save!
        end

        it 'show dashboard cache setting to default of tenant setting' do
          dashboardv4_with_settings.definition['settings']['autorun'] = true
          dashboardv4_with_settings.definition['settings']['cache_duration'] = nil
          dashboardv4_with_settings.save!

          safe_login(get_test_admin, dashboard_path(dashboardv4_with_settings))

          wait_for_element_load '.ci-table-report-data'

          wait_and_click('[data-ci="dashboard-preferences-button"]')
          wait_and_click('[data-ci="ci-tab-cache"]')
          wait_expect(true) { page.first('.ci-cache-duration').text == '24 hours' }
        end
      end
    end

    context 'reset default filter on dashboard' do
      before do
        FeatureToggle.toggle_global('new_navigation_node', true)
      end

      it 'reset filter work' do
        safe_login(get_test_admin, dashboard_path(dashboard_with_filter))
        wait_for_element_load '.ci-table-report-data'
        wait_for_element_load '.ci-viz-filter-values'
        # enter filter
        page.find('.dac-ic-block .ci-viz-filter-values input').fill_in(with: '20')
        page.send_keys :enter
        safe_click('[data-ci="ci-submit-filters-btn"]')

        # open filters panel
        safe_click('[data-ci="ci-controls-panel"]')

        # click reset filter button
        safe_click('[data-ci="ci-reset-all-controls"]')
        wait_expect '' do
          page.find('.dac-ic-block .ci-value-select').text
        end

        # button will be disabled if same default filter
        wait_expect true do
          !!page.find('[data-ci="ci-reset-all-controls"]').style('disabled')['disabled']
        end
      end
    end

    context 'persit zoom to session dashboard' do
      before do
        FeatureToggle.toggle_global('new_navigation_node', true)
      end

      it 'can save zoom dashboard' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_for_element_load '[data-ci="ci-zoom-dropdown"]'
        safe_click '[data-ci="ci-zoom-dropdown"]'
        safe_click '.ci-zoom-in'
        old_zoom = page.find('[data-ci="ci-zoom-dropdown"]').text

        # refresh page
        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_for_element_load '[data-ci="ci-zoom-dropdown"]'
        new_zoom = page.find('[data-ci="ci-zoom-dropdown"]').text
        expect(old_zoom).to eq(new_zoom)
      end

      it 'keep size fit to width when resize window' do
        Capybara.current_window.resize_to 1200, 800
        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_for_element_load '[data-ci="ci-zoom-dropdown"]'
        safe_click '[data-ci="ci-zoom-dropdown"]'
        safe_click '.ci-zoom-to-fit'
        old_zoom = page.find('[data-ci="ci-zoom-dropdown"]').text

        Capybara.current_window.resize_to 1500, 800
        new_zoom = page.find('[data-ci="ci-zoom-dropdown"]').text
        wait_expect(true) { new_zoom != old_zoom }
      end

      it 'keep size fit to page when resize window' do
        Capybara.current_window.resize_to 1200, 800
        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_for_element_load '[data-ci="ci-zoom-dropdown"]'
        safe_click '[data-ci="ci-zoom-dropdown"]'
        safe_click '.ci-zoom-to-fit'
        old_zoom = page.find('[data-ci="ci-zoom-dropdown"]').text

        Capybara.current_window.resize_to 1500, 800
        new_zoom = page.find('[data-ci="ci-zoom-dropdown"]').text
        wait_expect(true) { new_zoom != old_zoom }
      end
    end

    context 'load default zoom dashboard' do
      before do
        dashboard_table_timezone.definition['views'][0]['default_zoom'] = 1.5
        dashboard_table_timezone.save!
      end

      it 'can load default zoom dashboard' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
        wait_for_element_load '.ci-table-report-data'
        wait_for_element_load '[data-ci="ci-zoom-dropdown"]'
        wait_expect(true) { page.find('[data-ci="ci-zoom-dropdown"]').text == '150%' }
      end
    end

    context 'option export modal' do
      before do
        FeatureToggle.toggle_global('integrations:slack', true)
        FeatureToggle.toggle_global('slack_schedules:dashboard', true)
        FeatureToggle.toggle_global('integrations:sftp', true)
      end

      it 'render correct modal by slack type' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_no_timezone))
        wait_for_element_load '.ci-table-report-data'

        wait_for_element_load('[data-ci="ci-export-dropdown"]')
        safe_click('[data-ci="ci-export-dropdown"]')
        safe_click('.ci-schedule-slack-dest')

        expect(page).to have_text(/Slack integration/, minimum: 1)
      end

      it 'render correct modal by sftp type' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_no_timezone))
        wait_for_element_load '.ci-table-report-data'

        wait_for_element_load('[data-ci="ci-export-dropdown"]')
        safe_click('[data-ci="ci-export-dropdown"]')
        safe_click('.ci-schedule-sftp-dest')

        # SFTP Settings in confing modal
        expect(page).to have_text(/SFTP Settings/, minimum: 1)
      end

      it 'render correct modal by google sheets type' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_no_timezone))
        wait_for_element_load '.ci-table-report-data'

        wait_for_element_load('[data-ci="ci-export-dropdown"]')
        safe_click('[data-ci="ci-export-dropdown"]')
        safe_click('.ci-schedule-gsheet-dest')

        expect(page).to have_text(/Spreadsheet/, minimum: 1)
      end

      it 'render correct modal by email type' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_no_timezone))
        wait_for_element_load '.ci-table-report-data'

        wait_for_element_load('[data-ci="ci-export-dropdown"]')
        safe_click('[data-ci="ci-export-dropdown"]')
        safe_click('.ci-schedule-email-dest')

        expect(page).to have_text(/Recipients/, minimum: 1)
      end
    end

    context 'show widget outside canvas in edit mode' do
      before do
        definition_aml = <<~STR
          Dashboard unnamed {
            title: 'Unnamed Dashboard'
            description: ''''''
            view: CanvasLayout {
              label: 'View 1'
              height: 800
              block v1 {
                position: pos(-320, 0, 300, 330)
              }
            }
            block v1: VizBlock {
              viz: DataTable {
                dataset: 'test_data_set'
                fields: [
                  VizFieldFull {
                    ref: ref('new_sql_model', 'date_and_time')
                    format {
                      type: 'datetime'
                    }
                    uname: 'field_datetime'
                  },
                  VizFieldFull {
                    ref: ref('new_sql_model', 'value')
                    aggregation: 'sum'
                    format {
                      type: 'number'
                      pattern: 'inherited'
                    }
                    uname: 'field_value_number'
                  }
                ]
              }
            }
          }


        STR
        dashboard_table_no_timezone.update!(definition_aml: definition_aml)
        FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
      end

      let(:block_views) do
        {
          'v1' => { 'layer' => 0, 'position' => { 'h' => 330, 'w' => 300, 'x' => -320, 'y' => 0 } },
        }
      end

      let(:viz_field_datetime) do
        {
          'type' => 'datetime', 'format' => { 'type' => 'timestamp' },
          'path_hash' => { 'model_id' => query_data_model.id, 'field_name' => 'date_and_time' },
          'uuid' => 'field_datetime',
        }
      end

      let(:viz_field_value) do
        {
          'type' => 'number', 'format' => { 'type' => 'number', 'format' => { 'pattern' => 'inherited' } },
          'path_hash' => { 'model_id' => query_data_model.id, 'field_name' => 'value' },
          'uuid' => 'field_value_number',
        }
      end

      it 'works' do
        safe_login(get_test_admin, dashboard_path(dashboard_table_no_timezone))
        wait_for_element_load('[data-icon="edit"]')
        wait_expect(false) do
          page.has_css?('#block-v1')
        end

        safe_click('[data-icon="edit"]')
        wait_for_element_load('[data-ci="add-text-block"]')
        wait_for_element_load('[data-ci="ci-zoom-dropdown"]')
        safe_click('[data-ci="ci-zoom-dropdown"]')
        # zoom out 3 times to show widget outside canvas
        safe_click('.ci-zoom-out')
        safe_click('.ci-zoom-out')
        safe_click('.ci-zoom-out')

        # widget show in left of the dashboard
        wait_expect(true) do
          page.has_css?('#block-v1')
        end
      end
    end

    it 'expand block when click title' do
      safe_login(get_test_admin, dashboard_path(dashboard_table_timezone))
      wait_for_element_load '.ci-table-report-data'
      safe_click('.dac-viz-block-label')
      wait_expect(true) { !!page.find('.ci-expanded-block').visible? }
    end
  end
end

describe 'ViewDAC', :js do
  include_context 'test_tenant'
  include_context 'simple_query_model_dataset'

  before do
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    # Tabular Data Lazy is enabled by default in production
    FeatureToggle.toggle_global(Viz::Constants::FT_TABULAR_DATA_LAZY_TABLE_DATA, true)
    FeatureToggle.toggle_global('data_models:explore_controls', true)
    ThreadContext.set(:current_user, admin)
  end

  let(:dashboard) do
    # load and patch definition
    raw_definition = File.read("#{File.dirname(__FILE__)}/sample_dashboard_definition.json")
    definition = JSON.parse(raw_definition)
    definition['blocks'].each do |block|
      block['viz']['dataset_id'] = query_model_data_set.id if block['type'] == 'VizBlock'
    end

    create(
      :dashboard,
      {
        category_id: 0,
        version: 4,
        title: 'Test dashboard',
        owner_id: admin.id,
        tenant_id: admin.tenant_id,
        definition: definition,
        definition_aml: 'Dashboard test_dashboard {}',
      },
    )
  end

  def resize_and_load_dashboard(url_suffix = '')
    Capybara.current_window.resize_to 1200, 800
    safe_login(admin, dashboard_path(dashboard) + url_suffix)
    wait_for_element_load('.h-dashboard-container') # dashboard loaded
  end

  it 'does not load blocks if enters expanded block directly' do
    # enter expanded block directly
    resize_and_load_dashboard('?_expd=v1&_fstate=o9ksW7')
    wait_for_all_holistics_loadings
    wait_for_all_ajax_requests

    # expect only this block to load
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1, wait: true)

    # collapse the block
    safe_click('[data-ci="collapse-block-btn"]')
    wait_for_all_holistics_loadings
    wait_for_all_ajax_requests

    # expect all blocks to load
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 4, wait: true)
  end

  it 'does not load blocks if enters exploration directly' do
    # enter expanded block directly
    resize_and_load_dashboard('?_expl=v1')

    wait_for_all_holistics_loadings
    wait_for_all_ajax_requests

    # expect only this block to load
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1, wait: true)
    wait_for_all_ajax_requests
    reset_ajax_requests

    # close exploration modal
    safe_click('[data-ci="close-modal"]')
    wait_for_all_holistics_loadings
    wait_for_all_ajax_requests
    # expect all blocks to load
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3, wait: true)
  end

  it 'displays executed query tab in expaneded widget after refresh cache' do
    # enter expanded block directly
    resize_and_load_dashboard
    # expect all block load
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3, wait: true)
    reset_ajax_requests

    wait_for_element_load('#block-v1')
    wait_for(true) do
      page.find_by_id('block-v1').hover
      safe_click('[data-ci="ci-more-dropdown"]')
    rescue StandardError => e
      e.inspect
    end

    safe_click('[data-ci="widget-refresh-cache"]')
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1, wait: true)
    reset_ajax_requests

    wait_for(true) do
      page.find_by_id('block-v1').hover
      safe_click('[data-ci="expand-block-btn"]')
    rescue StandardError => e
      e.inspect
    end
    expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1)

    wait_for_all_holistics_loadings
    expect(page).to have_css('.ci-tab-toggle', text: 'Executed SQL')
  end
end
