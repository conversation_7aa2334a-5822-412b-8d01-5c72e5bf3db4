# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'batch_submit_generate in Canvas Dashboard', js: true, otel: true do
  let!(:ds) do
    ds = get_test_ds.dup
    ds.name = 'test_ds'
    ds.save!
    ds
  end
  let(:current_user) { get_test_admin }
  let(:dashboard_uname) { 'sleep_dashboard' }
  let(:db_dashboard) do
    deploy_result # make sure deployment is already done
    Dashboard.find_by!(uname: dashboard_uname)
  end

  before do
    FeatureToggle.toggle_global(Tenant::FT_NEW_TIMEZONE_CONFIG, true)
    FeatureToggle.toggle_global(DataModel::FT_AQL, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_TABLE_V2, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_PIVOT_V2, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_LIMIT_100_PIVOT_COLUMNS, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_LIMIT_100_CHART_PIVOT_COLUMNS, true)
    FeatureToggle.toggle_global(DataModel::FT_PROCESS_DYNAMIC_MODELS, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, true)
    FeatureToggle.toggle_global(Viz::Constants::FT_BATCH_SUBMIT_GENERATE, true)

    # RFC-2619: not using ThreadContext cache at phase 1 so disable CompiledCache for this test
    #           Will re-enable after implementing the ThreadContext cache
    FeatureToggle.toggle_global(AmlCompiledCache::FT_USE_AML_COMPILED_CACHE, false)

    GlobalConfig.set(Viz::Constants::GC_BATCH_SUBMIT_GENERATE_MIN_BATCH_SIZE, 3)
  end

  include_context 'aml_studio_deployed' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/sleep'
    end
  end

  # NOTE: we could not test response streaming in this integration spec :(
  # So this is just a smoke and tracing test
  it 'works' do
    safe_login(current_user, "/studio/projects/#{db_dashboard.project_id}/explore/dashboards/sleep.page.aml")

    wait_for_element_load('#block-v2 .ci-viz-result', 20)

    wait_expect("A\nA\n1\nalice\nalice\n2\nbob\nbob") do
      page.find('#block-v1 .ci-viz-result').text.strip.gsub(/ +/, ',')
    end
    wait_expect("B\nB\n1\n1\n1\n2\n2\n2") do
      page.find('#block-v2 .ci-viz-result').text.strip.gsub(/ +/, ',')
    end

    hotel_tester = HOtel::IntegrationTester.new(database: hotel_frontend_db)
    viz_spans = hotel_tester.extract_frontend_db({ name: 'VizResult#updateFunc' }).values
    viz_spans.sort! { |s1, s2| s1.dig('otelSpan', 'startTime') <=> s2.dig('otelSpan', 'startTime')}
    expect(viz_spans.size).to eq(2)

    batch_spans = otel_finished_spans.select { |span| span.name.include? '#batch_submit_generate' }
    expect(batch_spans.size).to eq(1)
    batch_span = batch_spans[0]

    spawn_spans = otel_finished_spans.select { |span| span.name == 'BatchSubmitGenerate#spawn_submit_generate' }
    spawn_spans.sort! { |s1, s2| s1.start_timestamp <=> s2.start_timestamp }
    expect(spawn_spans.size).to eq(2)

    run_spans = otel_finished_spans.select { |span| span.name == 'BatchSubmitGenerate#run_submit_generate' }
    run_spans.sort! { |s1, s2| s1.attributes['h.request_id']&.to_i <=> s2.attributes['h.request_id']&.to_i }
    expect(run_spans.size).to eq(2)

    jobs = Job.last(2)

    viz_trace_ids = viz_spans.map { |s| s.dig('otelSpan', 'traceId') }
    expect(viz_trace_ids.uniq.size).to eq(2)

    viz_trace_ids.each_with_index do |viz_trace_id, i|
      # batch controller is in a different trace
      expect(HOtel::Utils.bin_to_hex(batch_span.trace_id)).not_to eq(viz_trace_id)

      # run and job are in the same trace
      expect(HOtel::Utils.bin_to_hex(run_spans[i].trace_id)).to eq(viz_trace_id)
      job = jobs.find do |j|
        trace_id = HOtel::Utils.bin_to_hex(
          HOtel::HSpan.current_span(j.otel_context).trace_id,
        )
        trace_id == viz_trace_id
      end
      expect(job).to be_present

      # spawn span links to original viz trace
      expect(spawn_spans[i].links.size).to eq(1)
      expect(HOtel::Utils.bin_to_hex(spawn_spans[i].links[0].span_context.trace_id)).to eq(viz_trace_id)
    end

    spawn_spans.each_with_index do |spawn_span, i|
      expect(spawn_span.trace_id).to eq(batch_span.trace_id)

      expect(run_spans[i].links.size).to eq(1)
      expect(run_spans[i].links[0].span_context.span_id).to eq(spawn_span.span_id)
    end

    # test when cached
    job_count = Job.where(source_type: 'DataModel').count
    otel_reset
    safe_login(current_user, "/studio/projects/#{db_dashboard.project_id}/explore/dashboards/sleep.page.aml")

    wait_for_element_load('#block-v2 .ci-viz-result', 20)

    wait_expect("A\nA\n1\nalice\nalice\n2\nbob\nbob") do
      page.find('#block-v1 .ci-viz-result').text.strip.gsub(/ +/, ',')
    end
    wait_expect("B\nB\n1\n1\n1\n2\n2\n2") do
      page.find('#block-v2 .ci-viz-result').text.strip.gsub(/ +/, ',')
    end

    batch_spans = otel_finished_spans.select { |span| span.name.include? '#batch_submit_generate' }
    expect(batch_spans.size).to eq(1)
    batch_span = batch_spans[0]

    expect(Job.where(source_type: 'DataModel').count).to eq(job_count) # should not spawn any new jobs
  end

  context 'max batch size = 1' do
    before do
      GlobalConfig.set(Viz::Constants::GC_BATCH_SUBMIT_GENERATE_MAX_BATCH_SIZE, 1)
    end

    it 'makes 2 request' do
      safe_login(current_user, "/studio/projects/#{db_dashboard.project_id}/explore/dashboards/sleep.page.aml")

      wait_for_element_load('#block-v2 .ci-viz-result', 20)

      batch_spans = otel_finished_spans.select { |span| span.name.include? '#batch_submit_generate' }
      expect(batch_spans.size).to eq(2)
    end
  end

  context 'in Reporting' do
    it 'does not use batch_submit_generate' do
      safe_login(current_user, dashboard_path(db_dashboard))

      wait_for_element_load('#block-v2 .ci-viz-result', 20)

      wait_expect("A\nA\n1\nalice\nalice\n2\nbob\nbob") do
        page.find('#block-v1 .ci-viz-result').text.strip.gsub(/ +/, ',')
      end
      wait_expect("B\nB\n1\n1\n1\n2\n2\n2") do
        page.find('#block-v2 .ci-viz-result').text.strip.gsub(/ +/, ',')
      end

      batch_spans = otel_finished_spans.select { |span| span.name.include? '#batch_submit_generate' }
      expect(batch_spans.size).to eq(0)

      non_batch_spans = otel_finished_spans.select { |span| span.name.include? '#submit_generate' }
      expect(non_batch_spans.size).to eq(2)
    end
  end
end
