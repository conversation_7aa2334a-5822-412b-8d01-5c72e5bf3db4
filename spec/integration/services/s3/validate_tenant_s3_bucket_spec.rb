# frozen_string_literal: true
# typed: false

require 'rails_helper'

# NOTE: This is actually a server-side unit test, but moved here because of occasional failures on CircleCI

describe S3::ValidateTenantS3Bucket, stable: true do
  let(:bucket_name) { 'haha' }
  let(:region) { 'us-west-1' }
  let(:access_key_id) { 'id' }
  let(:secret_access_key) { 'key' }
  let(:tenant_s3_bucket) do
    TenantS3Bucket.new(bucket_name: bucket_name, region: region, access_key_id: access_key_id,
                       secret_access_key: secret_access_key,)
  end

  def execute_validate
    tenant_s3_bucket_params = tenant_s3_bucket.as_json.rsk
    S3::ValidateTenantS3Bucket.execute(tenant_s3_bucket_params)
  end

  context 'invalid credentials' do
    it 'raises error' do
      expect { execute_validate }.to raise_error(/Invalid Access Key ID for the provided region/)
    end
  end

  context 'valid credentials' do
    let(:tenant_s3_bucket) do
      create(:tenant_s3_bucket, bucket_name: bucket_name, region: region)
    end

    context 'valid bucket and region' do
      let(:bucket_name) { ENV.fetch('S3_SECURE_EXPORT_TEST_BUCKET', nil) }
      let(:region) { 'ap-southeast-1' }

      it 'returns true' do
        VCR.use_cassette('exports/secure_export_with_tenant_config', match_requests_on: [:host, :path, :method]) do
          expect(execute_validate).to be true
        end
      end
    end

    context 'bucket not found' do
      let(:region) { 'ap-southeast-1' }

      it 'raises error' do
        VCR.use_cassette('exports/secure_export_with_tenant_config', match_requests_on: [:host, :path, :method]) do
          expect { execute_validate }.to raise_error(/does not exist/)
        end
      end
    end

    context 'invalid region' do
      let(:region) { 'cn-northwest-1' }
      let(:bucket_name) { ENV.fetch('S3_SECURE_EXPORT_TEST_BUCKET', nil) }

      it 'raises error' do
        expect { execute_validate }.to raise_error
      end

      context 'region does not exist' do
        let(:region) { 'con_meo' }

        it 'raises error' do
          expect { execute_validate }.to raise_error(/Invalid `:region` option was provided/)
        end
      end
    end

    context 'readonly bucket' do
      let(:bucket_name) { 'holistics-readonly-test' }
      let(:region) { 'ap-southeast-1' }

      it 'raises error' do
        VCR.use_cassette('exports/secure_export_with_tenant_config', match_requests_on: [:host, :path, :method]) do
          expect do
            execute_validate
          end.to raise_error(/This ID does not have write permission to bucket '#{bucket_name}'/)
        end
      end
    end
  end
end
