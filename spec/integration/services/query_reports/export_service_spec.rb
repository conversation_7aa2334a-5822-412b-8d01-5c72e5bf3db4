# typed: false
require 'rails_helper'

describe QueryReports::ExportService, stable: true do
  let (:tenant) {get_test_tenant}
  let (:user) {get_test_admin}
  let(:test_ds) {
    get_test_ds
  }
  let(:filters) {
    [{type: 'dropdown', dropdown_source: 'sql', data_source_id: test_ds.id, dropdown_sql: "values('sg', 'Singapore'), ('vn', 'Vietnam')", name: 'country'}]
  }

  before do
    @report = FactoryBot.create :query_report, title: 'Avengers Report', query: 'values(1)',
                                 tenant: tenant, data_source: test_ds
    @report.filters = filters
    @report.save

    @report
  end

  describe '#export' do
    let(:match_requests_on) { [:host, :path, :body, :method] }
    before do
      # if ENV['CIRCLECI']
      #   pending 'this test should not be run on CircleCi but on local machine having AWS S3 credentials in .env'
      #   fail 'run locally only'
      # end
      ENV['AWS_REGION'] = 'ap-southeast-1'
      ENV['AWS_ACCESS_KEY_ID'] = 'id'
      ENV['AWS_SECRET_ACCESS_KEY'] = 'key'
      Cache.flushdb
      Cache.set('', 0, 'haha')
      pg_create_pageviews(test_ds)
      @report.query = 'select count(*) from public.test_pageviews'
      @report.save

      preprocess_result = QueryReports::PreprocessQueryService.new(report: @report, user: user, params: {}).execute(use_cache: false).unwrap
      @cache_key = preprocess_result.cache_key

      allow_any_instance_of(QueryReports::WriteToS3).to receive(:make_s3_file_name).and_return(
        "#{@report.to_param}-#{@cache_key}.csv"
      )
      allow_any_instance_of(QueryReports::WriteToS3).to receive(:make_file_metadata).and_return(
        { 'cache-saved-at' => '2018-07-10T16:21:19+07:00', 'cache-expired-at' => '2018-07-10T16:31:19+07:00' }
      )
      allow_any_instance_of(Transports::S3Dest).to receive(:can_write?).and_return(true)

      FeatureToggle.toggle_global(TenantS3Bucket::TENANT_S3_BUCKET_FT, true)
      FeatureToggle.toggle_global(TenantS3Bucket::TENANT_S3_BUCKET_FWALL, true)
    end

    def expect_job_stats(job)
      expect(job.stats[:report_execute_time_s]).not_to be_nil
      expect(job.stats[:export_generate_time_s]).not_to be_nil
      expect(job.stats[:export_upload_time_s]).not_to be_nil
    end

    def export_and_get_output(with_job: true, bust_cache: false, job_stats: true, apply_data_format: false)
      params = {}
      if with_job
        job = @report.async(self_cache: true,).execute(user.id, {})
        params[:job_id] = job.id
      end
      params[:_force] = bust_cache
      params[:apply_data_format] = apply_data_format
      @report.async(self_cache: false,).export(user.id, params, { format: 'csv' })
      last_job = Job.last
      result = last_job.fetch_cache_data.rsk
      last_job.logs.each {|l| puts l.message.blue}
      expect_job_stats(last_job) if job_stats
      expect(result[:http_path][-3..-1]).to eq 'csv'
      expect(result[:http_path].include?('formatted')).to be true if apply_data_format

      ThreadContext.reset(:web)

      URI.open(result[:http_path]).read
    end

    def test_secure_export
      error = nil
      expect do
        begin
          export_and_get_output
        rescue => e
          error = e.message
          raise
        end
      end.to raise_error(OpenURI::HTTPError)
      # reject using public link to download files in secure bucket
      expect(error).to eq "403 Forbidden"

      # generate private link
      j = Job.last
      tenant_bucket = TenantS3Bucket.from_tenant(j.tenant_id)
      downloader = S3Downloader.new(
        bucket_name: j.data[:result_metadata][:bucket_name],
        object_key: j.data[:result_metadata][:object_key],
        s3_client_config: tenant_bucket.s3_client_config
      )
      url = downloader.private_path(expires_in: 3)
      parsed_output = CSV.parse(URI.open(url).read)
      # expect private link to return correct data
      expect(parsed_output).to match_array([['count'], ['8']])
      sleep 4 unless ENV['CIRCLECI'] # If env is circleci, it means the requests have already been recorded
      # expect private link to expire correcly and return error
      expect { URI.open(url).read }.to raise_error(OpenURI::HTTPError)
    end

    it 'exports correctly with job' do
      VCR.use_cassette('exports/normal_export', match_requests_on: [:host, :path, :body, :method]) do
        parsed_output = CSV.parse export_and_get_output
        expect(parsed_output).to match_array([['count'], ['8']])
      end
      VCR.use_cassette('exports/normal_export_2', match_requests_on: [:host, :path, :body, :method]) do
        parsed_output = CSV.parse export_and_get_output(job_stats: false)
        expect(Job.last.logs.any? {|l| l.message.include?('file exists on S3')}).to eq true
        expect(parsed_output).to match_array([['count'], ['8']])
      end
    end

    it 'exports correctly without job, even when cache expired' do
      # export once to create cache
      VCR.use_cassette('exports/export_without_job', match_requests_on: match_requests_on) do
        parsed_output = CSV.parse export_and_get_output(with_job: false)
        expect(Job.last.logs.any? {|l| l.message.include?('file exists on S3')}).to eq false
        expect(parsed_output).to match_array([['count'], ['8']])
      end

      # add data
      Connectors.from_ds(test_ds).exec_sql("insert into public.test_pageviews values ('2015-01-01', '1u', 'homepage', 10)")
      VCR.use_cassette('exports/export_without_job_2', match_requests_on: match_requests_on) do
        parsed_output = CSV.parse export_and_get_output(with_job: false, job_stats: false)
        expect(Job.last.logs.any? {|l| l.message.include?('file exists on S3')}).to eq true
        expect(parsed_output).to match_array([['count'], ['8']])
      end

      # expire the cache key by sleep
      ReportCache.set(@cache_key, 1, ReportCache.fetch(@cache_key), ReportCache.fetch_metadata(@cache_key))
      sleep 2
      VCR.use_cassette('exports/export_without_job_3', match_requests_on: match_requests_on) do
        parsed_output = CSV.parse export_and_get_output(with_job: false)
        expect(Job.last.logs.any? {|l| l.message.include?('file exists on S3')}).to eq false
        expect(parsed_output).to match_array([['count'], ['9']])
      end
    end

    it 'does not use existing s3 file if the metadata of file and cache mismatch' do
      # export once to create cache
      VCR.use_cassette('exports/export_without_job', match_requests_on: match_requests_on) do
        parsed_output = CSV.parse export_and_get_output(with_job: false)
      end

      Connectors.from_ds(test_ds).exec_sql("insert into public.test_pageviews values ('2015-01-01', '1u', 'homepage', 10)")
      ReportCache.clear(@cache_key)
      # rerun for new cache
      @report.run_and_process(user, {}, bust_cache: true, data_need: ::Caching::PostgresCache::SqlRunner::FetchReport::EXPORT_DATA)
      expect_any_instance_of(Transports::S3Dest).to receive(:load_existing_file).and_return(false)
      VCR.use_cassette('exports/mismatch_metadata', match_requests_on: match_requests_on) do
        parsed_output = CSV.parse export_and_get_output(with_job: false)
        expect(Job.last.logs.any? {|l| l.message.include?('file exists on S3')}).to eq false
        expect(parsed_output).to match_array([['count'], ['9']])
      end
    end

    it 'uses a different cache object for formatted data exporting' do
      allow_any_instance_of(QueryReports::WriteToS3).to receive(:make_s3_file_name).and_call_original

      VCR.use_cassette('exports/export_with_formatted_data', match_requests_on: match_requests_on) do
        export_and_get_output(job_stats: false, apply_data_format: true)
      end

      VCR.use_cassette('exports/export_with_formatted_data_2', match_requests_on: match_requests_on) do
        export_and_get_output(job_stats: false, apply_data_format: true)
        expect(Job.last.logs.any? { |l| l.message.include?('file exists on S3') }).to be true
      end
    end

    it 'exports correcly with _force option' do
      # export once to create cache
      VCR.use_cassette('exports/export_without_job', match_requests_on: match_requests_on) do
        parsed_output = CSV.parse export_and_get_output(with_job: false)
      end

      VCR.use_cassette('exports/export_with_force', match_requests_on: match_requests_on) do
        Connectors.from_ds(test_ds).exec_sql("insert into public.test_pageviews values ('2015-01-01', '1u', 'homepage', 10)")
        parsed_output = CSV.parse export_and_get_output(with_job: false, bust_cache: true)
        expect(Job.last.logs.any? {|l| l.message.include?('file exists on S3')}).to eq false
        expect(parsed_output).to match_array([['count'], ['9']])
      end
    end

    it 'can export with secure_bucket feature_toggle' do
      FeatureToggle.toggle_global('exports:secure_bucket', true)
      VCR.use_cassette('exports/secure_export', match_requests_on: match_requests_on) do
        test_secure_export
      end
      FeatureToggle.toggle_global('exports:secure_bucket', false)
    end

    context 'with TenantS3Bucket' do
      before do
        @region = ENV.delete('AWS_REGION')
        @access_key_id = ENV.delete('AWS_ACCESS_KEY_ID')
        @secret_access_key = ENV.delete('AWS_SECRET_ACCESS_KEY')
        @tenant_s3_bucket = FactoryBot.create(:tenant_s3_bucket, bucket_name: ENV['S3_SECURE_EXPORT_TEST_BUCKET'])
      end

      it 'can export to tenant configured bucket correctly' do
        Transports::S3Dest.should_receive(:new).with(
          @tenant_s3_bucket.bucket_name,
          s3_client_config: @tenant_s3_bucket.s3_client_config
        ).and_call_original
        VCR.use_cassette('exports/secure_export_with_tenant_config', match_requests_on: match_requests_on) do
          test_secure_export
        end
      end

      after do
        ENV['AWS_REGION'] = @region
        ENV['AWS_ACCESS_KEY_ID'] = @access_key_id
        ENV['AWS_SECRET_ACCESS_KEY'] = @secret_access_key
      end
    end

    after do
      ds_drop_table(connector: Connectors.from_ds(test_ds), fqname: FQName.parse('public.test_pageviews'))
    end
  end
end
