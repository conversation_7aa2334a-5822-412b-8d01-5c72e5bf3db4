# typed: false
require 'rails_helper'

describe 'Data table', js: true do
  let(:admin) { get_test_admin }

  let(:report) {
    sql = <<~SQL
      select column1 as id, column2 as product, column3 as price
      from (
        values
        (1, 'abc', 777),
        (2, 'def', 999),
        (3, 'xyz', 666)
      ) t
    SQL
    FactoryBot.create :query_report, query: sql, data_source: get_test_ds
  }

  let(:data_table) {
    FactoryBot.create :viz_setting, source: report
    report
  }

  before do
    FeatureToggle.toggle_global('viz:table_v2', true)
    FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
  end

  after(:each) do
    clear_page_unload_events
  end

  def render_data_table
    wait_for_element_load('.ci-btn-run:not([disabled])')
    page.find('.ci-btn-run').click
    wait_for_element_load('.ci-table-report-data')
  end

  def save_report
    wait_for_element_load '.ci-save-report:not([disabled])'
    page.find('.ci-save-report').click
  end

  def show_sort_options
    page.first('.ci-data-table-sort .hui-select.hui-select-trigger').click
  end

  def add_first_table_field
    select_h_select_option('.table-fields .ci-viz-field-select', label: 'id')
  end

  def sort_by_price
    select_h_select_option('.ci-data-table-sort', label: 'price')
  end

  def expect_data_table_sort_by_price
    wait_for_loading_finish
    wait_expect(['666', '777', '999']) do
      page.all('.ag-cell.ag-column-last').map(&:text)
    end
    expect(page).to have_selector('.ci-table-report-data .ag-column-last.ag-header-cell [data-ci="ci-arrow-up"]')
  end

  def count_sort_options
    page.all('.hui-select-floating .v-vl-visible-items .hui-select-option').count
  end

  it 'should show data table sort section' do
    safe_login admin, edit_query_report_path(data_table)
    render_data_table
    wait_for_element_load '.ci-data-table-sort'
    expect(page).to have_selector('.ci-data-table-sort')
  end

  it 'should show all fields if no field is selected' do
    safe_login admin, edit_query_report_path(data_table)
    render_data_table
    show_sort_options
    expect(count_sort_options).to eq 3
  end

  it 'should show selected fields only' do
    FeatureToggle.toggle_global('data_models:custom_field', false)
    safe_login admin, edit_query_report_path(data_table)
    render_data_table
    add_first_table_field
    show_sort_options
    expect(count_sort_options).to eq 1
  end

  it 'should sync with handsontable' do
    safe_login admin, edit_query_report_path(data_table)
    render_data_table
    sort_by_price
    expect_data_table_sort_by_price
  end

  it 'should persist sort setting after saving' do
    safe_login admin, edit_query_report_path(data_table)
    render_data_table
    sort_by_price
    save_report
    wait_for_all_ajax_requests
    wait_for_element_load('.query-reports .ci-table-report-data')
    expect_data_table_sort_by_price
  end
end
