# typed: false

require 'rails_helper'

describe 'Multiple sorts', js: true do
  before do
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, false)
    FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
    FeatureToggle.toggle_global('viz:table_v2', true)
  end

  context 'report 3.0' do
    include_context 'dynamic_dashboard'

    let!(:viz_setting) do
      FactoryBot.create(:viz_setting,
                        viz_type: 'data_table',
                        source_id: data_set.id,
                        source_type: 'DataSet',
                        fields: {
                          table_fields: [
                            {
                              path_hash: {
                                field_name: 'id',
                                model_id: products_model.id,
                              },
                              custom_label: nil,
                              type: 'number',
                              format: {
                                type: 'number',
                                format: {},
                              },
                            },
                            {
                              path_hash: {
                                field_name: 'name',
                                model_id: products_model.id,
                              },
                              custom_label: nil,
                              type: 'text',
                              format: {
                                type: 'string',
                                sub_type: 'string',
                              },
                            },
                            {
                              path_hash: {
                                field_name: 'price',
                                model_id: products_model.id,
                              },
                              custom_label: nil,
                              type: 'number',
                              format: {
                                type: 'number',
                                format: {},
                              },
                            },
                            {
                              path_hash: {
                                field_name: 'status',
                                model_id: products_model.id,
                              },
                              custom_label: nil,
                              type: 'text',
                              format: {
                                type: 'string',
                                sub_type: 'string',
                              },
                            },
                          ],
                        },
                        settings: {},
                        format: {},
                        filters: [],
                        adhoc_fields: [],)
    end

    context 'single sort' do
      before do
        FeatureToggle.toggle_global('viz:table_v2', true)
      end

      it 'change sort order' do
        viz_setting.settings[:sort] = { column: 0, sortOrder: true }
        viz_setting.save!
        safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")
        safe_click('.ci-explorer-control-get-results')
        wait_for_element_load('.ci-table-report-data')
        wait_expect(true) do
          page.all('.ag-header-cell')[1].has_css?('[data-ci="ci-arrow-up"]')
        end

        safe_click('.btn-desc')
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
        wait_expect(true) do
          page.all('.ag-header-cell')[1].has_css?('[data-ci="ci-arrow-down"]')
        end
      end
    end

    context 'FT is enabled' do
      it 'multiple sort in viz setting' do
        safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")
        wait_for_element_load('.data-set-explorer')
        select_h_select_option('.viz-sort .ci-sort-column-select:last-child', label: 'Name')
        select_h_select_option('.viz-sort .ci-sort-column-select:last-child', label: 'Status')
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
        expected_data = page.find_all('.ci-table-report-data .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
        expect(expected_data).to match_array(['1.00 bread 2.25 available', '2.00 milk 3.00 available',
                                              '3.00 egg 5.00 available', '4.00 bread 2.25 expired',])

        safe_click('.viz-section .sorting .sort-select:first-child .h-icon[data-icon="cancel"]')
        safe_click('.viz-section .sorting .sort-select:first-child .h-icon[data-icon="cancel"]')
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        wait_expect(false) do
          page.all('.ag-header-cell')[2].has_css?('[data-ci="ci-arrow-up"]')
        end
      end

      it 'multiple sort in table header' do
        safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")

        safe_click('.ci-explorer-control-get-results')
        wait_for_element_load('.ci-table-report-data')
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
        table = page.find('[data-ci="ci-ag-grid-data-table"]')

        open_context_menu_by_name(element: table, header_name: 'Name')
        safe_click('.ci-sort-asc-btn')
        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        open_context_menu_by_name(element: table, header_name: 'Status')
        safe_click('.ci-sort-asc-btn')
        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        expected_data = page.find_all('.ci-table-report-data .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
        expect(expected_data).to match_array(['1.00 bread 2.25 available', '2.00 milk 3.00 available',
                                              '3.00 egg 5.00 available', '4.00 bread 2.25 expired',])
      end

      context 'paginating' do
        let(:connector) { Connectors.from_ds(data_set.data_source) }

        before do
          insert_orders = <<~SQL
            INSERT INTO data_modeling.products(name, merchant_id, category_id, "Price", "Status", "Created At")
              ( VALUES
                ('bread', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
                ('milk', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
                ('egg', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
                ('bread', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
                ('eggplant', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
                ('cucumber', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
                ('apple', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
                ('pen', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
                ('pinapple', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
                ('cow', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
                ('pig', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
                ('chicken', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
                ('sponge', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
                ('chili', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
                ('fish', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
                ('crab', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
                ('bread', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
                ('milk', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
                ('eggplant', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
                ('cucumber', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z'),
                ('apple', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
                ('samsung', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
                ('fish', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
                ('cow', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z')
              )
          SQL
          connector.exec_sql(insert_orders)
        end

        after do
          connector.exec_sql('drop table data_modeling.products')
          connector.exec_sql(
            <<~SQL,
              CREATE TABLE data_modeling.products(
                "Id" serial PRIMARY KEY,
                "name" varchar,
                "merchant_id" integer,
                "category_id" integer,
                "Price" float,
                "Status" varchar,
                "Created At" timestamp
              )
          SQL
          )
          load_data_modeling_data
        end

        it 'keep sortings when paginating' do
          viz_setting.settings[:sort] = [{ column: 0, sortOrder: true }, { column: 1, sortOrder: true }]
          viz_setting.save!
          safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")

          safe_click('.ci-explorer-control-get-results')
          wait_for_element_load('.ci-table-report-data')
          safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')

          wait_expect(true) do
            [1, 2].all? do |i|
              page.all('.ag-header-cell')[i].has_css?('[data-ci="ci-arrow-up"]')
            end
          end
          expected_data = page.find_all('.ci-table-report-data .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
          expect(expected_data).to match_array(['26.00 samsung 3.00 available', '27.00 fish 5.00 available',
                                                '28.00 cow 2.25 expired',])
        end

        it 'paginates normaly when ft is disabled' do
          safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")

          safe_click('.ci-explorer-control-get-results')
          wait_for_element_load('.ci-table-report-data')
          safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')
          wait_for_submit_generate
          expect(page.find_all('.ci-table-report-data .ag-row').length).to eq(3)
        end
      end
    end

    context 'FT is turn off on report with multiple sort' do
      before do
        FeatureToggle.toggle_global('viz:table_v2', false)
      end

      let!(:ds_report) do
        viz_setting.settings = { sort: [{ column: 1, sortOrder: true }, { column: 3, sortOrder: true }] }
        viz_setting.save!
        create(
          :products_table_report,
          owner: admin,
          data_set: data_set,
          viz_setting: viz_setting,
        )
      end
      let!(:dashboard_widget) do
        create(:dashboard_widget, source: ds_report, dashboard: dashboard, data: { is_adhoc: true })
      end

      it 'does not apply sort' do
        safe_login(admin, dashboard_path(dashboard))
        wait_for_element_load('.h-dashboard-widget')
        page.find('.h-dashboard-widget').hover
        page.find('.h-widget-controls [data-icon="ellipsis-horizontal"]').click
        safe_click('.ci-edit-report-widget')
        safe_click('.ci-explorer-control-get-results')
        wait_for_element_load('.ci-table-report-data')
        expected_data = page.find_all('.ci-table-report-data .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
        expect(expected_data).to match_array(['1.00 bread 2.25 available', '2.00 milk 3.00 available',
                                              '3.00 egg 5.00 available', '4.00 bread 2.25 expired',])
        expect(page.find_all('.viz-sort .ci-sort-column-select').length).to eq(1)
      end
    end

    context 'refresh cache' do
      before do
        FeatureToggle.toggle_global('viz:sort_before_limit', true)
      end

      let!(:ds_report) do
        viz_setting.settings = {
          sort: [{ column: 1, sortOrder: true }, { column: 3, sortOrder: true }],
          misc: {
            custom_color_list: [{}], pagination_size: 25, show_row_number: true, row_limit: -1,
          },
        }
        viz_setting.save!
        create(
          :products_table_report,
          owner: admin,
          data_set: data_set,
          viz_setting: viz_setting,
        )
      end
      let!(:dashboard_widget) do
        create(:dashboard_widget, source: ds_report, dashboard: dashboard, data: { is_adhoc: true })
      end

      it 'keeps current sort' do
        safe_login(admin, dashboard_path(dashboard))
        wait_for_element_load('.h-dashboard-widget')

        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        open_context_menu_by_name(element: table, header_name: 'Price')
        safe_click('.ci-sort-asc-btn')
        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
        wait_for_element_load('.cache-status')
        safe_click('.cache-status.refresh')

        wait_for_all_ajax_requests
        wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

        wait_expect(true) do
          table.all('.ag-header-cell')[2..4].all? do |header|
            header.has_css?('[data-ci="ci-arrow-up"]')
          end
        end
      end

      it 'works properly' do
        viz_setting.settings[:sort] = { column: nil }
        viz_setting.save!
        safe_login(admin, dashboard_path(dashboard))
        wait_for_element_load('.h-dashboard-widget')
        page.find('.h-dashboard-widget').hover
        safe_click('.ci-widget-controls-trigger')
        safe_click('.ci-edit-report-widget')
        safe_click('.ci-explorer-control-get-results')
        wait_for_element_load('.ci-table-report-data')
        expected_data = page.find_all('.ci-table-report-data .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
        expect(expected_data).to match_array(['1.00 bread 2.25 available', '2.00 milk 3.00 available',
                                              '3.00 egg 5.00 available', '4.00 bread 2.25 expired',])
      end
    end
  end

  context 'sql report' do
    include_context 'test_tenant'

    before do
      FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
    end

    let(:query) do
      <<~SQL
        select
          column1 as product,
          column2 as name,
          column3 as rev
        from
          (
            values
              ('Book', 'Laura', 50),
              ('Book', 'Britney', 150),
              ('Pen', 'Laura', 50),
              ('Pen', 'Britney', 80),
              ('Book', 'Laura', 50),
              ('Pen', 'Laura', 50),
              ('Book', 'Laura', 70),
              ('Book', 'Britney', 40),
              ('Pen', 'Laura', 60),
              ('Pen', 'Britney', 50),
              ('Book', 'Laura', 30),
              ('Pen', 'Laura', 70),
              ('Book', 'Laura', 200),
              ('Book', 'Britney', 250),
              ('Pen', 'Laura', 300),
              ('Pen', 'Britney', 400),
              ('Book', 'Laura', 401),
              ('Pen', 'Laura', 55),
              ('Book', 'Laura', 77),
              ('Book', 'Britney', 42),
              ('Pen', 'Laura', 65),
              ('Pen', 'Britney', 52),
              ('Book', 'Laura', 31),
              ('Pen', 'Laura', 79),
              ('Book', 'Laura', 51),
              ('Book', 'Britney', 159),
              ('Pen', 'Laura', 56),
              ('Pen', 'Britney', 88),
              ('Book', 'Laura', 225),
              ('Pen', 'Laura', 115)
        ) R
      SQL
    end
    let(:viz_setting) do
      FactoryBot.create(
        :viz_setting,
        fields: {
          table_fields: [],
        },
        settings: {
          sort: [{ column: 0, sortOrder: true }, { column: 2, sortOrder: true }],
        },
      )
    end
    let!(:report) do
      FactoryBot.create(
        :query_report_new,
        name: 'multiple sortings',
        query: query,
        viz_setting: viz_setting,
      )
    end

    it 'keeps sorting when paginating' do
      safe_login(admin, query_report_path(report))
      wait_for_element_load('.ci-table-report-data')
      safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      expected_data = page.find_all('.ci-table-report-data .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
      expect(expected_data).to match_array(['Pen Britney 400', 'Pen Britney 80', 'Pen Britney 88', 'Pen Laura 115',
                                            'Pen Laura 300',])
    end

    it 'keeps sorting when paginating in SQL editor' do
      safe_login(admin, '/adhoc/query_editor')
      wait_for_element_load('#adhoc-editor')
      ace_set_text('#adhoc-editor', query)
      page.has_css?('.ci-btn-run') ? safe_click('.ci-btn-run') : safe_click('.ci-run-query')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

      table = page.find('[data-ci="ci-ag-grid-data-table"]')
      open_context_menu_by_name(element: table, header_name: 'product')
      safe_click('.ci-sort-asc-btn')
      wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
      safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')
      wait_expect(true) { page.has_css?('#query-result .v-loading-container', visible: false) }
      product_header = find_header_element_by_name(element: table, header_name: 'product')
      expect(product_header).to have_css('[data-ci="ci-arrow-up"]')
    end

    it 'paginates normaly if ft is disabled' do
      FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', false)
      safe_login(admin, query_report_path(report))
      wait_for_element_load('.ci-table-report-data')
      safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')
      wait_for_submit_generate
      expect(page.find_all('.ci-table-report-data .ag-row').length).to eq(5)
    end

    it 'shows all fields if no fields are selected' do
      viz_setting.settings[:sort] = {}
      viz_setting.save!
      safe_login(admin, query_report_path(report))
      safe_click('.ci-edit-report-link')
      wait_for_element_load('.ci-btn-run:not([disabled])')
      safe_click('.ci-btn-run')
      wait_for_submit_generate
      safe_click('.viz-sort .ci-sort-column-select:last-child.hui-select-trigger')
      text = page.find('.hui-select-floating .v-vl-visible-items').text
      expect(text).to eq("product\nname\nrev")
    end

    it 'shows only selected fields' do
      viz_setting.settings[:sort] = {}
      viz_setting.save!
      safe_login(admin, query_report_path(report))
      safe_click('.ci-edit-report-link')
      wait_for_element_load('.ci-btn-run:not([disabled])')
      safe_click('.ci-btn-run')
      wait_for_submit_generate
      select_h_select_option('.viz-section:nth-child(1) .ci-viz-field-select', label: 'name')
      safe_click('.viz-sort .ci-sort-column-select:last-child.hui-select-trigger')
      text = page.find('.hui-select-floating .v-vl-visible-items').text
      expect(text).to eq('name')
    end

    it 'show multiple sort popover on single report' do
      safe_login(admin, query_report_path(report))
      wait_for_element_load('.ci-table-report-data')
      safe_click('.show-multiple-sort-popover-btn')
      fields = page.find_all('.multiple-sort-popover .ci-sort-column-select.hui-select-trigger')
      expect(fields.length).to eq 2
      expect(fields.map(&:text)).to match_array(['product', 'rev'])
    end

    context 'pivot' do
      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          viz_type: 'pivot_table',
          fields: {
            pivot_data: {
              rows: [{ path_hash: { field_name: 'rev' } }],
              columns: [],
              values: [{ path_hash: { field_name: 'rev' } }],
            },
          },
          settings: {
            misc: {
              pagination_size: 10,
              show_row_number: true,
            },
            sort: { column: 1, sortOrder: true },
          },
        )
      end
      let!(:report) do
        FactoryBot.create(
          :query_report_new,
          name: 'multiple sortings',
          query: query,
          viz_setting: viz_setting,
        )
      end

      context 'cannot perform multiple sort' do
        before do
          FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', false)
        end

        it 'workses correctly' do
          safe_login(admin, query_report_path(report))
          wait_for_element_load('.ci-table-report-data .pivot-table-wrapper')
          safe_click('.table-pagination .hui-pagination .hui-pagination-item:nth-child(3)')
          wait_for_submit_generate
          expect(page.has_css?('.ci-table-report-data .pivot-table-wrapper')).to eq true
        end
      end
    end
  end
end
