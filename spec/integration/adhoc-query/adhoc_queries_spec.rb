# typed: false

require 'rails_helper'

describe 'Integration test for adhoc-queries page', js: true do
  include_context 'test_tenant'

  before do
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
  end

  def create_new_adhoc
    safe_click('.ci-query-editor')
    ace_set_text '#adhoc-editor', 'values(1,2,3)'
    safe_click('.ci-run-query')
    sleep 1 # wait for changes
  end

  context 'existed adhoc queries' do
    let!(:qr) do
      query = 'SELECT 1 AS foo, 2 as bar'
      FactoryBot.create :adhoc_query, query: query, state: 'success', tenant: tenant, owner_id: admin.id,
                                      data_source: ds
    end

    it 'renders success adhoc-queries', stable: true do
      safe_login(admin, '/adhoc')
      wait_expect(true) { page.all('table.table-records tbody tr').length > 0 } # ensure queries are existed
    end

    it 'creates adhoc success', stable: true do
      safe_login(admin, '/adhoc')
      query_count = AdhocQuery.count
      create_new_adhoc

      page.driver.go_back
      sleep 2 # loading time
      wait_expect(true) { page.all('table.table-records tbody tr').count > query_count }

      # visit again
      first('.table.table-records tbody tr td a', text: '2').click
      wait_expect(1) { page.all('.ag-row').count }

      safe_click '.ci-edit-as-new' # the query should be appended to the existing content
      wait_expect("values(1,2,3)") { ace_get_text }
    end
  end

  context 'non-existed adhoc queries' do
    it 'remembers written queries', stable: true do
      safe_login(admin, '/adhoc/query_editor')
      ace_set_text '#adhoc-editor', '' # prevent from side effects
      wait_expect('') { ace_get_text }

      ace_set_text '#adhoc-editor', 'select 1;'
      sleep 1 # wait for changes

      reload_page
      wait_expect('select 1;') { ace_get_text }
    end
  end

  describe 'viewing adhoc query', stable: true do
    let(:aq) do
      FactoryBot.create(
        :adhoc_query,
        query: <<~SQL,
          WITH t(name, age) as (
            VALUES
              ('alice', 19),
              ('bob', 15)
          )
          SELECT * FROM t
        SQL
        tenant: tenant,
        owner_id: admin.id,
        data_source: ds,
      )
    end

    context 'with sql gen gem enabled' do
      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
      end

      context 'with conditional formatting' do
        let!(:viz_setting) do
          FactoryBot.create(
            :viz_setting,
            viz_type: 'data_table',
            fields: {
              table_fields: [
                { path_hash: { model_id: 123, field_name: 'name' } },
                { path_hash: { model_id: 123, field_name: 'age' } },
              ],
            },
            format: {},
            filters: [],
            settings: {
              conditional_formatting: [
                {
                  path_hash: {
                    field_name: 'age',
                  },
                  name: 'age',
                  type: 'number',
                  model: {
                    name: '"cache"."t5612"',
                    backend_type: 'PgcacheModel',
                  },
                  aggregation: 'count',
                  operator: {
                    operator: 'greater_than',
                    label: '>',
                    inputs: [
                      'number',
                    ],
                    modifiers: [],
                  },
                  values: [
                    '16',
                  ],
                  scale_values: [
                    nil,
                    nil,
                    nil,
                  ],
                  text_color: '#000000',
                  background_color: '#62ADD4',
                  min_color: '#C3E8C2',
                  mid_color: '#6FE562',
                  max_color: '#34BA5A',
                  mode: 'single',
                  apply_to_row: false,
                  modifier: nil,
                },
              ],
            },
            source: aq,
            tenant_id: tenant.id,
          )
        end

        before do
          aq.async(user_id: admin.id, tenant_id: tenant.id).execute
        end

        it 'renders conditional formatting correctly', stable: true do
          safe_login(admin, "/adhoc/#{aq.id}")

          wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'
          cells = page.all('.ag-cell')
          expect(cells.size).to eq(4)
          formatted_cells = cells.select do |cell|
            cell.native.css_value('background-color') == 'rgba(98, 173, 212, 1)'
          end
          expect(formatted_cells.size).to eq(1)
          expect(formatted_cells[0].text).to eq('19')
        end
      end
    end
  end

  it 'create new adhoc report', stable: true do
    FeatureToggle.toggle_global('sql_report:creation', true)
    safe_login(admin, '/adhoc/query_editor')
    ace_set_text '#adhoc-editor', '' # prevent from side effects
    wait_expect('') { ace_get_text }
    ace_set_text '#adhoc-editor', 'select 1;'
    safe_click('.ci-run-query')
    safe_click('.ci-save-adhoc')
    page.find('.ci-title input').set('report 1')

    select_h_select_option('.ci-folder', value: '0c')

    safe_click('.ci-save-report-from-explore-modal')
    safe_click('[data-ci="open-new-report"]')
    wait_expect('report 1') do
      # check title in new tab
      window = page.driver.browser.window_handles
      page.driver.browser.switch_to.window(window.last)
      all('.ci-report-title').first&.text
    end
  end

  context '`save query as` behaviors' do
    let!(:ds) { get_test_ds }

    it 'shows save as model button and save successfully', stable: true do
      FeatureToggle.toggle_global('data_models:manager', true)
      FeatureToggle.toggle_global('sql_report:creation', false)
      safe_login(admin, '/adhoc/query_editor')
      wait_expect('') { ace_get_text }

      ace_set_text '#adhoc-editor', 'select 1'
      safe_click '.ci-run-query'
      wait_for_element_load('.ci-save-as-model')
      safe_click '.ci-save-as-model'
      page.find('.ci-model-name').set('speccccccccc')
      safe_click '.ci-save-query-model'
      wait_for_element_load '.ci-link-to-result-model'
      result_model = DataModel.last
      expect(result_model.name).to eq 'speccccccccc'
      expect(result_model.backend.query).to eq 'select 1'
    end

    it 'shows save as report button', stable: true do
      FeatureToggle.toggle_global('data_models:manager', false)
      FeatureToggle.toggle_global('sql_report:creation', true)
      safe_login(admin, '/adhoc/query_editor')
      wait_expect('') { ace_get_text }
      ace_set_text '#adhoc-editor', 'select 1'
      safe_click '.ci-run-query'
      wait_for_element_load('.ci-save-adhoc')
    end

    it 'shows popover with save as model and save as report buttons', stable: true do
      FeatureToggle.toggle_global('data_models:manager', true)
      FeatureToggle.toggle_global('sql_report:creation', true)
      safe_login(admin, '/adhoc/query_editor')
      wait_expect('') { ace_get_text }
      ace_set_text '#adhoc-editor', 'select 1'
      safe_click '.ci-run-query'
      wait_for_element_load('.ci-save-query-result')
      safe_click('.ci-save-query-result')

      expect(page.find('.ci-save-adhoc-query-result').text.gsub("\n",
                                                                ' ',)).to eq('Save as Transform Model Save as Report')
    end
  end

  context 'schema explorer specs' do
    let!(:bigquery) do
      FactoryBot.create(
        :data_source,
        name: 'bigquery',
        dbtype: 'bigquery',
        dbconfig: dbconfig_bigquery_test_env.merge(
          dataset: 'ecommerce',
        ),
      )
    end
    let(:vcr_match_cond) { %i[method uri host path] }

    let!(:mysql) do
      FactoryBot.create(
        :data_source,
        name: 'mysql',
        dbtype: 'mysql',
        dbconfig: dbconfig_mysql_test_env,
      )
    end

    let(:vcr_match_cond) { %i[method uri host path] }

    it 'mysql should show all tables', stable: true do
      tenant.set_setting('default_ds_id', mysql.id)

      safe_login admin, '/adhoc/query_editor'

      wait_for_element_load '.schema-explorer .tree-select-option-list'

      expect(page.find('.schema-explorer .vue-recycle-scroller__item-view:first-child').text).to eq('(All tables)')
    end

    it 'disable data_source:enable_schema_info', stable: true do
      FeatureToggle.toggle_global('data_source:enable_schema_info', false)

      safe_login admin, '/adhoc/query_editor'

      expect(page.find('.schema-explorer').text).to eq('Schema Information is disabled for this Data Source. Please contact your Admin.')
    end

    it 'bigquery: analyze button should contains schema and table name', stable: true do
      VcrHelper.ignore_hosts('127.0.0.1', 'localhost')

      tenant.set_setting('default_ds_id', bigquery.id)

      VCR.use_cassette('bigquery_schema_fetching', match_requests_on: vcr_match_cond) do
        safe_login admin, '/adhoc/query_editor'

        wait_for_element_load '.schema-explorer .tree-select-option-list'

        page.find('.schema-explorer .tree-select-control-input').set('order_items')

        page.find('.schema-explorer .tree-select-item', text: 'order_items').hover

        safe_click('.schema-explorer .analyze-btn')

        expect(page.find('.ace_content').text).to eq "#standardSQL\nSELECT * FROM `ecommerce.order_items`"
      end
    end
  end

  it 'disables table interaction' do
    FeatureToggle.toggle_global('sql_report:creation', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('table:freeze_columns', true)
    FeatureToggle.toggle_global('table:hide_fields', true)
    safe_login(admin, '/adhoc/query_editor')
    ace_set_text '#adhoc-editor', '' # prevent from side effects
    wait_expect('') { ace_get_text }
    ace_set_text '#adhoc-editor', 'select 1, 2, 3;'
    safe_click('.ci-run-query')

    wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')

    header = page.first('.ag-header-cell', exact_text: '?column?')
    open_context_menu(header)
    expect(page.has_no_css?('[data-icon="column-freeze"]')).to be true
    expect(page.has_no_css?('[data-icon="eye-light-slash"]')).to be true
  end
end
