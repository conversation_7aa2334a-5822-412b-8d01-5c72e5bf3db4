# typed: false
require 'rails_helper'

describe 'Drilldowns feature', js: true, stable: true do
  let(:admin) {get_test_admin}

  before do
    size = 5
    sql = <<-SQL.strip_heredoc
        select generate_series(1, #{ size }) as column_name_1,
               generate_series(#{ size + 1 }, #{ size * 2 }) as column_name_2;
    SQL
    @report_raw_data = (1...size).map {|i| [i, i + 5]}

    settings = {}

    @src_report = FactoryBot.create :query_report, title: 'Drilldown - Source report',
                                     query: sql, settings: settings

    @dest_report_1 = @src_report = FactoryBot.create :query_report, title: 'Drilldown - Dest report 1',
                                                      query: sql, settings: settings

    @dest_report_2 = @src_report = FactoryBot.create :query_report, title: 'Drilldown - Dest report 2',
                                                      query: sql, settings: settings

    @shared_filter = FactoryBot.create(:shared_filter)

    @fo1 = FactoryBot.create :filter_ownership, var_name: 'Dest_report_filter_1', filterable_type: 'QueryReport', filterable_id: @dest_report_1.id, shared_filter: @shared_filter
    @fo2 = FactoryBot.create :filter_ownership, var_name: 'Dest_report_filter_2', filterable_type: 'QueryReport', filterable_id: @dest_report_2.id, shared_filter: @shared_filter

    @dashboard = FactoryBot.create :dashboard, title: 'Drilldown - Dest dashboard'
    @dashboard_fo = FactoryBot.create :filter_ownership, var_name: 'Dest_dashboard_filter_1', filterable_type: 'Dashboard', filterable_id: @dashboard.id, shared_filter: @shared_filter
  end

  def navigate_to_drilldown_popup
    page.first('.ci-toggle').click
    page.first('.ci-drilldowns-btn').click
  end

  context 'Dest is QueryReport' do
    it 'should navigate to dest report with the specified value used for filter' do
      drilldown = FactoryBot.create :drilldown,
                                     from_report_id: @src_report.id, dest: @dest_report_1,
                                     mapping: {
                                       link_column_index: '0',
                                       value_column_index: '1',
                                       dest_fo_id: @fo1.id
                                     }

      safe_login(admin, "/queries/#{@src_report.to_param}")
      wait_for_report_load

      record_index = 1
      link_elem = page.all('.drilldown-link')[record_index]
      drilldown_value = @report_raw_data[record_index][drilldown.mapping['value_column_index'].to_i]

      link_elem.click
      wait_for_report_load

      wait_expect("#{@fo1.var_name}=#{drilldown_value}") do
        URI.parse(current_url).query
      end
    end

    context 'CRU methods' do
      it 'can create drilldown object with all drilldown options selected' do
        safe_login(admin, "/queries/#{@src_report.to_param}")
        wait_for_report_load
        navigate_to_drilldown_popup

        page.find('.ci-drilldowns-new').click
        wait_for_element_load '.ci-link-column-dropdown'
        page.first('.ci-link-column-dropdown option').select_option
        page.first('.ci-value-column-dropdown option').select_option
        search_h_select('.ci-dest-dropdown', text: @dest_report_1.title)
        select_h_select_option('.ci-dest-dropdown', label: @dest_report_1.title)
        wait_for {first '#filter-dropdown'}
        select @fo1.var_name, from: 'filter-dropdown'
        safe_click('.drilldown-form-submit')

        safe_click('.ci-drilldowns-new')
        wait_for_element_load '.ci-link-column-dropdown'
        page.all('.ci-link-column-dropdown option')[1].select_option
        page.all('.ci-value-column-dropdown option')[1].select_option
        search_h_select('.ci-dest-dropdown', text: @dest_report_2.title)
        select_h_select_option('.ci-dest-dropdown', label: @dest_report_2.title)

        wait_for {first '#filter-dropdown'}
        select @fo2.var_name, from: 'filter-dropdown'
        safe_click('.drilldown-form-submit')

        wait_for_element_load '.drilldown-list'
        sleep 1
        drilldown_data = page.all('.drilldown-list tbody tr').map {|elem| elem.all('td').first(4).map {|e| e.text}}
        expect(drilldown_data.length).to eq 2
        expect(drilldown_data).to match_array([
          ["2", "column_name_2", "column_name_2", "Drilldown - Dest report 2"],
          ["1", "column_name_1", "column_name_1", "Drilldown - Dest report 1"]
        ])
      end

      it 'can create drilldown object without selecting dest filter' do
        safe_login(admin, "/queries/#{@src_report.to_param}")
        wait_for_report_load
        navigate_to_drilldown_popup

        safe_click('.ci-drilldowns-new')
        wait_for_element_load('.ci-link-column-dropdown')
        page.first('.ci-link-column-dropdown option').select_option
        page.first('.ci-value-column-dropdown option').select_option
        search_h_select('.ci-dest-dropdown', text: @dest_report_1.title)
        select_h_select_option('.ci-dest-dropdown', label: @dest_report_1.title)
        sleep 1

        safe_click('.drilldown-form-submit')

        wait_expect([["1", "column_name_1", "column_name_1", "Drilldown - Dest report 1"]]) do
          page.all('.drilldown-list tbody tr').map do |elem|
            elem
              .all('td')
              .first(4)
              .map(&:text)
          end
        end
      end

      it 'can be deleted successfully' do
        drilldown = FactoryBot.create :drilldown,
                                       from_report_id: @src_report.id, dest: @dest_report_1,
                                       mapping: {from_column: 'column_name_1', dest_fo_id: @fo1.id}

        safe_login(admin, "/queries/#{@src_report.to_param}")
        wait_for_report_load
        navigate_to_drilldown_popup
        wait_for { page.first('.drilldown-list') }
        safe_click('.drilldown-list .delete-drilldown')
        wait_for_element_load '.modal-dialog .ci-confirm'
        sleep 1
        wait_for_element_load '.ci-confirm'
        safe_click('.ci-confirm')
        wait_expect(0) { page.all('.ci-confirm', wait: false).count }

        wait_expect(0) { Drilldown.count }

        page.should have_no_selector('.drilldown-list tbody tr')
      end

      it 'can be updated' do
        drilldown = FactoryBot.create :drilldown,
                                       from_report_id: @src_report.id, dest: @dest_report_1,
                                       mapping: {
                                         link_column_index: '0',
                                         value_column_index: '0',
                                         dest_fo_id: @fo1.id
                                       }

        safe_login(admin, "/queries/#{@src_report.to_param}")
        wait_for_report_load
        navigate_to_drilldown_popup
        wait_for {first '.drilldown-list'}
        page.first('.drilldown-list .edit-drilldown').click

        wait_for_element_load '.ci-link-column-dropdown'
        page.all('.ci-link-column-dropdown option')[1].select_option
        page.all('.ci-value-column-dropdown option')[1].select_option
        search_h_select('.ci-dest-dropdown', text: @dest_report_2.title)
        select_h_select_option('.ci-dest-dropdown', label: @dest_report_2.title)

        wait_for {first '#filter-dropdown'}
        select @fo2.var_name, from: 'filter-dropdown'
        safe_click('.drilldown-form-submit')

        expected_drilldown = {
          from_report_id: @src_report.id,
          dest_id: @dest_report_2.id,
          dest_type: @dest_report_2.class.to_s,
          mapping: {
            'dest_fo_id' => @fo2.id,
            'link_column_index' => 1,
            'value_column_index' => 1
          }
        }

        wait_expect(expected_drilldown) do
          updated_drilldown = Drilldown.find drilldown.id

          {
            from_report_id: updated_drilldown.from_report_id,
            dest_id: updated_drilldown.dest_id,
            dest_type: updated_drilldown.dest_type,
            mapping: updated_drilldown.mapping
          }
        end
      end
    end
  end

  context 'Dest is Dashboard object' do
    it 'should be created and navigate to dest with the specified value used for filter' do
      safe_login(admin, "/queries/#{@src_report.to_param}")
      wait_for_report_load
      navigate_to_drilldown_popup
      safe_click('.ci-drilldowns-new')
      wait_for_element_load '.ci-link-column-dropdown'

      page.first('.ci-link-column-dropdown option').select_option
      page.first('.ci-value-column-dropdown option').select_option
      search_h_select('.ci-dest-dropdown', text: @dashboard.title)
      select_h_select_option('.ci-dest-dropdown', label: @dashboard.title)

      safe_click('.drilldown-form-submit')
      wait_expect([["1", "column_name_1", "column_name_1", "Drilldown - Dest dashboard"]]) do
        page.all('.drilldown-list tbody tr').map do |elem|
          elem
            .all('td')
            .first(4)
            .map(&:text)
        end
      end

      visit "/queries/#{@src_report.to_param}/"
      wait_for_report_load

      record_index = 0
      drilldown_value = @report_raw_data[record_index][0]

      page.all('.drilldown-link')[record_index].click
      wait_for_element_load('.dashboard-show')

      wait_expect("#{@dashboard_fo.var_name}=#{drilldown_value}") {URI.parse(current_url).query}
      wait_expect("/dashboards/#{@dashboard.to_param}") {URI.parse(current_url).path}
    end
  end

  describe 'embed drilldowns' do
    before do
      FeatureToggle.toggle_global('public_users:drilldowns', true)
      FeatureToggle.toggle_global('public_users:cache_params_without_token', true)
      @r1 = FactoryBot.create :query_report, query: "values('nice', 'asd')", title: 'First Report'
      @r2 = FactoryBot.create :query_report, query: 'select {{status}}', title: 'Second Report'
      @sf = FactoryBot.create :shared_filter, name: 'Status', settings: {type: "input"}
      @fo = FactoryBot.create :filter_ownership, var_name: 'status', filterable: @r2, shared_filter_id: @sf.id
      @drilldown = FactoryBot.create :drilldown,
                                    from_report_id: @r1.id, dest: @r2,
                                    mapping: {link_column_index: 0, value_column_index: 0, dest_fo_id: @fo.id}
      @customer_sf = FactoryBot.create :shared_filter, settings: {type:'input'}, name: 'Customer Id'

      @dashboard = FactoryBot.create :dashboard, title: 'First Dashboard'
      @dashboard_fo = FactoryBot.create :filter_ownership, var_name: 'status', filterable: @dashboard, shared_filter_id: @sf.id
      @customer_filter_ownership = FactoryBot.create :filter_ownership,
        shared_filter: @customer_sf,
        var_name: 'customer_id',
        filterable: @dashboard

      @common_sf = FactoryBot.create :shared_filter, name: 'text', settings: {type: "input"}
      @common_fo1 = FactoryBot.create :filter_ownership,
        shared_filter: @common_sf,
        var_name: 'text',
        filterable: @dashboard
      @common_fo2 = FactoryBot.create :filter_ownership,
        shared_filter: @common_sf,
        var_name: 'text',
        filterable: @r2

      @widget = FactoryBot.create :dashboard_widget, source_id: @r1.id, dashboard_id: @dashboard.id

      @r3 = FactoryBot.create :query_report, title: 'Third Report', query: "values('cool', 'haha')"
      @customer_filter_ownership3 = FactoryBot.create :filter_ownership,
        shared_filter: @customer_sf,
        var_name: 'customer_id',
        filterable: @r3
      @common_fo3 = FactoryBot.create :filter_ownership,
        shared_filter: @common_sf,
        var_name: 'text',
        filterable: @r3
      @drilldown2 = FactoryBot.create :drilldown,
                            from_report_id: @r3.id, dest: @r2,
                            mapping: {link_column_index: 0, value_column_index: 0, dest_fo_id: @fo.id},
                            settings: {copy_all_filters: true}

      create :tenant_subscription, tenant: admin.tenant, status: 'active'
    end

    # Prefetch the associations.
    # Without doing this, the test occasionally fails wtih cases like
    # * `embed_link.source.filter_ownerships` -> source is Nil
    # * `filter_ownership.shared_filter` -> shared_filter is Nil
    # __Dont know why__
    def prefetch_associations
      EmbedLink.find_each do |el|
        el.source.filter_ownerships
        el.filter_ownerships.each { |fo| fo.var_name }
      end
      FilterOwnership.find_each { |fo| fo.shared_filter }
      FilterValue.find_each { |fv| fv.filter_ownership }
    end

    # TODO Add more test with more than one filter
    def create_embed_link_and_token(source, customer_filter_ownership)
      embed_link = FactoryBot.create :embed_link, source: source
      embed_link.add_many(:filter_ownership, :embed_link_identifier_variable, customer_filter_ownership)
      embed_link.set_public_user
      embed_link.share_source
      embed_link.update_filters
      sk = embed_link.secret_key
      customer_key = 123
      token = jwt_encode(sk, { customer_filter_ownership.var_name => customer_key }, Time.now.to_i + 1000)

      prefetch_associations

      [embed_link, token]
    end

    def click_drilldown_link
      safe_click '.drilldown-link'
    end

    def click_back_drilldown
      safe_click '.ci-drilldown-back'
    end

    it 'allows user to drilldown embedded dashboard' do
      embed_link, token = create_embed_link_and_token(@dashboard, @customer_filter_ownership)
      visit "/embed/#{embed_link.hash_code}?_token=#{token}"
      wait_for_report_load

      click_drilldown_link
      wait_for { page.title.include?(@r2.title) }
      wait_for_report_load

      safe_click('.ci-toggle-download')
      safe_click('.ci-download-csv')
      wait_for_element_load '.ci-download-link'
      test_download_link

      click_back_drilldown
      wait_for { page.title.include?(@dashboard.title) }
      wait_for_report_load

      click_drilldown_link
      wait_for { page.title.include?(@r2.title) }
      wait_for_report_load
    end

    it 'allows user to drilldown embedded report' do
      embed_link, token = create_embed_link_and_token(@r3, @customer_filter_ownership3)
      visit "/embed/#{embed_link.hash_code}?_token=#{token}"
      wait_for_report_load

      click_drilldown_link
      wait_for { page.title.include?(@r2.title) }
      wait_for_report_load

      click_back_drilldown
      wait_for { page.title.include?(@r3.title) }
      wait_for_report_load

      click_drilldown_link
      wait_for { page.title.include?(@r2.title) }
      wait_for_report_load
    end

    it 'allows chaining drilldown' do
      @drilldown2.update(dest: @dashboard, mapping: {link_column_index: 0, value_column_index: 0, dest_fo_id: @dashboard_fo.id})
      embed_link, token = create_embed_link_and_token(@r3, @customer_filter_ownership3)
      visit "/embed/#{embed_link.hash_code}?_token=#{token}"
      wait_for_report_load

      click_drilldown_link
      wait_for { page.title.include?(@dashboard.title) }

      click_drilldown_link
      wait_for { page.title.include?(@r2.title) }
      wait_for_report_load

      click_back_drilldown
      wait_for { page.title.include?(@dashboard.title) }
      wait_for_report_load

      click_back_drilldown
      wait_for { page.title.include?(@r3.title) }
      wait_for_report_load
    end

    context 'drilldown to the same page' do
      before do
        @drilldown.update!(dest: @dashboard)
      end

      it 'works' do
        embed_link, token = create_embed_link_and_token(@dashboard, @customer_filter_ownership)
        visit "/embed/#{embed_link.hash_code}?_token=#{token}"
        wait_for_report_load

        click_drilldown_link
        wait_for { page.title.include?(@dashboard.title) }
        wait_for_report_load
      end
    end
  end
end
