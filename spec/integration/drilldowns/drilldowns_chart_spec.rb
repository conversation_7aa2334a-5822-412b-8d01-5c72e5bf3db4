# typed: false
require 'rails_helper'

describe 'pie chart', js: true, stable: true do
  let(:admin) { get_test_admin }

  before do
    @src_report = FactoryBot.create(:pie_chart_report, title: 'Drilldown - Source report')
    @dest_report_1 = FactoryBot.create(:query_report, title: 'Drilldown - Dest report 1')
    @dest_report_2 = FactoryBot.create(:query_report, title: 'Drilldown - Dest report 2')
    @shared_filter = FactoryBot.create(:shared_filter)
    @fo1 = FactoryBot.create :filter_ownership,
                              var_name: 'Dest_report_filter_1',
                              filterable_type: 'QueryReport', filterable_id: @dest_report_1.id,
                              shared_filter: @shared_filter
    @fo2 = FactoryBot.create :filter_ownership,
                              var_name: 'Dest_report_filter_2',
                              filterable_type: 'QueryReport', filterable_id: @dest_report_2.id,
                              shared_filter: @shared_filter
  end

  context 'Dest is QueryReport' do
    before do
      FactoryBot.create :drilldown,
                          from_report_id: @src_report.id,
                          dest: @dest_report_1,
                          mapping: {
                            link_column_index: '0',
                            value_column_index: '0', # value_column_index === fullpath to enable drilldown. Check the viz_setting fullpath
                            dest_fo_id: @fo1.id
                          }
    end

    it 'should navigate to dest report with the specified value used for filter' do
      safe_login(admin, "/queries/#{@src_report.to_param}/")
      wait_for_viz_load

      click_highchart('.highcharts-container .highcharts-series-group .highcharts-pie-series .highcharts-point')
      sleep 2 # wait for redirection
      wait_for_report_load

      # Why "Bali"? See the :pie_chart_report
      # We can't target the pie that we want on Highcharts - e.g. target the "Bali" pie,
      # but we hope that the 1st pie is the first value of the series.
      wait_expect("#{@fo1.var_name}=Bali") do
        URI.parse(current_url).query
      end
      wait_for_report_load
    end
  end

end
