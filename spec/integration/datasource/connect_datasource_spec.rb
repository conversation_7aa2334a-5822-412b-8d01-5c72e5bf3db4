# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Connect first datasource', js: true, stable: true do
  let(:current_user) { get_test_admin }
  let(:current_tenant) { get_test_tenant }
  let(:test_dbconfig) { dbconfig_rails_test_env }

  before do
    DataSource.destroy_all
    FeatureToggle.toggle_global('schema_synchronization', true)
    FeatureToggle.toggle_global('data_models:manager', true)
    FeatureToggle.toggle_global('aml_studio:enable', false)
    FeatureToggle.toggle_global('sql_report:creation', false)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(DataSet::FT_CREATION_IN_REPORTING, true)
    FeatureToggle.toggle_global('reporting_nav:enabled', true)
    ftg = FeatureToggleGroups::UpsertVersions.new.call!('Version 3.0')
    ftg.toggle_tenants!([current_tenant.id], FeatureToggleGroup::TOGGLE_MODE_ADD)
    qlogin current_user, '/home'
  end

  def connect_ds
    wait_for_element_load('.connect-data-source')
    page.find('button', text: 'PostgreSQL').click
    wait_for_element_load('.ci-name')
    fill_text('.ci-name', 'new-ds')

    fill_text('.ci-host', test_dbconfig[:host])
    fill_text('.ci-port', test_dbconfig[:port])
    fill_text('.ci-username', test_dbconfig[:user])
    fill_text('.ci-password', test_dbconfig[:password])
    fill_text('.ci-dbname', test_dbconfig[:dbname])

    safe_click '.ci-test'
    sleep 0.5
    wait_for_element_load('[data-ci="ci-test-message"]')
    wait_expect('connected successfully') { page.find('[data-hui-comp="banner"] [data-ci="ci-test-message"]').text.downcase }

    page.find('.ci-submit').click
    sleep 0.5
  end

  it 'should hide left menu nav and reporting nav when there is no datasource yet' do
    expect(page).not_to have_selector('.js-main-features')
    expect(page).not_to have_selector('.left-menus .header-node-item')
    expect(page).not_to have_selector('.left-menus .quick_links')
    expect(page).not_to have_selector('.node-explorer')
  end

  it 'should show connect datasource panel when there is no datasource yet' do
    wait_for_element_load('.onboarding-welcome-modal')
    page.find('.onboarding-welcome-modal').click_button('Get started')
    wait_for_element_load('.onboarding-welcome-screen')
    page.find('.ci-admin-enter-onboarding').click
    wait_for_element_load('.admin-onboarding-home')
    expect(page).to have_selector('.admin-onboarding-home')
  end

  it 'should successfully add new datasource' do
    wait_for_element_load('.onboarding-welcome-modal')
    page.find('.onboarding-welcome-modal').click_button('Get started')
    wait_for_element_load('.onboarding-welcome-screen')
    page.find('.ci-admin-enter-onboarding').click
    connect_ds
    wait_expect(current_user.tenant_id) { DataSource.last&.tenant_id }
    ds = DataSource.last

    expect(ds.tenant_id).to eq current_user.tenant_id
    %i[host user dbname password port].each do |k|
      expect(ds.dbconfig[k].to_s).to eq test_dbconfig[k].to_s
    end
  end

  it 'should successfully create model and navigate to data models page' do
    # connect to datasource
    wait_for_element_load('.onboarding-welcome-modal')
    page.find('.onboarding-welcome-modal').click_button('Get started')
    wait_for_element_load('.onboarding-welcome-screen')
    page.find('.ci-admin-enter-onboarding').click
    connect_ds
    wait_expect(current_user.tenant_id) { DataSource.last&.tenant_id }

    ds = DataSource.last
    wait_expect(true) { ds.reload.current_version.present? }
    wait_for_element_load('.add-table-models')
    tree_select_select_option('.tree-select-wrapper', 'users')
    page.find('.create-models').click
    wait_for_element_load('.create-data-set-onboarding')
    fill_text('.ci-dataset-name', 'The First Dataset')
    tree_select_select_option('.model-select-wrapper', 'user')
    page.find('.create-dataset').click
    wait_for_element_load('.data-set-explorer')
    expect(page.find('.ci-data-set-form-title').text).to eq 'The First Dataset'
    visit '/data_models/'
    wait_for_element_load('.data-model-header')
    wait_for { page.current_path.start_with?('/data_models') }
    expect(DataModel.where(data_source_id: ds.id).count).to be > 0
  end
end
