# typed: false
require 'rails_helper'

describe 'Usage Monitoring Dashboard', js: true, stable: true do
  include_context 'simple_query_model'

  let(:admin) { get_test_admin }
  let(:data_model_name) { 'public_users' }
  let(:dataset_name) { 'usage_monitoring' }
  let(:new_dataset_name) { 'usage_monitoring_ds_ver3' }
  let(:query_model_sql) do
    <<~SQL
      with t(id, name, tenant_id, created_at, views) as (
        values(1, 'alice', 1, '2020-01-01', 10),
          (2, 'foo', 1, '2020-02-04', 6),
          (3, 'bar', 1, '2020-02-02', 6),
          (4, 'foo', 2, '2020-01-03', 9),
          (5, 'cisco', 2, '2020-04-01', 8)
      )
      select id, name, tenant_id, created_at::timestamp, views from t
    SQL
  end
  let(:rm) do
    model = query_data_model
    model.name = data_model_name
    model.save!
    model
  end
  let(:data_set) do
    data_set = create(:data_set, root_model_id: nil)
    data_set.uname = dataset_name
    data_set.data_models << rm
    data_set.save!
    data_set
  end
  let!(:new_data_set) do
    data_set = create(:data_set, root_model_id: nil)
    data_set.uname = new_dataset_name
    data_set.data_models << rm
    data_set.save!
    data_set
  end
  let(:viz_setting) do
    create(
      :viz_setting,
      fields: {
        table_fields: [
          {
            path_hash: { field_name: 'name', model_id: rm.id },
            custom_label: '',
            type: rm.fields.first.type,
            format: { type: 'auto' },
          },
        ],
      },
    )
  end
  let(:root_model_id) { nil }
  let(:data_set_id) { data_set.id }
  let(:qr) { create :query_report, viz_setting: viz_setting, data_set_id: data_set_id }
  let(:dashboard) do
    create :dashboard, version: 3, owner: admin, name: 'Dashboard Usage Monitoring', settings: { date_drill_enabled: true }
  end
  let(:dw) { create :dashboard_widget, dashboard: dashboard, source: qr }
  let(:field_path) { DataModeling::Values::FieldPath.new(field_name: 'tenant_id', model_id: rm.id) }
  let(:filter_source) { create :dm_field_filter_source, data_set: data_set, field_path: field_path }
  let(:filter_definition) do
    create :dynamic_filter_definition, filter_source: filter_source, filter_type: 'number', label: 'Tenant Id', default_condition: { operator: 'is', modifier: nil, values: [] }
  end
  let(:dynamic_filter) do
    create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard, drillthrough_enabled: true
  end
  let(:embed_link) do
    el = create :embed_link, source: dashboard, filter_ownerships: [], version: 3
    el.set_public_user
    el.share_source
    el
  end
  let(:values) do
    ['bar', 'foo', 'alice']
  end

  before do
    FeatureToggle.toggle_global(::Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global(::Dashboard::FT_V1_CREATION, false)
    FeatureToggle.toggle_global(::Dashboard::FT_V3_DATE_DRILL, true)
    FeatureToggle.toggle_global(Viz::Services::ValidateScope::FT_ALLOW_ANY_DATE_SCOPE, true)
    FeatureToggle.toggle_global(::DataSet::FEATURE_TOGGLE_KEY, true)
    create :internal_embed_link, hash_code: embed_link.hash_code
    dynamic_filter
    dw
  end

  context 'feature toggle enabled' do
    before do
      FeatureToggle.toggle_global(::InternalEmbedLink::FT_USAGE_MONITORING, true)
    end

    it 'should show usage monitoring dashboard' do
      safe_login(:admin, '/manage/usage_monitoring')
      expect(page.all('.navigation-sidebar__list-item').map(&:text)).to include('Usage Monitoring')

      within_frame(page.find('.ci-frame-usage-monitoring')) do
        wait_for_report_data
        expect(page.find('.title').text).to eq('Dashboard Usage Monitoring')

        wait_expect(values) do
          page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }.flatten
        end
      end
    end

    context 'feature wall FT' do
      let(:viz_setting_x_axis) do
        {
          format: { type: 'date', sub_type: 'mmm yyyy' },
          type: 'date',
          sub_type: 'mmm yyyy',
          path_hash: { model_id: rm.id, field_name: 'created_at' },
          model_id: rm.id,
          field_name: 'created_at',
          transformation: 'datetrunc month',
        }
      end
      let(:viz_setting_series) do
        {
          type: 'text',
          format: { type: 'string', sub_type: 'string' },
          path_hash: { model_id: rm.id, field_name: 'name' },
        }
      end
      let(:viz_setting_y_axes) do
        [
          {
            label: 'Y Axis 1',
            columns: [
              {
                type: 'auto',
                color: 'auto',
                format: { type: 'number', sub_type: 'auto' },
                path_hash: { model_id: rm.id, field_name: 'views' },
                aggregation: 'sum',
                series_settings: {
                  series_type: 'auto'
                }
              },
            ],
          },
        ]
      end
      let(:viz_setting) do
        create(
          :viz_setting,
          viz_type: 'column_chart',
          fields: {
            x_axis: viz_setting_x_axis,
            series: viz_setting_series,
            y_axes: viz_setting_y_axes,
          },
          settings: {
            x_axis: { title: nil },
            legend: { enabled: true, alignment: 'bottom' },
            y_axes: [
              {
                max: nil,
                min: nil,
                align: 'left',
                title: nil,
                scale_type: 'linear',
                stack_type: 'normal',
                stack_series: false,
                maximum_groups: 5,
                group_long_tail: false,
                show_data_label: false,
                show_group_total: false,
                show_stack_total: false,
                show_series_percentage: false,
              },
            ],
            'misc' => { 'pagination_size' => 25, 'show_row_number' => true },
            'sort' => {},
            'aggregation' => { 'show_total' => false, 'show_average' => false },
            'conditional_formatting' => {},
          },
          format:
            { 'name' => { 'type' => 'string', 'index' => 0, 'sub_type' => 'auto' } },
        )
      end

      context 'enabled' do
        before do
          FeatureToggle.toggle_global(::InternalEmbedLink::FT_USAGE_MONITORING_EXTRA_FEATURES, true)
        end

        it 'able to perform date drill' do
          safe_login(:admin, '/manage/usage_monitoring')

          within_frame(page.find('.ci-frame-usage-monitoring')) do
            wait_for_element_load('.highchart')

            expect(page).to have_selector('.ci-filters .ci-dashboard-date-drill', visible: :visible)
            page.find('.ci-report-widget').hover
            expect(page).to have_selector('.h-report-widget .result-viz .ci-viz-date-drill', visible: :visible)

            page.find('.highchart').right_click
            sleep 0.5
            expect(page).to have_selector('.h-context-menu-content .ci-date-drill', visible: :visible)
          end
        end

        it 'able to export raw data' do
          safe_login(:admin, '/manage/usage_monitoring')

          within_frame(page.find('.ci-frame-usage-monitoring')) do
            wait_for_element_load('.highchart')
            page.find('.ci-report-widget').hover
            wait_and_click('.ci-widget-controls-more')
            page.find('.ci-download').hover

            expect(page).to have_selector('.ci-export-excel-widget', visible: :visible)
            expect(page).to have_selector('.ci-export-csv-widget', visible: :visible)
          end
        end
      end

      context ' disabled' do
        it 'dashboard date drill menu is disabled and show tooltip' do
          safe_login(:admin, '/manage/usage_monitoring')

          within_frame(page.find('.ci-frame-usage-monitoring')) do
            wait_for_element_load('.highchart')

            expect(page).to have_selector('.ci-filters .ci-dashboard-date-drill', visible: :visible)
            page.find('.ci-filters .ci-dashboard-date-drill').hover
            wait_for_element_load('.hui-tooltip-floating.ci-usage-monitoring-extra-features-tooltip')
            expect(page.find('.hui-tooltip-floating.ci-usage-monitoring-extra-features-tooltip').text).to match(/Date-drill in Usage Monitoring/)

            page.find('.ci-report-widget').hover
            expect(page).not_to have_selector('.h-report-widget .result-viz .ci-viz-date-drill', visible: :visible)

            page.find('.highchart').right_click
            sleep 0.5
            expect(page).not_to have_selector('.h-context-menu-content .ci-date-drill', visible: :visible)
          end
        end

        it 'export menu is disabled and show tooltip' do
          safe_login(:admin, '/manage/usage_monitoring')

          within_frame(page.find('.ci-frame-usage-monitoring')) do
            wait_for_element_load('.highchart')
            page.find('.ci-report-widget').hover

            expect(page).to have_selector('.ci-widget-controls-more', visible: :visible)
            wait_and_click('.ci-widget-controls-more')

            expect(page).not_to have_selector('.ci-download', visible: :visible)
            reliable_hover('.ci-usage-monitoring-extra-features-tooltip-trigger', '.ci-usage-monitoring-extra-features-tooltip')
            expect(page.find('.ci-usage-monitoring-extra-features-tooltip').text).to match(/Export raw data in Usage Monitoring/)

            expect(page).not_to have_selector('.ci-export-excel-widget', visible: :visible)
            expect(page).not_to have_selector('.ci-export-csv-widget', visible: :visible)
          end
        end
      end
    end
  end

  context 'feature toggle disabled' do
    before do
      FeatureToggle.toggle_global(::InternalEmbedLink::FT_USAGE_MONITORING, false)
    end

    it 'should not show navigation menu' do
      safe_login(:admin, '/manage/usage_monitoring')
      expect(page.all('.navigation-sidebar__list-item').map(&:text)).not_to include('Usage Monitoring')

      within_frame(page.find('.ci-frame-usage-monitoring')) do
        expect(page).not_to have_selector('.title')
      end
    end
  end
end
