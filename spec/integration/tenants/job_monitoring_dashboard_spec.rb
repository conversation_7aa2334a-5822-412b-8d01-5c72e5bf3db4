# typed: false
require 'rails_helper'

describe 'Job Monitoring Dashboard', js: true do
  include_context 'aml_studio_deployed' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/job_monitoring'
    end
  end

  before do
    GlobalConfig.set(
      InternalEmbedLinks::UrlGenerators::JobMonitoring::GC_PERMISSIBLE_MODELS,
      <<~JSON,
        [
          { "dataset": "report_jobs", "model": "done_report_jobs" },
          { "dataset": "report_jobs", "model": "job_queues" }
        ]
      JSON
    )
  end

  context 'with prepared aml files' do
    let(:aml_files) do
      aml_files = super()
      aml_files['done_report_jobs.model.aml'].gsub!('DS_ID1', data_sources[1].id.to_s)
      aml_files['done_report_jobs.model.aml'].gsub!('DS_ID2', data_sources[0].id.to_s)
      aml_files
    end

    let(:data_set) do
      deploy_result # make sure deployment is already done
      DataSet.find_by!(uname: 'report_jobs')
    end

    let(:model) { 'done_report_jobs' }

    let(:viz_setting) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        fields:
          { 'table_fields' => [
            {
              'type' => 'string',
              'format' => { 'type' => 'string', 'sub_type' => 'auto' },
              'path_hash' => { 'field_name' => 'name', model_id: model, 'joins_path' => [] },
              'custom_label' => nil,
            },
          ] },
        settings:
          { 'misc' => { 'pagination_size' => 25, 'show_row_number' => true },
            'sort' => nil,
            'aggregation' => { 'show_total' => false, 'show_average' => false },
            'conditional_formatting' => {}, },
        format:
          { 'name' => { 'type' => 'string', 'index' => 0, 'sub_type' => 'auto' } },
      )
    end
    let(:report) do
      FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
      create :query_report, data_set_id: data_set.id, viz_setting: viz_setting
    end

    let(:job_queues_viz_setting) do
      create(
        :viz_setting,
        viz_type: 'data_table',
        fields:
          { 'table_fields' => [
            {
              'type' => 'string',
              'format' => { 'type' => 'string', 'sub_type' => 'auto' },
              'path_hash' => { 'field_name' => 'name', model_id: 'job_queues', 'joins_path' => [] },
              'custom_label' => nil,
            },
          ] },
        settings:
          { 'misc' => { 'pagination_size' => 25, 'show_row_number' => true },
            'sort' => nil,
            'aggregation' => { 'show_total' => false, 'show_average' => false },
            'conditional_formatting' => {}, },
        format:
          { 'name' => { 'type' => 'string', 'index' => 0, 'sub_type' => 'auto' } },
      )
    end
    let(:job_queues_report) do
      FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
      create :query_report, data_set_id: data_set.id, viz_setting: job_queues_viz_setting
    end

    let(:dashboard) do
      FeatureToggle.toggle_global(::Dashboard::FT_V3_CREATION, true)
      create :dashboard, version: 3, title: 'Job Monitoring Dashboard'
    end
    let!(:dw) { create :dashboard_widget, dashboard: dashboard, source: report }
    let!(:job_queues_dw) { create :dashboard_widget, dashboard: dashboard, source: job_queues_report }
    let(:field_path) { DataModeling::Values::FieldPath.new(field_name: 'tenant_id_param', model_id: model) }
    let(:filter_source) { create :dm_field_filter_source, data_set: data_set, field_path: field_path }
    let(:filter_definition) do
      create :dynamic_filter_definition, filter_source: filter_source, filter_type: 'number', label: 'Tenant Id', default_condition: { operator: 'is', values: [] }
    end
    let!(:dynamic_filter) do
      create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard
    end

    let(:field_path_dt) { DataModeling::Values::FieldPath.new(field_name: 'dashboard_title', model_id: model) }
    let(:filter_source_dt) { create :dm_field_filter_source, data_set: data_set, field_path: field_path_dt }
    let(:filter_definition_dt) do
      create :dynamic_filter_definition, filter_source: filter_source_dt, filter_type: 'number', label: 'dashboard Title', default_condition: { operator: 'is', values: [] }
    end
    let!(:dynamic_filter_dt) do
      create :dynamic_filter, definition: filter_definition_dt, filter_holdable: dashboard
    end
    let!(:dynamic_filter_mapping) do
      create :dynamic_filter_mapping, dynamic_filter: dynamic_filter_dt, viz_conditionable: dw, field_path: field_path_dt
    end

    let(:embed_link) do
      el = create :embed_link, source: dashboard, version: 3
      el.set_public_user
      el.share_source
      el
    end

    let(:admin) { get_test_admin }

    before do
      create :internal_embed_link, hash_code: embed_link.hash_code, usage_type: InternalEmbedLink::UsageType::JobMonitoring.serialize
    end

    context 'feature toggle enabled' do
      before do
        FeatureToggle.toggle_global(DataModel::FT_PROCESS_DYNAMIC_MODELS, true)
        FeatureToggle.toggle_global(::InternalEmbedLink::FT_JOB_MONITORING, true)
      end

      it 'shows job monitoring dashboard' do
        safe_login(admin, '/manage/jobs/dashboard')

        wait_expect(3) { page.all('.ci-manage-jobs-tab-header', visible: true).size }

        within_frame(page.find('.ci-frame-job-monitoring')) do
          wait_for_report_data

          wait_expect(['bar', 'foo', 'alice']) do
            page.first('.ci-table-report-data').all('.ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
          end
        end
      end

      context 'with filter_dashboard param' do
        before do
          safe_login(admin, '/manage/jobs/dashboard?filter_dashboard=b')
        end

        it 'sets dashboard_title filter' do
          wait_expect(3) { page.all('.ci-manage-jobs-tab-header', visible: true).size }

          within_frame(page.find('.ci-frame-job-monitoring')) do
            wait_for_report_data

            wait_expect(['bar', 'foo']) do
              page.first('.ci-table-report-data').all('.ag-cell:not(.ag-column-first)').map(&:text)
            end
          end
        end
      end

      context 'visitor is analyst' do
        let(:analyst) { users(:analyst) }
        let(:data_sources) { DataSource.where(tenant_id: analyst.tenant_id).to_a }

        before do
          FeatureToggle.toggle_global(Job::FT_ALLOW_ANALYST_TO_MANAGE_JOB, true)
        end

        context 'no read_job permission' do
          it 'shows no data' do
            safe_login(analyst, '/manage/jobs/dashboard')

            wait_expect(2) { page.all('.ci-manage-jobs-tab-header', visible: true).size }

            within_frame(page.find('.ci-frame-job-monitoring')) do
              wait_expect(2) { page.all('.result-viz').count }

              wait_expect('No data') do
                page.all('.result-viz')[0].text
              end
              wait_expect('No data') do
                page.all('.result-viz')[1].text
              end
            end
          end
        end

        context 'with read_job permission' do
          before do
            admin.share(analyst, :read_job, data_sources[1])
          end

          it 'shows data of corresponding data sources only' do
            safe_login(analyst, '/manage/jobs/dashboard')

            wait_expect(2) { page.all('.ci-manage-jobs-tab-header', visible: true).size }

            within_frame(page.find('.ci-frame-job-monitoring')) do
              wait_expect(2, 10) { page.all('.result-viz').count }

              wait_expect(['bar', 'alice']) do
                page.first('.ci-table-report-data').all('.ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
              end
              wait_expect('No data') do
                page.all('.result-viz')[1].text
              end
            end
          end
        end
      end
    end
  end

  context 'feature toggle disabled' do
    before do
      FeatureToggle.toggle_global(::InternalEmbedLink::FT_JOB_MONITORING, false)
    end

    it 'does not show the tab' do
      safe_login(admin, '/manage/jobs/dashboard')

      wait_expect(2) { page.all('.ci-manage-jobs-tab-header', visible: true).size }
    end
  end
end
