# typed: false
require 'rails_helper'

describe 'Metric sheets', js: true, legacy: true do
  def add_sheet
    wait_for_element_load '.ci-add-sheet'
    safe_click('button.ci-add-sheet')
    sleep 1
    wait_for_all_ajax_requests
  end

  def set_title(title)
    safe_click('.ci-sheet-title')
    page.find('.ci-sheet-title input').set(title)
    safe_click('body') # unfocus input
    sleep 1
  end

  def set_description(desc)
    metric_desc_selector = '.ci-sheet-description'
    edit_editable_textarea(metric_desc_selector, desc)
    sleep 1
  end

  def add_header(title)
    wait_for_element_load '.ci-add-header'
    safe_click('.ci-add-header')
    page.find('.ci-add-header-input').set(title)
    safe_click('.ci-add-header-btn')
    sleep 1
  end

  def add_metric(id)
    wait_for_element_load('.ci-add-metric')
    safe_click('.ci-add-metric')
    select_h_select_option('.ci-metric-select', value: id)
    safe_click('.ci-add-metric-btn')
    sleep 1
  end

  it 'can set sheet title and description' do
    safe_login :admin, '/metrics'
    wait_for_element_load '.ci-add-sheet'
    safe_click('button.ci-add-sheet')
    set_title('New Sheet')
    set_description('New Description')
    sleep 1 # wait for submission
    sheet = MetricSheet.last
    expect(sheet.title).to eq('New Sheet')
    expect(sheet.description).to eq('New Description')
  end

  context 'with existed metrics' do
    let!(:metric) { FactoryBot.create :query_metric }
    before do
      safe_login :admin, '/metrics'
      add_sheet
    end

    it 'can add new header and metric' do
      add_header('New header')
      add_metric(metric.id)

      sheet = MetricSheet.last
      expect(sheet.ordered_rows.first.source.title).to eq('New header')
      expect(sheet.ordered_rows.second.source.title).to eq('MyString')
    end

    # Currently drag and drop does not work
    # See: https://github.com/RubaXa/Sortable/issues/563
    it 'can use context action buttons' do
      add_metric(metric.id)

      page.find('.metric-row').hover
      wait_for_element_load('.ci-shift-right-btn')
      safe_click('.ci-shift-right-btn')
      sleep 1
      sheet = MetricSheet.last
      expect(sheet.ordered_rows.first.level).to eq(1)

      safe_click('.ci-shift-left-btn')
      sleep 1
      sheet.reload
      expect(sheet.ordered_rows.first.level).to eq(0)
    end

    it 'can rearrange header and metric', skip: :true do
      add_header('New header')
      add_metric(metric.id)

      sheet = MetricSheet.last
      expect(sheet.ordered_rows.first.source_type).to eq('MetricHeader')
      expect(sheet.ordered_rows.second.source_type).to eq('QueryMetric')

      wait_for_element_load('.metric-row')

      page.find('.metric-row').hover

      wait_for_element_load('.metric-row span.ci-handle')
      handle = page.find('.metric-row span.ci-handle')

      wait_for_element_load('table.table-metrics thead')
      target = page.find('table.table-metrics thead')

      handle.drag_to target

      sleep 30
      sheet.reload
      expect(sheet.ordered_rows.first.source_type).to eq('QueryMetric')
      expect(sheet.ordered_rows.second.source_type).to eq('MetricHeader')
    end

    it 'can update rows title via ui' do
      add_metric(metric.id)

      safe_click('.title-wrapper')
      sleep 1
      page.find('.title-wrapper input').set 'New Name'
      sleep 1
      find('.title-wrapper input').native.send_keys(:return)

      safe_click('body')
      wait_for_element_load '[data-ci="ci-toasts-top"] [data-ci="ci-toast"]'

      sheet = MetricSheet.last
      expect(sheet.ordered_rows.first.title).to eq('New Name')
    end
  end
end
