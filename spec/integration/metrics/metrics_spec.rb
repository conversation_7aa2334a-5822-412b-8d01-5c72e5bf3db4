# typed: false

require 'rails_helper'

describe 'metrics integration test', legacy: true do
  let!(:test_ds) { get_test_ds }
  let(:tenant) { get_test_tenant }
  let(:admin) { users(:admin) }
  let(:cntor) { Connectors.from_ds(test_ds) }
  let(:pageviews_sql) do
    <<-SQL.strip_heredoc
      SELECT SUM(cnt)
      FROM test_pageviews
      WHERE {{time_where}}
    SQL
  end
  after { pg_drop_pageviews(test_ds) }

  def wait_for_edit_modal
    wait_for_modal('.report-editor', 15)
  end

  def ace_set_text(text)
    wait_for_element_load('#metric_editor', 15)

    text_escaped = text.gsub("\n", '\\n')
    js = <<-JS.strip_heredoc
      var editor = window.ace.edit('metric_editor');
      editor.setValue("#{text_escaped}")
    JS
    page.execute_script(js)
  end

  def set_title(title)
    page.find('.ci-metric-title').click
    sleep 2
    page.find('.ci-metric-title input').set(title)
    page.find('body').click # unfocus input
  end

  def set_description(desc)
    metric_desc_selector = '.report-editor .ci-metric-description.editable-textarea-container'
    edit_editable_textarea(metric_desc_selector, desc)
  end

  def set_time_field(text)
    page.find('.ci-time-exp').set(text)
  end

  describe 'metrics listing', js: true do
    before do
      pg_create_pageviews(test_ds)
      @pageviews_m = FactoryBot.create :query_metric, title: 'Pageviews', query: pageviews_sql,
                                                      time_field: 'date_d', tenant_id: tenant.id, data_source_id: test_ds.id
    end

    it 'list correct metrics' do
      safe_login(admin, '/metrics/manage')
      wait_for_element_load('table tr:nth-child(1) td:nth-child(2)')
      expect(page.find('table tr:nth-child(1) td:nth-child(2)').text).to eq('Pageviews')
    end
  end

  describe 'create new metric' do
    before do
      pg_create_pageviews(test_ds)
    end
    it 'successfully creates new metric', js: true do
      safe_login(admin, '/metrics/manage')
      wait_for_element_load '.ci-add-metric'
      page.find('.ci-add-metric').click

      wait_for_edit_modal
      ace_set_text(pageviews_sql)
      set_title 'Pageviews'
      set_description 'Some description'
      set_time_field 'date_d'

      safe_click('.ci-metric-submit')
      wait_for_element_load '.preview-success'

      safe_click('.ci-save-metrics')
      sleep 1

      m = QueryMetric.last
      expect(m.title).to eq 'Pageviews'
      expect(m.query).to eq pageviews_sql
      expect(m.time_field).to eq 'date_d'
      expect(m.data_source_id).to eq test_ds.id
      expect(m.tenant_id).to eq tenant.id
    end
  end

  describe 'edit metric' do
    before do
      pg_create_pageviews(test_ds)
      @pageviews_m = FactoryBot.create :query_metric, title: 'Pageviews', query: pageviews_sql,
                                                      time_field: 'date_d', tenant_id: tenant.id, data_source_id: test_ds.id
    end

    it 'successfully edit metric', js: true do
      safe_login(admin, '/metrics/manage')
      wait_for_element_load 'table tr:nth-child(1) td:nth-child(6) .ci-edit-metric'
      page.find('table tr:nth-child(1) td:nth-child(6) .ci-edit-metric').click

      wait_for_edit_modal
      ace_set_text(pageviews_sql)
      set_title 'Visits'
      set_description 'Some description'
      set_time_field 'date_d'

      safe_click('.ci-metric-submit')
      wait_for_element_load '.preview-success'

      safe_click('.ci-save-metrics')
      sleep 0.5

      m = @pageviews_m
      m.reload
      expect(m.title).to eq 'Visits'
      expect(m.query).to eq pageviews_sql
      expect(m.data_source_id).to eq test_ds.id
      expect(m.tenant_id).to eq tenant.id
    end
  end
end
