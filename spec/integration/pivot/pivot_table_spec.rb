# frozen_string_literal: true

# typed: false
require 'rails_helper'

describe 'Pivot specs', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:report) { FactoryBot.create :pivot_table_report }

  def check_sticky_row (expectation)
    clone_bottom_class = '.pivot-table .ht_clone_bottom'
    clone_left_bottom_class = '.pivot-table .ht_clone_bottom_left_corner'

    if expectation
      expect(page).to have_selector(clone_bottom_class)
      expect(page).to have_selector(clone_left_bottom_class)
    else
      expect(page).not_to have_selector(clone_bottom_class)
      expect(page).not_to have_selector(clone_left_bottom_class)
    end
  end

  it 'query report should render pivot table correctly' do
    safe_login admin, query_report_path(report)
    wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
    expect(page).to have_selector('[data-ci="ci-ag-grid-pivot-table"]')

    pivot_table = page.find('[data-ci="ci-ag-grid-pivot-table"]')
    total_cell = find_cell_element(table_element: pivot_table, row_index: 'b-0', col_index: 1, row_id: nil)
    expect(total_cell.text).to eq 'Total'
  end

  it 'query report can toggle zero as null in pivot table' do
    safe_login admin, query_report_path(report)
    wait_expect(true) do
      page.has_css?('[data-ci="ci-ag-grid-pivot-table"]')
    end

    page.find('.ci-edit-report-link').click
    safe_click('.ci-btn-run')
    wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

    # Toggle On convert_null_to_zero
    styles = page.find('div.nav-left > div:nth-child(2) > span', text: 'Styles')
    styles.click
    convert_null_to_zero = page.find('[data-hui-comp="switch"]', text: 'Display empty cell as 0')
    convert_null_to_zero.click

    page.find('.ag-body-viewport').scroll_to(0, 300)
    wait_expect(true) do
      page.all('.ag-row[row-index="14"]')[1].text.include?('0')
    end
    cells = page.all('.ag-row[row-index="14"] .ag-cell').map(&:text)
    wait_expect(%w[Korea Sub-Total 3 3 0 0]) { cells[0...6] } # Expect 0 in the result

    # Toggle Off convert_null_to_zero -> Expect Null in the result
    convert_null_to_zero.click
    wait_expect(false) do
      page.all('.ag-row[row-index="14"]')[1].text.include?('0')
    end
    cells = page.all('.ag-row[row-index="14"] .ag-cell').map(&:text)
    wait_expect(['Korea', 'Sub-Total', '3', '3', '', '']) { cells[0...6] }
  end

  # This feature toggle freeze rows on pivot is deprecated
  xit 'has freezing rows to be true with existing pivot report' do
    safe_login admin, query_report_path(report)
    wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')
    sleep 2
    page.find('.ci-edit-report-link').click
    safe_click('.ci-btn-run')
    wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

    # Check if freeze rows is toggled on
    styles = page.find('div.nav-left > div:nth-child(2) > span', text: 'Styles')
    styles.click
    freeze_rows_toggle = page.find('[data-ci="ci-freeze-rows"]')

    expect(freeze_rows_toggle[:class].include?('toggled')).to be true
  end

  it 'removes previous sticky row if column total is off' do
    safe_login admin, query_report_path(report)
    safe_click('.ci-edit-report-link')
    safe_click('.ci-btn-run')
    wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

    pivot = page.find('[data-ci="ci-ag-grid-pivot-table"]')
    total_cell = find_cell_element(table_element: pivot, row_index: 'b-0', col_index: 1, row_id: nil)
    expect(total_cell.text).to eq 'Total'

    safe_click('.viz-setting-form .__tab-list .nav-left div:nth-child(2)')

    safe_click('[data-hui-comp="switch"]', text: 'Column total')

    wait_for_all_holistics_loadings
    wait_expect(false) do
      page.has_css?('[row-index="b-0"]')
    end
  end

  context 'dataset-based report' do
    context 'big numbers' do
      include_context 'query_model_dataset_report_with_bignumbers'
      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          viz_type: 'pivot_table',
          fields:
            {
              'pivot_data' => {
                'rows' => [
                  {
                    'type' => 'number',
                    'format' => { 'type' => 'number', 'sub_type' => 'auto' },
                    'path_hash' => { 'field_name' => 'gender', 'model_id' => query_data_model.id },
                  },
                ],
                'columns' => [
                  {
                    'type' => 'number',
                    'format' => { 'type' => 'number', 'sub_type' => 'none' },
                    'path_hash' => { 'field_name' => 'num_chat_messages', 'model_id' => query_data_model.id },
                  },
                ],
                'values' => [
                  {
                    'type' => 'number',
                    'format' => { 'type' => 'number', 'sub_type' => 'auto' },
                    'path_hash' => { 'field_name' => 'value', 'model_id' => query_data_model.id },
                    'aggregation' => 'sum',
                  },
                ],
              },
            },
          settings:
            { 'misc' => { 'pagination_size' => 25, 'show_row_number' => true },
              'sort' => nil,
              'aggregation' => { 'show_total' => false, 'show_average' => false },
              'conditional_formatting' => {}, },
          format: {},
        )
      end

      it 'can render big numbers properly' do
        safe_login admin, query_report_path(query_model_dataset_based_report)
        wait_for_element_load('[data-ci="ci-ag-grid-pivot-table"]')

        wait_expect(['2', '5', '7', '9', '10', '23845621290950143.23845621290950143', 'Total']) do
          page.all('.ag-header-group-cell')[1..].map(&:text)
        end
        wait_expect(['', '', '803275.23845621290950143', '', '23845621290950143', '', '23845621291753418.23845621290950143']) do
          page.all('.ag-row[row-index="0"] .ag-cell')[1..].map(&:text)
        end
        wait_expect(['23845621292002029.69', '2443378.32', '', '23845621290950143', '3593998.24', '1771846', '47691242590761395.25']) do
          page.all('.ag-row[row-index="1"] .ag-cell')[1..].map(&:text)
        end
        wait_expect(
          [
            '23845621292002029.69',
            '2443378.32',
            '803275.23845621290950143',
            '23845621290950143',
            '23845621294544141.24',
            '1771846',
            '71536863882514813.48845621290950143',
          ],
        ) do
          page.all('.ag-row[row-index="b-0"] .ag-cell')[1..].map(&:text)
        end
      end
    end
  end
end
