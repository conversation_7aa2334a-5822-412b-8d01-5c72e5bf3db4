# typed: false

require 'rails_helper'

describe 'Data Set Explorer', js: true do
  include_context 'data_exploration'

  before do
    FeatureToggle.toggle_global('new_navigation_node', true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('filter:time_filter', true)
    FeatureToggle.toggle_global('viz_settings:explorer_control_v2', false)
    FeatureToggle.toggle_global('dataset_explorer:viz_caching_v2', false)
  end

  context 'Pivot Table' do
    before do
      FeatureToggle.toggle_global('table:single_row', true)
    end

    it 'shows pivot table after changing row_height' do
      viz_setting =
        create(
          :viz_setting,
          viz_type: 'pivot_table',
          source_id: data_set.id,
          source_type: 'DataSet',
          fields: {
            pivot_data: {
              columns: [
                {
                  custom_label: nil,
                  format: {
                    sub_type: 'string',
                    type: 'string',
                  },
                  path_hash: {
                    field_name: 'status',
                    model_id: model.id,
                  },
                  transformation: nil,
                  type: 'text',
                },
              ],
              rows: [
                {
                  custom_label: nil,
                  format: {
                    sub_type: 'string',
                    type: 'string',
                  },
                  path_hash: {
                    field_name: 'name',
                    model_id: model.id,
                  },
                  transformation: nil,
                  type: 'text',
                },
              ],
              values: [
                {
                  aggregation: 'sum',
                  custom_label: nil,
                  format: {
                    format: {
                      pattern: 'inherited',
                    },
                    type: 'number',
                  },
                  path_hash: {
                    field_name: 'price',
                    model_id: model.id,
                  },
                  type: 'number',
                },
              ],
            },
          },
          settings: {
            conditional_formatting: [],
            misc: {
              convert_null_to_zero: false,
              custom_color_list: [
                {},
              ],
              pagination_size: 25,
              row_height: 'Multiple lines',
              row_limit: -1,
              show_row_number: true,
            },
            others: {
              column_total: true,
              include_empty_children_rows: false,
              row_total: true,
              sub_total: false,
            },
            sort: {},
          },
        )

      safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")
      wait_for_element_load('.data-set-explorer')
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      expect(page.find('.pivot-table-wrapper').evaluate_script('this.getBoundingClientRect().height;')).to be > 0
      safe_click('[data-ci="ci-viz-setting-tab-styles"]')
      safe_click('[data-ci="ci-text-wrap"] [data-hui-section="handle"]')
      wait_for_viz_load
      expect(page.find('.pivot-table-wrapper').evaluate_script('this.getBoundingClientRect().height;')).to be > 0
    end
  end

  context 'custom expression' do
    before do
      FeatureToggle.toggle_global('data_sets:custom_expression', true)

      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
    end

    it 'able to add custom field' do
      select_h_select_option('.table-fields', value: "#{data_set.data_models.first.id}$!created_at")
      sleep 1
      select_h_select_option('.table-fields .ci-empty-field', label: 'Add Business Calculation')
      sleep 1

      fill_text('.ci-label-input', 'test field')
      fill_in_monaco('#ci-definition-input', '1 - 2 - 3')

      safe_click('.ci-submit')

      safe_click('.ci-explorer-control-get-results')

      wait_for_viz_load

      values = page.all('.ci-table-report-data .ag-cell:not(:first-child)').map(&:text)
      expect(values).to eq(['2019-08-09 00:00:00', '-4'])
    end

    it 'able to add custom measure' do
      select_h_select_option('.table-fields', value: "#{data_set.data_models.first.id}$!created_at")
      sleep 1
      select_h_select_option('.table-fields .ci-empty-field', label: 'Add Business Calculation')
      sleep 1

      fill_text('.ci-label-input', 'test field')
      fill_in_monaco('#ci-definition-input', 'count(data_modeling_products.id)')

      safe_click('.ci-submit')

      safe_click('.ci-explorer-control-get-results')

      wait_for_viz_load

      values = page.all('.ci-table-report-data .ag-cell:not(:first-child)').map(&:text)
      expect(values).to eq(['2019-08-09 00:00:00', '4'])
    end

    it 'able to click on the view more functions external link' do
      select_h_select_option('.table-fields', value: "#{data_set.data_models.first.id}$!created_at")
      sleep 1
      select_h_select_option('.table-fields .ci-empty-field', label: 'Add Business Calculation')
      sleep 1

      safe_click('.ci-add-sample-business-calc')
      business_calc_doc_window = window_opened_by do
        safe_click('.ci-view-more-functions')
      end
      within_window business_calc_doc_window do
        assert_current_path 'https://docs.holistics.io/docs/expression#supported-functions'
      end
    end
  end

  it 'tracks hotel frontend spans', otel: true do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')
    select_h_select_option('.table-fields', value: "#{data_set.data_models.first.id}$!name")
    safe_click('.ci-explorer-control-get-results')
    wait_for_submit_generate

    hotel_tester = HOtel::IntegrationTester.new(database: hotel_frontend_db)
    hotel_tester.expect_span({ name: 'ExploreDataSet' }, count: 1)
    hotel_tester.expect_child_spans({ name: 'ExploreDataSet' }, [
      { metadata: { name: 'VizResult#updateFunc' }, attributes: { 'h.viz_type' => 'data_table' }, count: 1 },
    ],)

    explore_dataset_span = hotel_tester.extract_frontend_db({ name: 'ExploreDataSet' })
    viz_update_func_span = hotel_tester.extract_frontend_db(
      { name: 'VizResult#updateFunc', parentSpanId: explore_dataset_span.keys.first },
    )
    hotel_tester.expect_child_spans({ name: 'VizResult#updateFunc', parentSpanId: explore_dataset_span.keys.first }, [
      { metadata: { name: 'VizResult#generateVizInput' }, count: 1 },
      { metadata: { name: /GET\s\/jobs\/parsed_query\.json/ }, count: 1 },
      { metadata: { name: 'VizResult#updateFunc#resize' }, count: 1 },
    ],)

    hotel_tester.expect_child_spans(
      { name: 'VizResult#generateVizInput', parentSpanId: viz_update_func_span.keys.first }, [
        { metadata: { name: /POST\s\/viz_data\/submit_generate\.json/ }, count: 1 },
        { metadata: { name: /GET\s\/viz_data\/get_generate_results\.json\?job_id=\d*/ }, count: 1 },
      ],
    )

    ajax_span = hotel_tester.extract_frontend_db({ name: /GET\s\/viz_data\/get_generate_results\.json\?job_id=\d*/ })
    expect(ajax_span.values.first['otelSpan']['attributes']['h.location']).to include('datasets/1-new-dataset')
  end

  it 'disables get result button when there is no field' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')

    expect(page.find('.explorer-controls')).to have_button('Get Result', disabled: true)

    select_h_select_option('.table-fields', value: "#{model.id}$!category_id")
    expect(page.find('.explorer-controls')).to have_button('Get Result', disabled: false)
  end

  it 'shows drag tooltip when there is no field' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')
    reliable_hover('.ci-explorer-control-get-results', '.hui-tooltip-floating.get-results-tooltip')
    wait_expect('Please drag in any fields to get the result') do
      page.find('.hui-tooltip-floating.get-results-tooltip', wait: 1).text
    end
  end

  it 'shows update tooltip when viz setting is changed' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')

    expect(page).to have_no_css('.hui-tooltip-floating')

    select_h_select_option('.table-fields', value: "#{model.id}$!category_id")

    reliable_hover('.hui-tooltip-floating.get-results-tooltip', '.hui-tooltip-floating.get-results-tooltip')
    wait_expect(/Click to fetch new/) do
      page.find('.hui-tooltip-floating.get-results-tooltip').text
    end
  end

  it 'shows tooltip when hover on field in viz setting' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')

    select_h_select_option('.table-fields', value: "#{model.id}$!category_id")
    page.first('.table-fields .ci-viz-field-select .modifier-select').hover

    data_model = data_set.data_models.first
    field = data_model.all_fields_sorted.first

    wait_for_element_load('.ci-field-label')
    expect(page.find('.ci-field-label').text).to eq field.label
    expect(page.find('.ci-field-type').text).to eq field.type
    expect(page.find('.ci-field-description').text).to eq 'No Description'
    expect(page.find('.ci-formula').text).to eq field.sql
    expect(page.find('.ci-data-model-info').text).to eq data_model.label
  end

  it 'shows tooltip when hover on condition' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')

    data_model = data_set.data_models.first
    select_h_select_option('.static-filters .viz-field', value: "#{data_model.id}$!created_at")

    sleep 1

    select_h_select_option('.ci-operator-select', value: 'last')
    filter_condition = page.find('.hui-popover-floating.dm-filter-popover-content .viz-filter-values .ci-value-select')
    filter_condition.send_keys('1')
    filter_condition.send_keys(:tab)
    page.first('.viz-section-header').click

    sleep 1
    page.first('.static-filters .viz-field .dm-filter').hover

    wait_for_element_load('.viz-filter-info')
    expect(page.all('.viz-filter-info')[0].text).to eq 'last 1 minute'
  end

  it 'shows field suggestions in condition input' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')

    select_h_select_option '.static-filters .ci-viz-field-select', label: 'Status'
    safe_click('.ci-value-select input')
    wait_expect(['available', 'expired']) do
      page.all('.h-select__options-item').map(&:text)
    end
    page.first('.ci-value-select input').send_keys('e')
    wait_expect(/available/) do
      page.find('.h-select__options').text
    end
    wait_expect(/expired/) do
      page.find('.h-select__options').text
    end
    wait_expect("+\nAdd option e") do
      page.find('.ci-create-option').text
    end
    page.first('.ci-value-select input').send_keys('x')
    wait_expect(['expired', "+\nAdd option ex"]) do
      page.all('.h-select__options-item').map(&:text)
    end
  end

  it 'handles big int for number filter input' do
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')

    # set filter value to a big int
    select_h_select_option '.static-filters .ci-viz-field-select', label: 'Price'
    wait_for_element_load('.ci-value-select')
    page.find('.ci-value-select input').send_keys('123123123123123123').send_keys(:enter)

    page.first('.viz-section-header').click

    # check filter value
    wait_expect("Price\n  equal 123123123123123123") { page.first('.dm-filter .h-input')&.text }
  end

  context 'when users create incomplete filters' do
    before do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
    end

    it 'keeps incomplete filters when viz setting is changed' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      select_h_select_option '.ci-viz-filter-select', label: 'Merchant Id'
      page.first('.viz-section-header').click

      # change viz type
      safe_click('.ci-viz-type-pie_chart')

      wait_expect("Merchant Id\n  any value") do
        page.find('.ci-dm-filter').text
      end

      # add a valid filter then an invalid filter
      make_condition('Category Id', '1')
      select_h_select_option '.ci-viz-filter-select', label: 'Price'
      page.first('.viz-section-header').click

      # change viz type again
      safe_click('.ci-viz-type-pyramid_chart')
      wait_expect(["Merchant Id\n  any value", "Category Id\n  equal 1", "Price\n  any value"]) do
        page.all('.ci-dm-filter').map(&:text)
      end
    end
  end

  context 'viz setting form should reflect missing field' do
    it 'column chart should reflect y-axis field' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      safe_click '.ci-viz-type-select'
      safe_click '.ci-viz-type-column_chart'
      select_h_select_option '.ci-x-axis-row .ci-empty-field', value: '1$!category_id'
      safe_click '.ci-explorer-control-get-results'

      wait_for_element_load '.result-viz'
      # include error response  (check ErrorSurvey component)
      wait_expect("Can you solve this problem yourself?\nYes, I can\nI understand but can't solve it\nI don't understand the error\nPlease select Y-axis field") do
        page.find('.result-viz').text
      end
      wait_for_element_load('.ci-add-y-axis-row .warning-viz-field-select')
      page.find('.ci-add-y-axis-row .warning-viz-field-select').hover
      expect(page.find('.hui-tooltip-floating', text: 'Please select Y-axis field')).to be_visible
    end
  end

  describe 'syncing with url' do
    it 'can sync explore state with url' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')

      hash_regex = /datasets\/[^\/]+\/(.+)/
      expect(current_url).not_to match(hash_regex)
      url = current_url

      select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
      hash1 = wait_for do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])
      url = current_url

      select_h_select_option '.table-fields .ci-empty-field', value: "#{data_set.data_models.first.id}$!id"
      hash2 = wait_for do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id'])
      url = current_url

      select_h_select_option '.table-fields .ci-empty-field', label: 'Price'
      hash3 = wait_for do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id', 'Price'])
      url = current_url

      page.go_back
      wait_expect(hash2) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id'])
      url = current_url

      select_h_select_option '.table-fields .ci-empty-field', label: 'Price'
      wait_expect(hash3) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id', 'Price'])
      url = current_url

      page.all('.table-fields .ci-viz-field-select .h-icon[data-icon="cancel"]').last.click
      wait_expect(hash2) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id'])
      url = current_url

      page.go_back
      wait_expect(hash3) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id', 'Price'])
      url = current_url

      page.go_forward
      wait_expect(hash2) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id'])
      url = current_url

      page.all('.table-fields .ci-viz-field-select .h-icon[data-icon="cancel"]').last.click
      sleep 0.2
      page.all('.table-fields .ci-viz-field-select .h-icon[data-icon="cancel"]').last.click
      wait_expect(nil) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq([])
      url = current_url

      page.go_back
      # we removed fields quickly, so the url should have not synced to hash1
      wait_expect(hash2) do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id'])
      url = current_url

      select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
      hash4 = wait_for do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id', 'Status'])
      expect([nil, hash1, hash2, hash3, hash4].uniq.size).to eq(5)

      visit url
      begin
        page.driver.browser.switch_to.alert.accept
      rescue StandardError
        nil
      end

      wait_expect(['Status', 'Id']) do
        page.all('.table-fields .ci-field-info .field-label').map(&:text)
      end
    end

    it 'is synced when exploring conditions tab' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      hash_regex = /datasets\/[^\/]+\/(.+)/
      test_condition_url_sync(hash_regex)
    end

    context 'caching v2' do
      before do
        FeatureToggle.toggle_global('dataset_explorer:viz_caching_v2', true)
      end

      it 'can sync explore state with url, be refreshed using Refresh btn, and only create Jobs when necessary' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')

        job_count = Job.count

        hash_regex = /datasets\/[^\/]+\/(.+)/
        expect(current_url).not_to match(hash_regex)

        wait_for_element_load('.table-fields .ci-empty-field')
        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        hash1 = wait_for do
          current_url.match(hash_regex).try(:[], 1)
        end
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])
        url = current_url

        select_h_select_option '.table-fields .ci-empty-field', value: "#{data_set.data_models.first.id}$!id"
        hash2 = wait_for do
          next if current_url == url

          current_url.match(hash_regex).try(:[], 1)
        end
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status', 'Id'])
        expect(hash2).not_to eq(hash1)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(0)

        safe_click('.ci-explorer-control-get-results')
        wait_for do
          Job.count == job_count + 1
        end
        expect(Job.count).to eq(job_count + 1)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(1)

        url = current_url

        visit url
        begin
          page.driver.browser.switch_to.alert.accept
        rescue StandardError
          nil
        end
        wait_expect(['Status', 'Id']) do
          page.all('.table-fields .ci-field-info .field-label').map(&:text)
        end
        wait_for_element_load '.ci-pagination-info'
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1)
        expect(Job.count).to eq(job_count + 1)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(1)
        url = current_url

        # Get Result after changing FORMAT should not trigger any new Job
        safe_click('.ci-viz-setting-tab-format')
        select_h_select_option('.format-picker', label: 'Number (Rounded)')
        hash5 = wait_for do
          next if current_url == url

          current_url.match(hash_regex).try(:[], 1)
        end
        expect(hash5).not_to eq(hash2)
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1)
        expect(Job.count).to eq(job_count + 1)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(0)
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 2)
        expect(Job.count).to eq(job_count + 1)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(1)

        safe_click('.ci-viz-setting-tab-settings')
        select_h_select_option '.table-fields .ci-empty-field', label: 'Price'
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 2)
        expect(Job.count).to eq(job_count + 1)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(0)
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3)
        expect(Job.count).to eq(job_count + 2)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(1)

        # Clicking Get Result triggers a new ajax call, but not a new Job, because data is still cached on backend
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 4)
        expect(Job.count).to eq(job_count + 2)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(1)

        # Clicking Refresh triggers a new Job
        safe_click('.ci-dataset-explorer-refresh')
        wait_for_all_ajax_requests
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 5)
        expect(Job.count).to eq(job_count + 3)
        expect(page.all('.ci-dataset-explorer-refresh').size).to eq(1)
      end
    end

    context 'with AML datasets' do
      include_context 'aml_studio_dataset'

      it 'works' do # smoke testing
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')

        hash_regex = /datasets\/[^\/]+\/(.+)/
        expect(current_url).not_to match(hash_regex)

        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        wait_for do
          current_url.match(hash_regex).try(:[], 1)
        end
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])
        url = current_url

        select_h_select_option '.table-fields .ci-empty-field', value: 'data_modeling_orders$!id'
        wait_expect(['Status', 'Id']) do
          page.all('.table-fields .ci-field-info .field-label').map(&:text)
        end

        visit url
        begin
          page.driver.browser.switch_to.alert.accept
        rescue StandardError
          nil
        end
        wait_expect(['Status']) do
          page.all('.table-fields .ci-field-info .field-label').map(&:text)
        end
      end
    end
  end

  describe 'highlighting selected fields' do
    let(:model_products) { create_data_modeling_model_from_table(ds, 'products') }
    let(:model_orders) { create_data_modeling_model_from_table(ds, 'orders') }
    let(:model_users) { create_data_modeling_model_from_table(ds, 'users') }

    before do
      data_set.data_models = [
        model_products,
        model_orders,
        model_users,
      ]
      data_set.save!
    end

    it 'highlights selected fields and show count in pill' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')

      expect(page.all('.model-field').size).to eq(model_products.fields.size)
      expect(page.all('.model-field').size).to eq(7)
      expect(page.all('.model-field.selected').size).to eq 0

      safe_click(".ci-data-model-#{model_orders.id}")
      safe_click(".ci-data-model-#{model_users.id}")
      expect(page.all('.model-field').size).to eq(model_products.fields.size + model_orders.fields.size + model_users.fields.size)
      expect(page.all('.model-field.selected').size).to eq 0

      hash_regex = /datasets\/[^\/]+\/(.+)/

      wait_for_element_load('.table-fields .ci-empty-field')
      select_h_select_option '.table-fields .ci-empty-field', group: model_users.label, value: "#{model_users.id}$!id", scroll_to_find: true
      hash1 = wait_for do
        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Id'])
      expect(page.all('.model-field.selected').size).to eq 1
      expect(page.all('.model-field.selected').map(&:text)).to eq(['Id'])

      url = current_url
      visit url

      begin
        page.driver.browser.switch_to.alert.accept
      rescue StandardError
        nil
      end
      wait_for_element_load('.data-set-explorer')
      wait_expect(model_users.fields.size) do
        page.all('.model-field').size
      end
      wait_for_element_load('.model-field.selected')
      expect(page.all('.model-field.selected').map(&:text)).to match_array(['Id'])
      expect(page.all('.ci-data-model .model-selected-fields-count').size).to eq(1)
      expect(page.find(".ci-data-model-#{model_users.id} .model-selected-fields-count").text).to eq('1')

      wait_for_element_load('.table-fields .ci-empty-field')
      search_h_select('.table-fields .ci-empty-field', text: 'quantity')
      select_h_select_option '.table-fields .ci-empty-field', value: "#{model_orders.id}$!quantity"
      hash2 = wait_for do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.find(".ci-data-model-#{model_orders.id} .model-selected-fields-count").text).to eq('1') # selected field count on `orders` shows `1` even when `orders` is not expanded
      safe_click(".ci-data-model-#{model_orders.id}")
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Id', 'Quantity'])
      expect(hash2).not_to eq(hash1)
      expect(page.all('.model-field.selected').size).to eq 2
      expect(page.all('.model-field.selected').map(&:text)).to match_array(['Id', 'Quantity'])
      url = current_url

      search_h_select('.table-fields .ci-empty-field', text: 'user id')
      select_h_select_option '.table-fields .ci-empty-field', value: "#{model_orders.id}$!user_id"

      hash3 = wait_for do
        next if current_url == url

        current_url.match(hash_regex).try(:[], 1)
      end
      expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Id', 'Quantity', 'User Id'])
      expect(hash3).not_to eq(hash2)
      expect(page.all('.model-field').size).to eq(model_orders.fields.size + model_users.fields.size)
      expect(page.all('.model-field.selected').map(&:text)).to match_array(['Id', 'Quantity', 'User Id'])
      expect(page.all('.ci-data-model .model-selected-fields-count').size).to eq(2)
      expect(page.find(".ci-data-model-#{model_orders.id} .model-selected-fields-count").text).to eq('2')
      expect(page.find(".ci-data-model-#{model_users.id} .model-selected-fields-count").text).to eq('1')

      url = current_url

      wait_for_element_load('.data-set-explorer')
      visit url
      begin
        page.driver.browser.switch_to.alert.accept
      rescue StandardError
        nil
      end
      wait_for_element_load('.data-set-explorer')
      wait_expect(model_orders.fields.size + model_users.fields.size) do
        page.all('.model-field').size
      end
      wait_for_element_load('.model-field.selected')
      expect(page.all('.model-field.selected').map(&:text)).to match_array(['Id', 'Quantity', 'User Id'])

      expect(page.all('.ci-data-model .model-selected-fields-count').size).to eq(2)
      expect(page.find(".ci-data-model-#{model_orders.id} .model-selected-fields-count").text).to eq('2')
      expect(page.find(".ci-data-model-#{model_users.id} .model-selected-fields-count").text).to eq('1')

      safe_click(".ci-data-model-#{model_products.id}")
      expect(page.all('.model-field').size).to eq(model_products.fields.size + model_orders.fields.size + model_users.fields.size)
      expect(page.all('.model-field.selected').map(&:text)).to match_array(['Id', 'Quantity', 'User Id'])

      safe_click(".ci-data-model-#{model_orders.id}")
      expect(page.all('.model-field').size).to eq(model_products.fields.size + model_users.fields.size)
      expect(page.all('.model-field.selected').map(&:text)).to eq(['Id'])

      safe_click(".ci-data-model-#{model_users.id}")
      expect(page.all('.model-field').size).to eq(model_products.fields.size)
      expect(page.all('.model-field.selected').map(&:text)).to eq([])

      expect(page.all('.ci-data-model .model-selected-fields-count').size).to eq(2)
      expect(page.find(".ci-data-model-#{model_orders.id} .model-selected-fields-count").text).to eq('2')
      expect(page.find(".ci-data-model-#{model_users.id} .model-selected-fields-count").text).to eq('1')
    end
  end

  context 'when hovering on data set info icon' do
    it 'shows tooltip' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      wait_for_element_load('[data-ci="ci-data-set-info"]')
      page.first('[data-ci="ci-data-set-info"]').hover

      expect(page.find('[data-ci="ci-data-set-description"]')).to be_visible
    end
  end

  context 'hover on data model field' do
    def get_tooltip_content(selector)
      page.all(selector).map(&:text)
    end

    it 'shows tooltip when there is no description or long label' do
      data_model = data_set.data_models.first
      field = data_model.all_fields_sorted.first
      field.description = ''
      data_model.save

      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')
      page.first('.data-set-explorer .model-field').hover
      wait_for_element_load('.field-detail')
      page.find('.field-detail').hover

      wait_for_element_load('.ci-field-label')
      expect(page.find('.ci-field-label').text).to eq field.label
      expect(page.find('.ci-field-type').text).to eq field.type
      expect(page.find('.ci-field-description').text).to eq 'No Description'
      expect(page.find('.ci-formula').text).to eq field.sql
      expect(page.find('.ci-data-model-info').text).to eq data_model.label
      expect(page.find('.ci-action-url')[:href]).to end_with("data_models?ds=#{data_model.data_source_id}&model=#{data_model.id}&fieldName=#{field.name}")
    end

    it 'shows tooltip when hover tooltip icon' do
      data_model = data_set.data_models.first
      field = data_model.all_fields_sorted[1]
      field.description = 'Description'
      field.sql = '{{ SUM(#SOURCE.model + 1) }}'
      data_model.save

      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')
      wait_for { page.all('.data-set-explorer .model-field').count > 1 }
      page.all('.data-set-explorer .model-field')[1].hover
      wait_for_element_load('.field-detail')
      page.find('.field-detail').hover

      wait_for_element_load('.ci-field-label')
      expect(page.find('.ci-field-label').text).to eq field.label
      expect(page.find('.ci-field-type').text).to eq field.type
      expect(page.find('.ci-field-description').text).to eq field.description
      expect(page.find('.ci-formula').text).to eq field.sql
      expect(page.find('.ci-data-model-info').text).to eq data_model.label
      expect(page.find('.ci-action-url')[:href]).to end_with("data_models?ds=#{data_model.data_source_id}&model=#{data_model.id}&fieldName=#{field.name}")
    end

    it 'shows tooltip when the label is too long' do
      data_model = data_set.data_models.first
      field = data_model.fields.sort_by(&:name)[2]
      field.label = 'longggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggg'
      data_model.save

      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')

      wait_for { page.all('.data-set-explorer .model-field').count > 1 }
      page.all('.data-set-explorer .model-field')[2].hover
      wait_for_element_load('.field-detail')
      page.find('.field-detail').hover

      wait_for_element_load('.ci-field-label')
      expect(page.find('.ci-field-label').text).to eq field.label
      expect(page.find('.ci-field-type').text).to eq field.type
      expect(page.find('.ci-field-description').text).to eq 'No Description'
      expect(page.find('.ci-formula').text).to eq field.sql
      expect(page.find('.ci-data-model-info').text).to eq data_model.label
      expect(page.find('.ci-action-url')[:href]).to end_with("data_models?ds=#{data_model.data_source_id}&model=#{data_model.id}&fieldName=#{field.name}")
    end

    it 'renders an error message that shows the relationship guideline when missing relations' do
      orders = create_data_modeling_model_from_table(ds, 'orders')
      data_set = DataSet.create_from_models(
        data_models: [model, orders],
        title: 'New dataset',
        category_id: 0,
        data_source_id: model.data_source.id,
        owner_id: admin.id,
        tenant_id: tenant.id,
      )
      data_set.save!

      safe_login(:admin, data_set_path(data_set))
      safe_click(".data-models-list ##{model.name}-#{model.fields[0].name} .name-label")
      page.all('.data-models-list .ci-data-model')[1].click
      page.find(".data-models-list ##{orders.name}-#{orders.fields[0].name} .name-label").click
      safe_click '.ci-explorer-control-get-results'
      wait_for_all_ajax_requests
      error_message = page.find('.ci-message-container .ci-relationship-guide-message').text
      expect(error_message).to eq 'You need Relationships to combine data from different Data Models'
    end

    it 'can show relationship diagram' do
      orders = create_data_modeling_model_from_table(ds, 'orders')
      data_set = DataSet.create_from_models(
        data_models: [model, orders],
        title: 'New dataset',
        category_id: 0,
        data_source_id: model.data_source.id,
        owner_id: admin.id,
        tenant_id: tenant.id,
      )
      data_set.save!

      safe_login(:admin, data_set_path(data_set))

      safe_click('[data-ci="view-relationship"]')
      wait_for_element_load('.ci-tab-toggle')
      page.all('.ci-tab-toggle').last.click

      expect(page).to have_selector('.konvajs-content')
    end

    it 'is able to click on the data model url in new tab then open and focus on the correct field' do
      data_model = data_set.data_models.first
      field = data_model.all_fields_sorted.last
      field.description = ''
      data_model.save

      safe_login admin, data_set_path(data_set)
      wait_for { page.all('.data-set-explorer .model-field').count > 1 }
      page.all('.data-set-explorer .model-field').last.hover
      wait_for_element_load('.field-detail')
      page.find('.field-detail').hover

      wait_for_element_load('.ci-field-label')
      expect(page.find('.ci-field-label').text).to eq field.label
      expect(page.find('.ci-field-type').text).to eq field.type
      expect(page.find('.ci-field-description').text).to eq 'No Description'
      expect(page.find('.ci-formula').text).to eq field.sql
      expect(page.find('.ci-data-model-info').text).to eq data_model.label

      data_modeling_window = window_opened_by do
        safe_click('.ci-action-url')
      end
      switch_to_window(data_modeling_window)

      expected_field_id = "##{data_model.name}-#{field.name}:focus"
      wait_for_element_load('.ci-model-tabs')
      wait_for_element_load(expected_field_id)
      field_row = page.find(expected_field_id)
      expect(field_row.find_all('td').first.text).to eq field.name
    end
  end

  context 'explore data set' do
    include_context 'data_set'

    it 'shows no matching options if there is no searching results' do
      safe_login admin, data_set_path(data_set)
      search_h_select('.table-fields .ci-empty-field', text: 'abcxyz')
      expect(page.find('.hui-select-floating').text).to eq 'No options...'
    end

    it 'only show groups which contain the searching results' do
      safe_login admin, data_set_path(data_set)
      search_h_select('.table-fields .ci-empty-field', text: 'discount')
      expect(page.find('.hui-select-floating').text).to eq "Orders\nDiscount"
    end

    context 'timestamp field' do
      before do
        safe_login admin, data_set_path(data_set)
      end

      it 'matches not show time panel' do
        select_h_select_option('.static-filters .ci-viz-field-select', label: 'Created at')

        expect(page.all('.mx-datepicker-footer').count == 0).to be true
      end

      it 'is on not have time panel' do
        select_h_select_option('.static-filters .ci-viz-field-select', label: 'Created at')

        select_h_select_option('.ci-filter-form .ci-operator-select', value: 'is')
        safe_click('.date-picker-custom-input')
        expect(page.all('.mx-datepicker-footer').count == 0).to be true
      end

      it 'between show time panel' do
        select_h_select_option('.static-filters .ci-viz-field-select', label: 'Created at')

        select_h_select_option('.dm-filter-form .ci-operator-select', value: 'between')
        safe_click('.date-picker-custom-input')
        expect(page.all('.mx-datepicker-footer').count > 0).to be true
      end
    end
  end

  describe 'edit data set' do
    include_context 'data_set'

    context 'update from data set form' do
      it 'updates data models' do
        safe_login(:admin, data_set_path(data_set))
        safe_click('.ci-dataset-dropdown')
        safe_click('.ci-add-edit-models')
        wait_for_element_load('.model-select-wrapper')
        safe_click('.model-tree-select .tree-select-option-list .vue-recycle-scroller__item-view:last-child .tree-select-item')
        safe_click('.ci-save-btn')
        wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
        # sleep 1
        models = page.all('.ci-data-models-list .ci-data-model')

        expect(models.length).to eq(1)
      end

      it 'updates data set name' do
        safe_login(:admin, data_set_path(data_set))
        safe_click('.ci-dataset-dropdown')
        safe_click('.ci-rename')
        page.first('.ci-rename-input').set('abc_xyz')
        safe_click('.ci-submit-btn')

        wait_expect(0) do
          page.all('span.icon-spin', wait: false).count
        end

        wait_expect('abc_xyz') do
          page.find('.data-set-form .title-name').text
        end
        wait_expect('abc_xyz') do
          page.find('.ci-navigation-node .active .title').text
        end
      end
    end

    context 'update from navigation node' do
      it 'updates data models' do
        safe_login(:admin, data_set_path(data_set))
        wait_for_element_load('.ci-navigation-node')
        reliable_hover('.ci-navigation-node .active', '.navigation-node-action')
        safe_click('.ci-navigation-node .active .ci-navigation-node-action')
        safe_click('.navigation-node-action-popover .ci-add-edit-models')

        wait_for_element_load('.model-select-wrapper')
        safe_click('.model-tree-select .tree-select-option-list .vue-recycle-scroller__item-view:last-child .tree-select-item')
        safe_click('.ci-save-btn')
        wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')

        models = page.all('.ci-data-models-list .ci-data-model')

        expect(models.length).to eq(1)
      end

      it 'updates data set name' do
        safe_login(:admin, data_set_path(data_set))
        wait_for_element_load('.ci-navigation-node')
        reliable_hover('.ci-navigation-node .active', '.navigation-node-action')
        safe_click('.ci-navigation-node .active .ci-navigation-node-action')
        safe_click('.ci-navigation-node-action-popover .ci-rename')

        page.first('.ci-rename-input').set('abc_xyz')
        safe_click('.ci-submit-btn')
        wait_expect(0) do
          page.all('span.icon-spin', wait: false).count
        end
        expect(page.find('.data-set-form .title-name').text).to eq('abc_xyz')
      end
    end
  end

  describe 'Filter on aggregation' do
    include_context 'data_set'

    before do
      model = data_set.data_models.first

      # create custom measure
      model.external_fields << DataModeling::Explores::Measure.new(
        name: 'count_a',
        label: 'Count a',
        sql: 'count(data_modeling_products.id) - count(data_modeling_orders.id) * 1.0',
        syntax: 'aml',
        type: 'number',
        description: '',
        aggregation_type: 'custom',
        is_external: true,
      )
      model.save
    end

    def select_and_test_aggregation_select(field_label, disabled)
      select_h_select_option('.static-filters .ci-empty-field', label: field_label)
      wait_for_element_load('.ci-aggregation-select')
      expect(page.has_selector?('.ci-aggregation-select.hui-select-trigger.disabled')).to eq(disabled)
    end

    it 'can select aggregation for filter field' do
      safe_login(:admin, data_set_path(data_set))

      select_and_test_aggregation_select('Name', false)
    end

    it 'can select custom measure as filter, but cannot select aggregation for it' do
      safe_login(:admin, data_set_path(data_set))

      select_and_test_aggregation_select('Count a', true)
    end
  end

  describe 'Limit Dropdown' do
    include_context 'data_set'

    def set_and_expect_row_limit(limit)
      safe_click('.ci-limit-select-toggle')
      safe_click('.ci-custom-limit-input')
      fill_text('.ci-custom-limit-input', limit.to_s)
      safe_click('.ci-custom-limit-btn')
      safe_click('.ci-explorer-control-get-results')
      wait_for_all_holistics_loadings
      wait_for_all_ajax_requests
      executed_query_tab = page.find('.ci-tab-toggle', text: 'Executed SQL')
      executed_query_tab.click unless executed_query_tab[:class].include?('active')
      expect(page.find('.sql.hljs').all('span').last.text).to eql limit.to_s
    end

    context 'with FT_TENANT_VISUALIZATION_SETTING_V2 disabled' do
      before do
        FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, false)
      end

      it 'initial value is No limit, execute the query with the correct limit' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')

        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])

        # Get result with initial limit. The executed query  must be end up with LIMIT 200
        wait_for_element_load '.ci-limit-select-toggle'
        expect(page.find('.ci-limit-select-toggle').text).to eq 'No limit'
        safe_click('.ci-explorer-control-get-results')
        wait_for_element_load '.ci-pagination-info'
        executed_query_tab = page.find('.ci-tab-toggle', text: 'Executed SQL')
        executed_query_tab.click unless executed_query_tab[:class].include?('active')
        expect(page.find('.sql.hljs').all('span').last.text).to eql '100000'

        # Change the limit to 100. The executed query  must be end up with LIMIT 100
        set_and_expect_row_limit(100)
        # Change the limit to No Limit. The executed query  must be end up with LIMIT 100000
        set_and_expect_row_limit(100000)
      end
    end

    context 'with FT_TENANT_VISUALIZATION_SETTING_V2 enabled' do
      before do
        FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, true)
      end

      it 'initial value is 5k for tables' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')

        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])

        wait_for_element_load '.ci-limit-select-toggle'
        expect(page.find('.ci-limit-select-toggle').text).to eq 'Limit 5000'
        safe_click('.ci-explorer-control-get-results')
        wait_for_element_load '.ci-pagination-info'
        executed_query_tab = page.find('.ci-tab-toggle', text: 'Executed SQL')
        executed_query_tab.click unless executed_query_tab[:class].include?('active')
        expect(page.find('.sql.hljs').all('span').last.text).to eql '5000'

        # show row limit warning when limit = results
        set_and_expect_row_limit(2)
        expect(page).to have_css('[data-ci="row-limit-warning"]')

        # do not show row limit warning when limit > results
        set_and_expect_row_limit(3)
        expect(page).to_not have_css('[data-ci="row-limit-warning"]')

        # show row limit warning when limit < results
        set_and_expect_row_limit(1)
        expect(page).to have_css('[data-ci="row-limit-warning"]')
      end

      it 'initial value is 5k for charts' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')
        safe_click('.ci-viz-type-line_chart')
        page.first('.placeholder-container').click # click outside to hide popover

        select_h_select_option '.viz-section:nth-child(1) .ci-x-axis-row', label: 'Status'
        select_h_select_option '.viz-section:nth-child(3) .ci-add-y-axis-row', label: 'Created at'

        wait_for_element_load '.ci-limit-select-toggle'
        expect(page.find('.ci-limit-select-toggle').text).to eq 'Limit 5000'
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_holistics_loadings
        wait_for_all_ajax_requests
        wait_for_element_load('.ci-tab-toggle', text: 'Executed SQL')
        executed_query_tab = page.find('.ci-tab-toggle', text: 'Executed SQL')
        executed_query_tab.click unless executed_query_tab[:class].include?('active')
        expect(page.find('.sql.hljs').all('span').last.text).to eql '5000'

        # show row limit warning when limit = results
        set_and_expect_row_limit(2)
        expect(page).to have_css('[data-ci="row-limit-warning"]')

        # do not show row limit warning when limit > results
        set_and_expect_row_limit(3)
        expect(page).to_not have_css('[data-ci="row-limit-warning"]')

        # show row limit warning when limit < results
        set_and_expect_row_limit(1)
        expect(page).to have_css('[data-ci="row-limit-warning"]')
      end
    end
  end

  describe 'DataSet Explorer leave confirmation' do
    include_context 'data_set'

    context 'when user does not explore yet' do
      xit 'does not show the confirmation modal when clicking the cancel button' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')
        safe_click('.ci-cancel-ds-based-report')
        expect(page.first('.ci-home-user-name').text).to eq('admin')
      end

      it 'does not show the confirmation modal when changing route' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')
        safe_click('.ci-home-btn')
        expect(page.first('.ci-home-user-name').text).to eq('admin')
      end
    end

    context 'when user exploring dataset' do
      xit 'shows the confirmation modal when clicking the cancel button' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')
        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])
        safe_click('.ci-explorer-control-get-results')
        # wait for some progress
        sleep 1
        safe_click('.ci-cancel-ds-based-report')
        safe_click('.ci-close')
        safe_click('.ci-cancel-ds-based-report')
        safe_click('.ci-confirm')
        expect(page.first('.ci-home-user-name').text).to eq('admin')
      end

      it 'shows the confirmation modal when changing route' do
        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')
        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])
        safe_click('.ci-explorer-control-get-results')
        # wait for some progress
        sleep 1
        safe_click('.ci-home-btn')
        safe_click('.ci-close')
        safe_click('.ci-home-btn')
        safe_click('.ci-confirm')
        expect(page.first('.ci-home-user-name').text).to eq('admin')
      end

      it 'shows the confirmation modal when changing dataset' do
        orders = create_data_modeling_model_from_table(ds, 'orders')
        orders_dataset = DataSet.create_from_models(
          data_models: [model, orders],
          title: 'orders dataset',
          category_id: 0,
          data_source_id: model.data_source.id,
          owner_id: admin.id,
          tenant_id: tenant.id,
        )
        orders_dataset.save!

        safe_login admin, data_set_path(data_set)
        wait_for_element_load('.data-set-explorer')
        select_h_select_option '.table-fields .ci-empty-field', label: 'Status'
        expect(page.all('.table-fields .ci-field-info .field-label').map(&:text)).to eq(['Status'])
        safe_click('.ci-explorer-control-get-results')
        wait_for_all_holistics_loadings
        safe_click('.ci-navigation-node', text: 'orders dataset')
        wait_for_element_load('[data-hui-section="dismiss-button"]')
        safe_click('[data-hui-section="dismiss-button"]')
        safe_click('.ci-navigation-node', text: 'orders dataset')
        safe_click('[data-hui-section="resolve-button"]')
        wait_for_element_load('.ci-data-set-form-title')
        expect(page.first('.ci-data-set-form-title').text).to eq('orders dataset')
      end
    end
  end

  describe 'loading states' do
    before do
      FeatureToggle.toggle_global('loading_progress:use_elapsed_time', true)
    end

    include_context 'simple_query_model_dataset'
    it 'show correct loading with pending states', js: true do
      sql = get_sql_sleep_pg(3)
      query_data_model.backend.update!(query: sql)
      data_set.update!(data_models: [query_data_model])

      job = FactoryBot.create(:job, status: 'running', tenant: dashboard.tenant, tag: 'adhoc_query')
      ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '1'
      Capybara.current_window.resize_to 1600, 1000
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      select_h_select_option('.table-fields', value: "#{data_set.data_models.first.id}$!age")

      safe_click('.ci-explorer-control-get-results')
      expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1)

      wait_for { page.first('.job-queue-status__display-text').text.include?('pending') } # check pending status

      Thread.new do
        Job.last._send_to_workers
      end
      ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '0'

      wait_for { page.first('.v-loading-text').text.include?('Running query') } # check loading status
      wait_for { page.first('.v-loading-progress').text.include?('s') } # check loading status using elapsed time
      wait_for_all_holistics_loadings # viz results loaded
    end
  end

  describe 'Cancel' do
    before do
      tenant.update!(settings: { unused_report_jobs_ttl: 10 })

      ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '1'
      # create a job to block queue
      FactoryBot.create(:job, status: 'running', tenant: dashboard.tenant, tag: 'adhoc_query')
    end

    it 'cancels current job' do
      safe_login admin, data_set_path(data_set)
      wait_for_element_load('.data-set-explorer')
      select_h_select_option('.table-fields', value: "#{data_set.data_models.first.id}$!name")
      safe_click('.ci-explorer-control-get-results')
      expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1)
      wait_for { page.first('.job-queue-status__display-text').text.include?('pending') } # check pending status
      job = Job.last
      expect(job.status).to eq('created')

      # test cancel
      safe_click('[data-ci="explorer-control-cancel"]')
      expect(page).to have_css('.error-container', text: 'The job has been canceled')
      expect(job.reload.status).to eq('cancelled')

      # run another job to make sure the UI is still functional
      ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '0'
      safe_click('.ci-explorer-control-get-results')
      wait_for_viz_load
      values = page.all('.ci-table-report-data .ag-cell.ag-column-last').map(&:text)
      expect(values).to eq(['milk', 'bread', 'egg'])
    end
  end
end

def test_condition_url_sync(hash_regex)
  # Add condition
  url = current_url

  make_condition('Category Id', '1')
  wait_for_element_load('.ci-viz-static-filters')
  wait_expect(["Category Id\n  equal 1"]) do
    page.all('.ci-viz-static-filters .ci-dm-filter').map(&:text)
  end
  # wait for route change
  hash_with_category_id_condition = wait_for do
    next if current_url == url

    current_url.match(hash_regex).try(:[], 1)
  end

  url = current_url
  # Add condition
  make_condition('Merchant Id', '1')
  wait_for_element_load('.ci-viz-static-filters')
  wait_expect(["Category Id\n  equal 1", "Merchant Id\n  equal 1"]) do
    page.all('.ci-viz-static-filters .ci-dm-filter').map(&:text)
  end
  # wait for route change
  hash_with_merchant_id_condition = wait_for do
    next if current_url == url
    current_url.match(hash_regex).try(:[], 1)
  end

  url = current_url
  # Modify condition
  page.all('.ci-dm-filter').last.click
  safe_click('.ci-value-select')
  page.first('.ci-value-select input').send_keys('2')
  page.first('.ci-value-select input').send_keys(:enter)
  page.first('.viz-section-header').click
  wait_for_element_load('.ci-viz-static-filters')
  wait_expect(["Category Id\n  equal 1", "Merchant Id\n  equal 1, 2"]) do
    page.all('.ci-viz-static-filters .ci-dm-filter').map(&:text)
  end
  # wait for route change
  hash_with_2_merchant_id_conditions = wait_for do
    next if current_url == url
    current_url.match(hash_regex).try(:[], 1)
  end

  page.go_back
  sleep 0.5
  check_condition_urls(hash_with_merchant_id_condition,
                       ["Category Id\n  equal 1", "Merchant Id\n  equal 1"],
                       hash_regex,)

  page.go_forward
  check_condition_urls(hash_with_2_merchant_id_conditions,
                       ["Category Id\n  equal 1", "Merchant Id\n  equal 1, 2"],
                       hash_regex,)

  # Remove conditions
  page.go_back
  check_condition_urls(hash_with_merchant_id_condition,
                       ["Category Id\n  equal 1", "Merchant Id\n  equal 1"],
                       hash_regex,)

  page.go_back
  check_condition_urls(hash_with_category_id_condition, ["Category Id\n  equal 1"], hash_regex)

  # Remove all conditions
  page.go_back
  check_condition_urls(nil, [], hash_regex)
end

def make_condition(label, key)
  select_h_select_option '.ci-viz-filter-select', label: label
  page.first('.ci-value-select input').send_keys(key)
  page.first('.ci-value-select input').send_keys(:enter)
  page.first('.viz-section-header').click
end

def check_condition_urls(hash_id, condition_state, hash_regex)
  wait_expect(hash_id) do
    current_url.match(hash_regex).try(:[], 1)
  end
  wait_expect(condition_state) do
    page.all('.ci-viz-static-filters .ci-dm-filter').map(&:text)
  end
end
