# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, stable: true do
  include_context 'billing/hard_restriction'

  describe 'upload csv' do
    it 'upload csv in data manager' do
      safe_login(admin, '/data_manager')
      ensure_usage_alert_is_shown
      wait_for_element_load('.ci-upload-csv')
      safe_click('.ci-dismiss-data-manager-docs')
      safe_click('.ci-upload-csv')
      restriction_modal_should_be_shown(true)
    end
  end

  describe 'analyst should be restricted' do
    it 'create report' do
      safe_login(analyst, '/browse')
      ensure_usage_alert_is_shown
      # Button (+) in header
      page.find('.top-header .ci-quicklinks').click
      page.find('.quick-link-popover .new-report').click
      restriction_modal_should_be_shown(true)
      # Button (+ Create) on right bar
      page.find('.ci-create').click
      page.find('.ci-create-new-report').click
      restriction_modal_should_be_shown(true)
    end
  end
end
