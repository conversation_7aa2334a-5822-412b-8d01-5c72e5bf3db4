# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, legacy: true do
  include_context 'billing/hard_restriction'

  describe 'data transform' do
    let!(:data_transform) {
      FactoryBot.create :data_transform, tenant_id: admin.tenant_id
    }

    it 'new data transform' do
      safe_login(admin, '/data_transforms')
      ensure_usage_alert_is_shown
      # Button (+) in header
      page.find('.ci-quicklinks.header-item-btn').click
      page.find('.new-transform').click
      restriction_modal_should_be_shown(true)
      # Button transform
      page.find('.ci-btn-new-transform').click
      restriction_modal_should_be_shown(true)
    end

    it 'new data transform via direct link' do
      safe_login(admin, '/data_transforms/new')
      ensure_usage_alert_is_shown
      expect_disabled('#ci-save') # btn save disabled
      expect_disabled('#ci-validate-query') # btn validate query
    end

    it 'edit data transform' do
      safe_login(admin, "/data_transforms/#{data_transform.id}/edit")
      ensure_usage_alert_is_shown
      expect_disabled('#ci-save') # btn save disabled
      expect_disabled('#ci-validate-query') # btn validate query
    end

    it 'new schedule on edit page' do
      safe_login(admin, "/data_transforms/#{data_transform.id}/edit")
      ensure_usage_alert_is_shown
      wait_for_element_load('#ci-add-schedule')
      safe_click('#ci-add-schedule')
      # btn done in schedule is disabled
      expect_disabled('.ci-schedule-done')
    end

    it 'new schedule on management page' do
      safe_login(admin, '/data_transforms')
      ensure_usage_alert_is_shown
      wait_for_element_load('.ci-transform-schedules')
      safe_click('.ci-transform-schedules')
      # btn done in schedule is disabled
      expect_disabled('.ci-new-schedule')
    end
  end
end
