# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, legacy: true do
  include_context 'billing/hard_restriction'

  describe 'data import' do
    let(:from_ds) { mysql_testdb_ds }
    let(:dest_ds) { get_test_ds }
    let(:from_fqname) { FQName.parse 'foobar' }
    let(:from_fqname2) { FQName.parse 'foobar2' }
    let(:columns) do
      [
        { column_name: 'col_0', data_type: 'bigint' },
        { column_name: 'col_1', data_type: 'tinyint(2)' },
        { column_name: 'col_2', data_type: 'float' },
        { column_name: 'col_3', data_type: 'varchar(25)' },
      ]
    end
    let(:title) { 'Sample DB to DB Import' }
    let(:dest_fqname) { FQName.parse('public.test_di_dest') }
    let(:dest_fqname2) { FQName.parse('public.test_di_dest_2') }
    let(:data_import) {
      FactoryBot.create :data_import,
                         source_type: DataImport::SOURCE_TYPE[:DATABASE],
                         source_config: {
                           dbtable: { ds_id: from_ds.id, fqname: from_fqname.to_unquoted_s },
                         },
                         dest_ds_id: dest_ds.id,
                         dest_schema_name: dest_fqname.schema_name,
                         dest_table_name: dest_fqname.table_name,
                         table_config: { columns: columns }
    }

    before do
      FeatureToggle.toggle_global(::DataImport::FT_V1_CREATION, true)
      Cache.clear from_ds.cacher.all_columns_cache_key
      ds_create_table(connector: Connectors.from_ds(from_ds), columns: columns, fqname: from_fqname, rows: [])
      ds_create_table(connector: Connectors.from_ds(from_ds), columns: columns, fqname: from_fqname2, rows: [])
    end

    after do
      ds_drop_table(connector: Connectors.from_ds(from_ds), fqname: from_fqname)
      ds_drop_table(connector: Connectors.from_ds(from_ds), fqname: from_fqname2)
    end

    it 'new data import one click import' do
      safe_login(admin, '/data_imports')
      ensure_usage_alert_is_shown
      # Button (+ Create) on the left
      page.find('.ci-new-data-import').click
      restriction_modal_should_be_shown(true)
    end

    it 'direct link to new data import' do
      safe_login(admin, '/data_imports/new')
      ensure_usage_alert_is_shown
      select_source_table(from_ds, from_fqname)
      select_dest_table(dest_ds, dest_fqname)
      expect_disabled('.ci-validate-dest') # btn validate query
      expect_disabled('.ci-submit-import') # btn save disabled
    end

    it 'edit data import' do
      safe_login(admin, "/data_imports/#{data_import.id}/edit")
      ensure_usage_alert_is_shown
      expect_disabled('.ci-validate-dest') # btn validate query
      expect_disabled('.ci-submit-import') # btn save disabled
      expect_disabled('.ci-add-schedule') # btn add schedule disabled
    end

    context 'enabled one click import' do
      before do
        FeatureToggle.toggle_global('one_click_import', true)
      end

      it 'new data import in header' do
        safe_login(admin, '/data_imports')
        ensure_usage_alert_is_shown
        wait_for_element_load('.ci-quicklinks.header-item-btn')
        sleep 3 # Need to sleep to sync usage
        # Button (+) in header
        page.find('.ci-quicklinks.header-item-btn').click
        page.find('.new-import').click
        restriction_modal_should_be_shown(true)
        # Button (+ Create) on the left
        page.find('.ci-new-data-import').click
        # Button single import
        page.find('.ci-single-import').click
        restriction_modal_should_be_shown(true)
        # Button batch import
        page.find('.ci-new-data-import').click
        page.find('.ci-batch-import').click
        restriction_modal_should_be_shown(true)
      end
    end
  end
end
