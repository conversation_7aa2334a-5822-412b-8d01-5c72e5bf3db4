# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, stable: true do
  include_context 'billing/hard_restriction'

  describe 'data schedule' do
    def create_es(source)
      schedule = FactoryBot.create :schedule, start_date: Time.new, repeat: '* * * * *'
      FactoryBot.create :email_schedule, schedule: schedule,
                                          tenant: tenant,
                                          recipients: %w[foo@abc bar@abc],
                                          source: source
    end

    context 'in report' do
      it 'can not add new report email/slack/sftp/google_sheet schedule' do
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown

        safe_click('.ci-data-schedules-dropdown')
        safe_click('.ci-new-email-dest')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-data-schedules-dropdown')
        safe_click('.ci-new-slack-dest')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-data-schedules-dropdown')
        safe_click('.ci-new-gsheet-dest')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-data-schedules-dropdown')
        safe_click('.ci-new-sftp-dest')
        restriction_modal_should_be_shown(true)
      end

      it 'restrict report schedule manager' do
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown

        # Schedule management
        safe_click('.ci-data-schedules-dropdown')
        page.find('.ci-manage-schedules').click

        safe_click('.ci-new-schedule-btn')
        safe_click('.ci-emaildest-schedule')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-new-schedule-btn')
        safe_click('.ci-slackdest-schedule')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-new-schedule-btn')
        safe_click('.ci-gsheetdest-schedule')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-new-schedule-btn')
        safe_click('.ci-sftpdest-schedule')
        restriction_modal_should_be_shown(true)
      end

      it 'update report data schedule' do
        create_es(report)

        safe_login(admin, report_url)
        ensure_usage_alert_is_shown

        # Schedule management
        safe_click('.ci-data-schedules-dropdown')
        page.find('.ci-manage-schedules').click
        wait_for_element_load('.ci-data-schedule-list')

        # Click on button pause
        page.first('.ci-edit-schedule').hover
        safe_click('.ci-pause', { visible: false })

        # Click on button play
        page.find('.schedule-description').hover
        safe_click('.ci-play')
        restriction_modal_should_be_shown(true)

        # Edit schedule
        safe_click('.ci-actions')
        safe_click('.ci-es-edit')
        expect_disabled('.ci-es-done') # btn save
      end
    end

    context 'in dashboard' do
      it 'restrict dashboard schedule manager' do
        safe_login(admin, dashboard_url)
        ensure_usage_alert_is_shown

        # open data schedule manager
        safe_click('.ci-email-schedules')
        safe_click('.ci-new-schedule-btn')
        safe_click('.ci-emaildest-schedule')
        restriction_modal_should_be_shown(true)

        safe_click('.ci-new-schedule-btn')
        safe_click('.ci-slackdest-schedule')
        restriction_modal_should_be_shown(true)
      end

      it 'update dashboard data schedule' do
        create_es(dashboard)

        safe_login(admin, dashboard_url)
        ensure_usage_alert_is_shown

        # Open data schedule manager
        wait_for_element_load('.ci-email-schedules')
        safe_click('.ci-email-schedules')
        wait_for_element_load('.ci-data-schedule-list')

        # Click on button pause
        page.first('.ci-edit-schedule').hover
        safe_click('.ci-pause')

        # Click on button play
        page.find('.schedule-description').hover
        safe_click('.ci-play')
        restriction_modal_should_be_shown(true)

        # Edit schedule
        safe_click('.ci-actions')
        safe_click('.ci-es-edit')
        expect_disabled('.ci-es-done') # btn save
      end
    end
  end
end
