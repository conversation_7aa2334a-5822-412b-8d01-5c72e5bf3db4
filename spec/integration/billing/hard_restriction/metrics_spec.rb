# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, legacy: true do
  include_context 'billing/hard_restriction'

  describe 'metrics' do
    let(:pageviews_sql) {
      <<-SQL.strip_heredoc
        SELECT SUM(cnt)
        FROM test_pageviews
        WHERE {{time_where}}
      SQL
    }

    before do
      pg_create_pageviews(test_ds)
    end

    def can_not_update_metric
      wait_for_element_load('.ci-save-metrics')
      wait_expect(true) { page.find('.ci-save-metrics').disabled? } # btn save disabled
      expect(page.find('.ace-editor-wrapper')[:class].include?('read-only')).to be true # editor read-only
    end

    it 'should show restriction banner when clicking on button New' do
      safe_login(admin, '/metrics/manage')
      ensure_usage_alert_is_shown

      page.find('.ci-add-metric').click
      restriction_modal_should_be_shown(true)

      page.find('.ci-quicklinks.header-item-btn').click
      page.find('.new-metric').click
      restriction_modal_should_be_shown(true)
    end

    it 'should disable editor and button save when editting' do
      FactoryBot.create :query_metric,
                          title: 'Pageviews',
                          query: pageviews_sql,
                          time_field: 'date_d',
                          tenant_id: tenant.id,
                          data_source_id: test_ds.id

      safe_login(admin, '/metrics/manage')
      ensure_usage_alert_is_shown

      page.find('table tr:nth-child(1) td:nth-child(6) .ci-edit-metric').click
      can_not_update_metric
    end

    it 'should disable editor and button save when open direct link' do
      safe_login(admin, '/metrics/manage?openModal=true')
      ensure_usage_alert_is_shown
      reload_page # work around to show edit modal
      can_not_update_metric
    end
  end
end
