# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, legacy: true do
  include_context 'billing/hard_restriction'

  describe 'legacy report' do
    let(:legacy_report) { FactoryBot.create :query_report, title: 'Test report', query: 'values (1,2,3)' }
    let(:legacy_report_url) { query_report_path(legacy_report) }

    def legacy_report_query_editor_is_disabled
      wait_for_element_load('.ci-report-preview')
      expect_disabled('.ci-report-preview') # btn preview disabled
      expect(page.find('.ace-editor-wrapper')[:class].include?('read-only')).to be true # editor read-only
      expect_disabled('#upper-submit-button') # btn save disabled
      expect_disabled('#upper-save-only-button') # btn save disabled
    end

    def legacy_report_disable_filter_select
      click('.ci-filter-tab')
      expect_disabled('.ci-add-adhoc-filter-btn') # btn New Filter disabled
      expect_disabled('a', text: 'Add Filter Template') # btn Add Filter Template disabled
      expect_disabled('.ci-report-preview') # btn preview disabled
    end

    it 'direct link to legacy report' do
      safe_login(admin, '/queries/legacy_new')
      ensure_usage_alert_is_shown
      legacy_report_query_editor_is_disabled
      legacy_report_disable_filter_select
    end

    it 'edit legacy report' do
      safe_login(admin, legacy_report_url)
      ensure_usage_alert_is_shown

      wait_for_element_load('.ci-edit-report-link')
      click('.ci-edit-report-link')
      legacy_report_query_editor_is_disabled
      clear_page_unload_events
    end
  end
end
