# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, stable: true do
  include_context 'billing/hard_restriction'

  describe 'query report should show restriction banner when' do
    it 'click on button new' do
      safe_login(admin, '/browse')
      ensure_usage_alert_is_shown

      # Button (+) in header
      page.find('.ci-quicklinks.header-item-btn').click
      page.find('.new-report').click
      restriction_modal_should_be_shown(true)

      # Button (+ Create) on right bar
      page.find('.ci-create').click
      page.find('.ci-create-new-report').click
      restriction_modal_should_be_shown(true)

      # Button (+) on left sidebar
      open_left_sidebar
      page.find('.draggable-main .section__title', text: 'Reports & Dashboards').hover
      safe_click '.ci-navigation-node .add'
      sleep 1 # wait for popup
      click_js('.navigation-node-action-popover [data-icon=report]') # rspec can not click this element
      restriction_modal_should_be_shown(true)
    end

    context 'adhoc query' do
      before do
        query = 'SELECT 1 AS foo, 2 as bar'
        FactoryBot.create :adhoc_query, query: query, state: 'success', tenant: get_test_tenant, owner: admin
      end

      it 'click save on query editor' do
        safe_login(:admin, '/adhoc')
        ensure_usage_alert_is_shown
        # Fill query
        page.find('.ci-query-editor').click
        wait_for_element_load('#adhoc-editor')
        fill_in_ace '#adhoc-editor', 'values(1,2,3)'
        page.find('.ci-run-query').click
        wait_for_viz_load
        # Button save
        page.find('.ci-save-adhoc').click
        restriction_modal_should_be_shown
      end
    end

    context 'copy report' do
      it 'copy to in report' do
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown

        wait_for_element_load '.ci-toggle'
        safe_click'.ci-toggle'
        safe_click'.ci-report-copy-to'
        restriction_modal_should_be_shown
      end

      it 'copy to in left side bar' do
        safe_login(admin, '/browse')
        ensure_usage_alert_is_shown

        open_left_sidebar
        page.find('#navigation-node-Dashboard-1').hover
        page.find('#navigation-node-Dashboard-1 .draggable-main > .dragging-content .ci-navigation-node-action').click
        page.find('.ci-navigation-node-copy-to').click
        restriction_modal_should_be_shown
      end
    end

    context 'edit report' do
      def report_query_editor_is_disabled
        wait_for_element_load('.ci-save-report')
        expect(page.find('.ace-editor-wrapper')[:class].include?('read-only')).to be true # editor read-only
        expect_disabled('.ci-save-report') # btn save disabled
      end

      it 'click save on editting report' do
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown

        wait_for_element_load('.ci-edit-report-link')
        safe_click('.ci-edit-report-link')

        report_query_editor_is_disabled
        clear_page_unload_events
      end

      it 'edit adhoc report' do
        safe_login(admin, dashboard_url)
        ensure_usage_alert_is_shown

        wait_for_element_load('.widget-wrapper')
        page.first('.widget-wrapper').hover
        safe_click('.ci-widget-controls')
        safe_click('.ci-widget-edit')

        report_query_editor_is_disabled
        clear_page_unload_events
      end

      it 'direct link to new Report' do
        safe_login(admin, '/queries/new')
        ensure_usage_alert_is_shown
        report_query_editor_is_disabled
      end
    end
  end
end
