# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, stable: true do
  include_context 'billing/hard_restriction'

  describe 'schedule cache' do
    def toggle_auto_preload
      # open cache settings
      wait_for_element_load('.cache-status')
      page.find('.cache-status').hover
      wait_for_element_load('.cache-status__popover')
      page.find('.cache-status__popover').click_button('Cache Settings')
      # toggle auto preload
      wait_for_element_load('.ci-auto-preload')
      click('.ci-auto-preload')
      click('.ci-auto-preload')
    end

    context 'in report' do
      let(:report_with_cache) { FactoryBot.create :query_report_with_cache, owner: admin }
      let(:report_url) { query_report_path(report_with_cache) }
      before do
        FactoryBot.create :scheduled_cache_preload, source: report_with_cache
        report_with_cache.reload
      end
      it 'should be restricted' do
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown
        toggle_auto_preload
        restriction_modal_should_be_shown(true)
      end
    end

    context 'in dashboard' do
      let(:dashboard_with_auto_preload) { FactoryBot.create :dashboard_with_cache, owner: admin }
      let(:dashboard_url) { dashboard_path(dashboard_with_auto_preload) }
      before do
        FactoryBot.create :scheduled_cache_preload, source: dashboard_with_auto_preload
        dashboard_with_auto_preload.reload
      end
      it 'should be restricted' do
        safe_login(admin, dashboard_url)
        ensure_usage_alert_is_shown
        toggle_auto_preload
        restriction_modal_should_be_shown(true)
      end
    end
  end
end
