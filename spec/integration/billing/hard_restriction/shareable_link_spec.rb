# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Billing: Hard restriction', js: true, stable: true do
  include_context 'billing/hard_restriction'

  def create_shareable_link_from(resource)
    sl = create :shareable_link, resource: resource, owner_id: admin.id
    sl.set_public_user
    sl.share_resource
  end

  def open_shareable_links_modal
    wait_for_element_load('.ci-share-dropdown')
    safe_click('.ci-share-dropdown')
    safe_click('.ci-shareable-links')
  end

  def click_new_shareable_link
    wait_and_click('.ci-new-shareable-link')
  end

  describe 'shareable link should show restriction banner' do
    context 'in dashboard' do
      it 'when create first shareable link' do
        safe_login(admin, dashboard_url)
        ensure_usage_alert_is_shown
        open_shareable_links_modal
        click_new_shareable_link
        restriction_modal_should_be_shown
      end

      it 'when create link in dashboard' do
        create_shareable_link_from(dashboard)
        safe_login(admin, dashboard_url)
        ensure_usage_alert_is_shown
        open_shareable_links_modal
        click_new_shareable_link
        restriction_modal_should_be_shown
      end
    end

    context 'in query report' do
      it 'when create first shareable link' do
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown
        open_shareable_links_modal
        click_new_shareable_link
        restriction_modal_should_be_shown
      end

      it 'when create link in report' do
        create_shareable_link_from(report)
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown
        open_shareable_links_modal
        click_new_shareable_link
        restriction_modal_should_be_shown
      end

      it 'when update shareable link' do
        create_shareable_link_from(report)
        safe_login(admin, report_url)
        ensure_usage_alert_is_shown
        open_shareable_links_modal
        wait_and_click('.ci-edit-link')
        wait_for_element_load('.ci-save-links')
        expect_disabled('.ci-save-links')
      end
    end
  end
end
