# typed: false

require 'rails_helper'

describe 'Usage Alert header', js: true, stable: true do
  let!(:test_ds) { get_test_ds }
  let(:tenant) { get_test_tenant }
  let (:admin) { users(:admin) }
  let (:analyst) { users(:analyst) }
  let (:business) { users(:bizuser) }
  let!(:plan) { FactoryBot.create :plan }
  let!(:tsub) {
    FactoryBot.create :tenant_subscription,
                       tenant: tenant,
                       status: 'active',
                       expired_at: Time.now + 10.days,
                       plan: plan,
                       period_type: 'annually',
                       max_restriction_level: 'hard',
                       embed_workers: 10 # to exceed usage
  }
  let(:embed_usage_rule) { FactoryBot.create :usage_rule, name: 'embed_worker' }
  let!(:plan_embed_rule) {
    FactoryBot.create :plan_usage_rule,
      plan: plan,
      usage_rule: embed_usage_rule,
      per_additional_usage: 300
  }
  let(:admin_msg_hard_mode) { 'Certain functionalities will be disabled soon. Please upgrade your plan to continue using Holistics.' }
  let(:admin_msg_soft_mode) { 'Certain functionalities will be disabled soon. Please upgrade your plan to continue using Holistics.' }
  let(:analyst_msg_hard_mode) { 'Certain functionalities will be disabled soon. Please contact the admin to upgrade your plan.' }
  let(:analyst_msg_soft_mode) { 'Certain functionalities will be disabled soon. Please contact the admin to upgrade your plan.' }

  def not_see_any_alert
    expect(page).not_to have_selector('#usage-alert-message')
    expect(page).not_to have_selector('.ci-upgrade-button')
  end

  def see_usage_alert(message)
    wait_for_element_load('#usage-alert-message')
    expect(page.find('#usage-alert-message').text).to eq(message)
  end

  def not_see_upgrade_button
    expect(page).not_to have_selector('.ci-upgrade-button')
  end

  def see_upgrade_button
    expect(page).to have_selector('.ci-upgrade-button')
  end

  describe 'Restriction Level: NONE' do
    before do
      tsub.max_restriction_level = 'none'
      tsub.save!
    end

    it 'should not show for admin' do
      safe_login(admin, '/browse')
      not_see_any_alert
    end

    it 'should not show for analyst' do
      safe_login(analyst, '/browse')
      not_see_any_alert
    end

    it 'should not show for business' do
      safe_login(business, '/browse')
      not_see_any_alert
    end
  end

  describe 'Restriction Level: SOFT' do
    before do
      tsub.max_restriction_level = 'soft'
      tsub.save!
    end

    it 'should show for admin' do
      safe_login(admin, '/browse')
      see_usage_alert(admin_msg_soft_mode)
      see_upgrade_button
    end

    it 'should show for analyst' do
      safe_login(analyst, '/browse')
      see_usage_alert(analyst_msg_soft_mode)
      not_see_upgrade_button
    end

    it 'should not show for business' do
      safe_login(business, '/browse')
      not_see_any_alert
    end
  end

  describe 'Restriction Level: HARD' do
    before do
      tsub.max_restriction_level = 'hard'
      tsub.save!
    end

    it 'should show for admin' do
      safe_login(admin, '/browse')
      see_usage_alert(admin_msg_hard_mode)
      expect(page).to have_selector('.ci-upgrade-button')
      see_upgrade_button
    end

    it 'should show for analyst' do
      safe_login(analyst, '/browse')
      see_usage_alert(analyst_msg_hard_mode)
      not_see_upgrade_button
    end

    it 'should show for business' do
      safe_login(business, '/browse')
      see_usage_alert(analyst_msg_hard_mode)
      not_see_upgrade_button
    end
  end
end
