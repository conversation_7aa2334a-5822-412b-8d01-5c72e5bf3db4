require 'rails_helper'

describe 'create dashboard', :js do
  include_context 'aml_studio_basic'
  include_context 'aml_studio_dev_mode'

  let(:user) { get_test_admin }
  let(:dashboard_extension) { AmlStudio::Values::DASHBOARD_EXTENSION }

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
  end

  it 'creates dashboard successfully' do
    safe_login user, '/studio/projects/1/explore'

    wait_for_element_load('.bottom-bar-container')
    page.first('.ci-aml-add').click
    # Click the add dataset
    item = page.all('.node-creation-menu-item')
    item[3].click
    # Fill data for modal
    wait_for_element_load('.ci-title-input')
    sleep(1) # wait for the input in node tree to be fully focused :hang:
    page.find('.ci-title-input').send_keys('test_dashboard').send_keys(:enter)

    wait_for_element_load(".node-content[title=\"test_dashboard#{dashboard_extension}\"]")
    new_dashboard = proj_work_flow.read_file("test_dashboard#{dashboard_extension}")
    expect(new_dashboard[:oid]).to eq('c14a62fb6e1135566d3903c6f45daa1e2adb6967')
  end
end
