# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Click on dest table name', js: true, legacy: true do
  let(:user) { get_test_admin }
  let(:src_table) { FQName.parse('public.import_src') }
  let(:dst_table) { FQName.parse('public.import_dest') }
  let(:test_ds) { get_test_ds }
  let(:connector) { Connectors.from_ds(test_ds) }
  let(:columns) do
    [
      { column_name: 'date_d', data_type: 'date' },
      { column_name: 'registrations', data_type: 'integer' },
      { column_name: 'views', data_type: 'integer' }
    ]
  end
  let(:rows) do
    [['2013-01-01', 100, 200],
     ['2013-01-02', 200, 400],
     ['2013-01-03', 300, 600]]
  end

  before do
    connector.drop_table src_table
    connector.drop_table dst_table
    ds_create_table(connector: connector, columns: columns, rows: rows, fqname: src_table)
  end

  after do
    connector.drop_table src_table
    connector.drop_table dst_table
  end

  describe 'Data Import' do
    before do
      di = FactoryBot.create :data_import, owner: user,
                                            tenant: user.tenant,
                                            source_type: 'dbtable',
                                            source_config: {
                                              dbtable: {
                                                ds_id: test_ds.id,
                                                fqname: src_table.to_s
                                              }
                                            },
                                            dest_ds_id: test_ds.id,
                                            dest_schema_name: dst_table.schema_name,
                                            dest_table_name: dst_table.table_name,
                                            table_config: { columns: columns }

      di.async.execute
    end

    it 'Directs user to data manager page' do
      safe_login(user, '/data_imports')

      wait_for_element_load '.group-name'
      page.first('.group-name').click

      wait_for_element_load '.ci-dest-table'
      page.first('.ci-dest-table').click

      wait_for_element_load '.schema-name'
      page.should have_css '.schema-table'
    end
  end

  describe 'Data Transform' do
    before do
      dt = FactoryBot.create :data_transform,
                              table_config: {
                                columns: columns,
                                dist_key: '', sort_keys: nil,
                                dist_style: 'EVEN', sort_style: 'NONE',
                                increment_column: 'views'
                              },
                              query: "select * from #{src_table}",
                              data_source_id: test_ds.id,
                              data_dest_id: test_ds.id,
                              dest_schema_name: dst_table.schema_name,
                              dest_table_name: dst_table.table_name,
                              settings: {
                                mode: 'materialized_incremental'
                              }

      dt.execute
    end

    it 'Create an adhoc query and redirect user to result page' do
      safe_login(user, '/data_transforms')

      wait_for_element_load '.ci-dest-table'
      new_window = window_opened_by { safe_click('.ci-dest-table') }

      within_window new_window do
        wait_for_viz_load
        # table need time to resize and show content
        wait_for_element_load('.ci-table-report-data')
        values = page.first('.ci-table-report-data').all('.ag-cell').map(&:text)
        expect(values).to eq(['Jan 01 2013', '100', '200', 'Jan 02 2013', '200', '400', 'Jan 03 2013', '300', '600'])
      end
    end
  end
end
