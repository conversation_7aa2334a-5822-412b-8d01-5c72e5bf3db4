# typed: false
require 'rails_helper'

describe 'List input filter', js: true, legacy: true do
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }
  let(:filter) { FactoryBot.create :list_sf }
  let(:report) { FactoryBot.create :query_report }

  before do
    filter.settings.merge!({ help_text: 'Just a help text' })
    filter.save!

    FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: 'date_range'
    FactoryBot.create :tenant_subscription, tenant_id: ds.tenant_id, status: 'active'

    sign_in(:admin)
    visit '/shared_filters'
  end

  def updateListInputFilter
    wait_for_element_load('.ci-sf-list')
    page.find('.ci-sf-list').set('Another help text')
    select_h_select_option('.ci-items-limit-control', value: 100)
  end

  it 'it should be create success list input filter' do

    safe_click('.ci-filter-name')
    wait_expect('Just a help text') { page.find('.ci-sf-list').value }
  end

  it 'it should be update success' do

    safe_click('.ci-filter-name')
    updateListInputFilter
    safe_click('.ci-shared-filter-save')

    wait_expect('Filter template updated successfully') { page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text }
  end
end
