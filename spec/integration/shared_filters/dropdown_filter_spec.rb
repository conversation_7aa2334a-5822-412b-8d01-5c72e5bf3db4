# typed: false
require 'rails_helper'

describe 'Dropdown Shared Filter', js: true, legacy: true do
  let (:admin) {get_test_admin}
  let (:ds) {get_test_ds}
  let (:filter_sql) {
    "values ('vnd','Viet Nam Dong'),
            ('\"usd\"','US Dollar'),
            ('thb','Baht'),
            ('sgd','Singapore Dollar')"
  }
  let (:settings) {
    {
      :type => "dropdown",
      :hide_all => false,
      :permissions => '',
      :data_source_id => ds.id,
      :date_max_ds_id => ds.id,
      :date_min_ds_id => ds.id,
      :dropdown_source => "sql",
      :parent_filter_id => nil,
      :dropdown_multiselect => false,
      :dropdown_sql => filter_sql
    }
  }
  let (:multiple_filter) {
    settings[:dropdown_multiselect] = true
    FactoryBot.create :dropdown_shared_filter, name: 'multi', settings: settings
  }
  let (:single_filter) {
    settings[:dropdown_multiselect] = false
    FactoryBot.create :dropdown_shared_filter, name: 'single', settings: settings
  }
  let (:lazy_filter) {
    settings[:dropdown_multiselect] = false
    settings[:dropdown_lazy_loading] = true
    FactoryBot.create :dropdown_shared_filter, name: 'lazy', settings: settings
  }

  before do
    report = FactoryBot.create :query_report,
                                query: "select * from ( #{filter_sql} ) R where [[column1 IN ({{DropdownSharedFilter}})]]",
                                data_source: ds
    FactoryBot.create :filter_ownership, filterable: report, shared_filter: multiple_filter, var_name: "DropdownSharedFilter"
    FactoryBot.create :tenant_subscription, tenant_id: ds.tenant_id, status: 'active'

    sign_in(:admin)
    visit "/shared_filters"
  end

  it 'it should create dropdown filter options' do
    safe_click('.ci-filter-name')
    safe_click('.ci-sf-preview')
    safe_click('.ci-multi-dropdown-filter')

    expect(page.all('.ci-multi-dropdown-filter .dropdown-filter__dropdown .dropdown-filter__options .dropdown-filter__option').length).to eq 4
  end

  it 'it should be update success' do
    safe_click('.ci-filter-name')
    h_checkbox_check(selector: '.ci-sf-multiple-dropdown')
    sleep 1
    safe_click('.ci-shared-filter-save')

    wait_expect('Filter template updated successfully') { page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text }
  end
end
