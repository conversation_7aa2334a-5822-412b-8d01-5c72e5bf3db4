# typed: false
require 'rails_helper'

describe 'Date filter', js: true, legacy: true do
  let (:admin) { get_test_admin }
  let (:ds) { get_test_ds }
  let(:filter) { FactoryBot.create :date_range_shared_filter }
  let(:report) { FactoryBot.create :query_report }

  before do
    filter.settings.merge!(
      {
        date_min: '2017-01-11', date_max: '2017-02-01', default_start: '2017-01-18', default_end: '2017-01-25'
      }
    )
    filter.save!

    FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: 'date_range'
    FactoryBot.create :tenant_subscription, tenant_id: ds.tenant_id, status: 'active'

    test_sign_in(:admin)
    visit '/shared_filters'
  end

  def wait_for_date_select_popover_close
    wait_for { page.all('.ci-date-input').count == 0 }
  end

  def update_date_filter
    wait_for_element_load '.date-select-popover'

    page.all('.date-select-popover')[0].click
    wait_for_element_load('.ci-date-input')
    page.find('.ci-date-input').set('2017-01-19')
    page.find('.h-modal-header').click # click outside to close popover

    wait_for_date_select_popover_close

    page.all('.date-select-popover')[1].click
    wait_for_element_load('.ci-date-input')
    page.find('.ci-date-input').set('2017-01-26')
    page.find('.h-modal-header').click # click outside to close popover

    wait_for_date_select_popover_close

    page.all('.date-select-popover')[2].click
    wait_for_element_load('.ci-date-input')
    page.find('.ci-date-input').set('2017-01-12')
    page.find('.h-modal-header').click # click outside to close popover

    wait_for_date_select_popover_close

    page.all('.date-select-popover')[3].click
    wait_for_element_load('.ci-date-input')
    page.find('.ci-date-input').set('2017-03-01')
    page.find('.h-modal-header').click # click outside to close popover
  end

  it 'it should be contains old data to edit' do
    safe_click('.ci-filter-name')

    wait_for_element_load '.date-select-popover'
    expect(page.all('.date-select-popover')[0].first('.filter-input').text).to eq '2017-01-18'
    expect(page.all('.date-select-popover')[1].first('.filter-input').text).to eq '2017-01-25'
    expect(page.all('.date-select-popover')[2].first('.filter-input').text).to eq '2017-01-11'
    expect(page.all('.date-select-popover')[3].first('.filter-input').text).to eq '2017-02-01'
  end

  it 'it should be update success' do
    safe_click('.ci-filter-name')
    update_date_filter
    safe_click('.ci-shared-filter-save')

    wait_expect('Filter template updated successfully') { page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text }
  end
end
