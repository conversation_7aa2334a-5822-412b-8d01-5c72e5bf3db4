# typed: false
require 'rails_helper'

describe 'Text filter', js: true, legacy: true do
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }
  let(:filter) { FactoryBot.create :text_sf }
  let(:report) { FactoryBot.create :query_report }
  let(:test_settings) do
    { default: 'This is a test', help_text: 'Just a help text' }
  end

  before do
    filter.settings.merge!(test_settings)
    filter.save!
    FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: 'date_range'
    FactoryBot.create :tenant_subscription, tenant_id: ds.tenant_id, status: 'active'
  end

  def edit_filter_name
    wait_for_element_load('.ci-filter-name')
    page.find('.ci-filter-name').click
  end

  def update_text_filter
    page.find('.ci-text-sf-default').set('Another test')
    page.find('.ci-text-sf-help').set('Another help text')
  end

  it 'it should be create success text filter' do
    safe_login(:admin, '/shared_filters')
    edit_filter_name
    wait_expect(test_settings[:default]) { page.find('.ci-text-sf-default').value }
    wait_expect(test_settings[:help_text]) { page.find('.ci-text-sf-help').value }
  end

  it 'it should be update success' do
    safe_login(:admin, '/shared_filters')
    edit_filter_name
    update_text_filter
    page.find('.ci-shared-filter-save').click
    expect_notifier_content('filter template updated successfully')
  end
end
