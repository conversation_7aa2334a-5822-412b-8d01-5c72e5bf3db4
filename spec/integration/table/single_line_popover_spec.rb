require 'rails_helper'

describe 'Data exploration specs', js: true, stable: true do
  let(:report) {
    FactoryBot.create :report_with_table_fields_long_value
  }

  let(:pivot_report) {
    FactoryBot.create :pivot_table_with_custom_labels
  }

  # compare content in cell and popover content
  def compare_popover_content(cell, popover)
    cell_content = cell.text
    expect(cell.text).to eq(popover.first('.content').first('div.ci-popover-content').text)
  end

  # wait, find and hover element for DELAY_SHOWING_POPOVER + 0.1s
  def find_and_hover(selector)
    wait_for_element_load selector
    cell = page.first selector
    cell.hover
    cell
  end

  def wait_for_popover(rendered = true)
    wait_expect(rendered ? 1 : 0) do
      page.all('.ci-single-line-row-popover').count
    end
  end

  context 'show popover on hovering' do
    before do
      create :feature_toggle, key: 'table:single_row', toggle_mode: 'enabled'
    end

    describe 'report' do
      before do
        qlogin :admin, query_report_path(report)
      end

      it 'show/not show' do
        # hover long-content cell
        long_cell = find_and_hover '.ag-cell:not(.ag-column-first)'
        wait_for_popover

        popover = page.first('.ci-single-line-row-popover')
        compare_popover_content(long_cell, popover)

        # hover header to close popover
        find_and_hover 'div.col-header'

        # hover short-content cell
        find_and_hover '.ag-row:nth-child(3) .ag-cell:not(.ag-column-first)'
        wait_for_popover(false)
      end

      it 'still show after click' do
        # hover long-content cell
        long_cell_selector = '.ag-cell:not(.ag-column-first)'
        wait_for_element_load long_cell_selector
        long_cell = page.first long_cell_selector
        long_cell.click
        long_cell.hover
        wait_for_popover
        popover = page.first('.ci-single-line-row-popover')
        compare_popover_content(long_cell, popover)
      end

      it 'not show after hover another cell' do
        # hover long-content cell
        long_cell_selector = '.ag-cell:not(.ag-column-first)'
        wait_for_element_load long_cell_selector
        long_cell = page.first long_cell_selector
        long_cell.hover

        # hover another cell
        second_cell_selector = '.ag-row:nth-child(3) .ag-cell:not(.ag-column-first)'
        second_cell = page.first second_cell_selector
        second_cell.hover

        # not show popover
        wait_for_popover(false)

        long_cell.hover
        wait_for_popover
        popover = page.first('.ci-single-line-row-popover')
        compare_popover_content(long_cell, popover)
      end
    end
  end

  context 'not show popover on hovering' do
    describe 'report' do
      before do
        report.viz_setting.settings[:misc][:row_height] = "Multiple lines"
        report.viz_setting.save!
        qlogin :admin, query_report_path(report)
      end

      it 'not show popover on hovering' do
        # hover long-content cell
        find_and_hover '.ag-cell:not(.ag-column-first)'
        wait_for_popover(false)
      end
    end
    describe 'report and FT off' do
      before do
        create :feature_toggle, key: 'table:single_row', toggle_mode: 'disabled'
        report.viz_setting.settings[:misc][:row_height] = "Single line"
        report.viz_setting.save!
        qlogin :admin, query_report_path(report)
      end

      it 'not show popover on hovering' do
        # hover long-content cell
        find_and_hover '.ag-cell:not(.ag-column-first)'
        wait_for_popover(false)
      end
    end
  end
end
