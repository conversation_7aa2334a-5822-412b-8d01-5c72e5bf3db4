require 'rails_helper'

describe 'Data exploration specs', js: true, stable: true do
  include_context 'test_tenant'
  let(:report) {
    FactoryBot.create :report_with_table_fields_long_value
  }

  let(:pivot_report) {
    FactoryBot.create :pivot_table_with_custom_labels
  }

  # wait, find and hover element for DELAY_SHOWING_POPOVER + 0.1s
  def find_and_open_dropdown(header_index)
    header_element_index = header_index + 1
    selector = "thead tr th:nth-child(#{header_element_index}) .header-dropdown-icon"
    safe_click(selector)
  end

  def compare_values(column, expected_values)
    column_values = page.all(".ci-table-report-data .ag-row .ag-cell:nth-child(#{column + 1})").map {|i| i.text.to_i}
    expect(column_values).to eq expected_values
  end

  describe 'normal table' do
    before do
      FeatureToggle.toggle_global('viz:table_v2', true)
      FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
    end

    it 'open report show correct icon' do
      safe_login admin, query_report_path(report)
      # expect column 2 have asc sort icon
      wait_expect(true) do
        page.has_selector?('[data-ci="ci-arrow-up"]')
      end
    end

    describe 'report behavior' do
      before do
        report.viz_setting.settings[:sort][:column] = -1
        report.viz_setting.save!
        report
        qlogin :admin, query_report_path(report)
      end

      it 'show dropdown' do
        # hover long-content cell
        wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'
        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        open_context_menu_by_name(element: table, header_name: 'column2')
        wait_expect(true) do
          page.has_selector?('.ci-sort-asc-btn')
        end
        # dont have remove
        wait_expect(true) do
          page.has_no_selector?('.ci-sort-remove-btn')
        end

        # sort asc
        sort_asc_btn = page.first '.ci-sort-asc-btn'
        sort_asc_btn.click
        wait_for_all_ajax_requests
        wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'
        compare_values(2, [1, 2, 3, 123])
        safe_click('.show-multiple-sort-popover-btn') # close sort
        wait_expect(true) do
          page.has_selector?('[data-ci="ci-arrow-up"]')
        end

        # sort desc
        open_context_menu_by_name(element: table, header_name: 'column2')
        sort_desc_btn = page.first '.ci-sort-desc-btn'
        sort_desc_btn.click
        wait_for_all_ajax_requests
        wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'
        compare_values(2, [123, 3, 2, 1])
        safe_click('.show-multiple-sort-popover-btn') # close sort
        wait_expect(true) do
          page.has_selector?('[data-ci="ci-arrow-down"]')
        end

        # other sort headers not show remove btn
        open_context_menu_by_name(element: table, header_name: 'column1')
        wait_expect(true) do
          page.has_no_selector?('.ci-sort-remove-btn')
        end

        # current sort header show remove btn
        open_context_menu_by_name(element: table, header_name: 'column2')
        wait_expect(true) do
          page.has_selector?('.ci-sort-remove-btn')
        end

        # after remove sort
        sort_remove_btn = page.first '.ci-sort-remove-btn'
        sort_remove_btn.click
        wait_for_all_ajax_requests
        wait_for_element_load '[data-ci="ci-ag-grid-data-table"]'
        compare_values(2, [1, 3, 2, 123])
      end
    end
  end

  describe 'pivot table' do
    describe 'open report' do
      before do
        FeatureToggle.toggle_global('viz:pivot_v2', true)
        FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
        pivot_report.viz_setting.settings[:sort] = {
          column: 0,
          sortOrder: true,
        }
        pivot_report.viz_setting.save!
        pivot_report
        qlogin :admin, query_report_path(pivot_report)
      end
      it 'show correct icon' do
        icon_selector = '.ag-header-cell [data-ci="ci-arrow-up"] [data-icon]'
        wait_for_element_load icon_selector
        icon = page.first icon_selector
        wait_expect(true) do
          icon.native.attribute('data-icon') == 'arrow-up'
        end
      end
    end

    describe 'open report' do
      before do
        FeatureToggle.toggle_global('viz:pivot_v2', true)
        FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
        qlogin :admin, query_report_path(pivot_report)
      end
      it 'row work correctly' do
        # sort row asc
        wait_for_element_load '[data-ci="ci-ag-grid-pivot-table"]'
        open_context_menu_by_name(element: page.find('[data-ci="ci-ag-grid-pivot-table"]'), header_name: 'a')
        desc_btn = page.first('.ci-sort-desc-btn')
        desc_btn.click

        wait_for_all_ajax_requests
        wait_for_element_load '[data-ci="ci-ag-grid-pivot-table"]'

        icon_selector = '.ag-header-cell [data-ci="ci-arrow-down"] [data-icon]'
        icon = page.first(icon_selector)
        wait_expect(true) do
          icon.native.attribute('data-icon') == 'arrow-down'
        end
      end

      it 'column work correctly' do
        wait_for_element_load '[data-ci="ci-ag-grid-pivot-table"]'
        open_context_menu_by_name(element: page.find('[data-ci="ci-ag-grid-pivot-table"]'), header_name: 'a')
        desc_btn = page.first('.ci-sort-desc-btn')
        desc_btn.click
        wait_for_all_ajax_requests
        wait_for_element_load '[data-ci="ci-ag-grid-pivot-table"]'

        icon_selector = '.ag-header-cell [data-ci="ci-arrow-down"] [data-icon]'
        wait_for_element_load icon_selector
        icon = page.first(icon_selector)
        wait_expect(true) do
          icon.native.attribute('data-icon') == 'arrow-down'
        end
      end
    end
  end
end
