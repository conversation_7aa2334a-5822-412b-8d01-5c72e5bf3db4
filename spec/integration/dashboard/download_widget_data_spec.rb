# typed: false
require 'rails_helper'

describe 'download widget data', js: true, stable: true do
  let(:report) {
    query = <<-SQL.strip_heredoc
      VALUES ('2013-01-01', 100, 200),
             ('2013-01-02', 200, 400),
             ('2013-01-03', 300, 600)
    SQL
    report = FactoryBot.create :query_report, title: 'Test', query: query
    FactoryBot.create :viz_setting, source: report

    report
  }
  let(:dashboard) {
    FactoryBot.create :dashboard
  }
  let(:dashboard_widget) {
    FactoryBot.create :dashboard_widget, dashboard: dashboard, source: report, title: 'widget'
  }
  before do
    dashboard_widget
  end

  def test_export(format, without_cache: false, test_friendly_name: false, friendly_filename: dashboard_widget.title)
    wait_for_element_load '.ci-table-report-data'
    ReportCache.flushall if without_cache
    wait_for_element_load('.widget-wrapper')
    page.find('.widget-wrapper').hover
    safe_click '.widget-wrapper .ci-widget-controls'
    safe_click ".ci-download-#{format}"
    wait_for_element_load '.ci-download-link'
    metadata = test_download_link(test_friendly_name: test_friendly_name, extension: format, friendly_filename: friendly_filename)
    expect(metadata.base_uri.to_s).to match /#{format}$/
  end

  def test_expand_mode_export(format, without_cache: false, test_friendly_name: false, friendly_filename: dashboard_widget.title)
    wait_for_element_load '.ci-table-report-data'
    ReportCache.flushall if without_cache
    wait_for_element_load '.widget-wrapper'
    page.first('.widget-wrapper').hover
    wait_for_element_load '.widget-wrapper .btn-expand-view'
    safe_click '.widget-wrapper .btn-expand-view'
    wait_for_element_load '.ci-table-report-data'

    wait_for_element_load '.ci-toggle-download'
    safe_click '.ci-toggle-download'
    safe_click ".ci-download-#{format}"

    wait_expect(true, 20) do
      raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

      page.all('.ci-download-link').present?
    end
    metadata = test_download_link(test_friendly_name: test_friendly_name, extension: format, friendly_filename: friendly_filename)
    expect(metadata.base_uri.to_s).to match /#{format}$/
  end

  describe 'signed in users' do
    describe 'via widget dropdown' do
      %w(xlsx csv).each do |format|
        it "successfully export file in #{format}" do
          qlogin(:admin, dashboard_path(dashboard))
          safe_click 'body'
          test_export format
        end
      end

      it "successfully export file in csv with friendly filename" do
        FeatureToggle.toggle_global('exportings:friendly_filename', true)
        qlogin(:admin, dashboard_path(dashboard))
        safe_click 'body'
        test_export 'csv', test_friendly_name: true
        FeatureToggle.toggle_global('exportings:friendly_filename', false)
      end

      it 'successfully export file in csv with friendly filename as report title when dashboard widget title is blank' do
        dashboard_widget.title = ''
        dashboard_widget.save!

        FeatureToggle.toggle_global('exportings:friendly_filename', true)
        qlogin(:admin, dashboard_path(dashboard))
        safe_click 'body'
        test_export 'csv', test_friendly_name: true, friendly_filename: report.title
        FeatureToggle.toggle_global('exportings:friendly_filename', false)
      end

      it 'successfully export file in csv with friendly filename when title contains special characters' do
        dashboard_widget.title = "Naïve ;f'i\"l,e.txt"
        dashboard_widget.save!

        FeatureToggle.toggle_global('exportings:friendly_filename', true)
        qlogin(:admin, dashboard_path(dashboard))
        safe_click 'body'
        test_export 'csv', test_friendly_name: true
        FeatureToggle.toggle_global('exportings:friendly_filename', false)
      end

      context 'without cache' do
        it "successfully export file in excel" do
          qlogin(:admin, dashboard_path(dashboard))
          safe_click 'body'
          test_export 'xlsx', without_cache: true
          expect(Job.last.logs.any? {|l| l.message.include?('Using cached data')}).to eq false
        end

        it "successfully export file in excel with friendly filename" do
          FeatureToggle.toggle_global('exportings:friendly_filename', true)
          qlogin(:admin, dashboard_path(dashboard))
          safe_click 'body'
          test_export 'xlsx', without_cache: true, test_friendly_name: true
          expect(Job.last.logs.any? {|l| l.message.include?('Using cached data')}).to eq false
          FeatureToggle.toggle_global('exportings:friendly_filename', false)
        end
      end

      context 'pivot table widget' do
        let(:report) {
          qr = FactoryBot.create :pivot_report
          QueryReports::LegacyVizConverter.new(qr).execute.save
          qr.query.sub!('10000', '10')
          qr.save
          qr
        }
        before do
          dashboard_widget.data = {:is_adhoc=>nil, :row_limit=>15, :view_mode=>"viz"}
          dashboard_widget.save
        end
        it "successfully export file in excel" do
          qlogin(:admin, dashboard_path(dashboard))
          safe_click 'body'
          test_export 'xlsx'
          expect(Job.last.logs.any? {|l| l.message.include?('Using cached data')}).to eq true
        end
      end
    end

    describe 'in expanded mode' do
      %w(xlsx csv).each do |format|
        it "successfully export file in #{format}" do
          qlogin(:admin, dashboard_path(dashboard))
          wait_for_element_load '.widget-title'
          safe_click '.widget-title' # click '.widget-title'
          test_expand_mode_export(format)
        end
      end

      context 'pivot table widget' do
        let(:report) {
          qr = FactoryBot.create :pivot_report
          QueryReports::LegacyVizConverter.new(qr).execute.save
          qr.query.sub!('10000', '10')
          qr.save
          qr
        }
        before do
          dashboard_widget.data = {:is_adhoc=>nil, :row_limit=>15, :view_mode=>"viz"}
          dashboard_widget.save
        end
        it "successfully export file in excel" do
          qlogin(:admin, dashboard_path(dashboard))
          safe_click 'body'
          test_expand_mode_export 'xlsx'
          expect(Job.last.logs.any? {|l| l.message.include?('Using cached data')}).to eq true
        end
      end
    end
  end

  describe 'public users' do
    before do
      FactoryBot.create :tenant_subscription, tenant: dashboard.tenant, status: 'active'
    end
    let (:shareable_link) {
      sl = FactoryBot.create :shareable_link, resource: dashboard, owner_id: get_test_admin.id
      sl.set_public_user
      sl.share_resource
      "#{dashboard_path dashboard}?_pl=#{sl.hash_code}"
    }
    let (:embed_link) {
      filter = FactoryBot.create :shared_filter, name: 'Input'
      FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: filter, var_name: 'test', is_hidden: false
      customer_fo = FactoryBot.create :filter_ownership, var_name: 'customer_key', filterable: dashboard, shared_filter: filter
      el = FactoryBot.create :embed_link, source: dashboard, owner_id: get_test_admin.id
      el.add_many(:filter_ownership, :embed_link_identifier_variable, customer_fo)
      el.set_public_user
      el.share_source
      token = jwt_encode(el.secret_key, { customer_fo.var_name => 123 }, Time.now.to_i + 24*60*60)
      "/embed/#{el.hash_code}?_token=#{token}"
    }

    describe 'via dashboard widget dropdown' do
      %w(xlsx csv).each do |format|
        it "successfully export file in #{format}" do
          visit embed_link
          safe_click 'body'
          test_export format
        end
      end
      it "successfully export file in csv to secure bucket" do
        FeatureToggle.toggle_global('exports:secure_bucket', true)
        visit embed_link
        safe_click 'body'
        wait_for_element_load '.ci-table-report-data'
        page.first('.widget-wrapper').hover
        safe_click '.widget-wrapper .ci-widget-controls'
        safe_click ".ci-download-csv"
        wait_for_element_load '.ci-download-link'
        download_link = page.first('.ci-download-link')['href'].to_s
        test_download_link
        FeatureToggle.toggle_global('exports:secure_bucket', false)
      end

      %w(xlsx csv).each do |format|
        it "successfully export file in csv" do
          visit shareable_link
          safe_click 'body'
          test_export format
        end
      end

      it "successfully export file in csv with friendly filename" do
        FeatureToggle.toggle_global('exportings:friendly_filename', true)
        visit shareable_link
        safe_click 'body'
        test_export 'csv', test_friendly_name: true
        FeatureToggle.toggle_global('exportings:friendly_filename', false)
      end
    end

    describe 'in expand mode' do
      %w(xlsx csv).each do |format|
        it "successfully export file in #{format}" do
          visit embed_link
          safe_click 'body'
          test_expand_mode_export format
        end
      end
      it "successfully export file in csv to secure bucket" do
        FeatureToggle.toggle_global('exports:secure_bucket', true)
        visit embed_link
        safe_click 'body'
        wait_for_element_load '.ci-table-report-data'
        page.first('.widget-wrapper').hover
        safe_click '.widget-wrapper .ci-widget-controls'
        safe_click ".ci-download-csv"
        wait_for_element_load '.ci-download-link'
        download_link = page.first('.ci-download-link')['href'].to_s
        test_download_link
        FeatureToggle.toggle_global('exports:secure_bucket', false)
      end

      %w(xlsx csv).each do |format|
        it "successfully export file in #{format}" do
          visit shareable_link
          safe_click 'body'
          test_expand_mode_export format
        end
      end
    end
  end
end
