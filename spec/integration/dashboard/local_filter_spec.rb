# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Dashboard Local Filters', js: true, stable: true do
  let (:admin) { get_test_admin }
  let (:user) { get_test_user }
  let (:ds) { get_test_ds }
  let (:country_filter) do
    FactoryBot.create :shared_filter, name: 'Country', tenant_id: admin.tenant_id, data_source_id: ds.id, is_adhoc: false,
                                       settings: {
                                         name: 'Country', type: 'dropdown', label: 'Country',
                                         data_source_id: ds.id, date_max_ds_id: ds.id, date_min_ds_id: ds.id,
                                         dropdown_source: 'manual', dropdown_manual_entries: "sg,Singapore\nvn,Viet Nam",
                                       }
  end
  let (:platform_filter) do
    FactoryBot.create :shared_filter, name: 'Platform', tenant_id: admin.tenant_id, data_source_id: ds.id, is_adhoc: false,
                                       settings: {
                                         name: 'Platform', type: 'dropdown', label: 'Platform',
                                         data_source_id: ds.id, date_max_ds_id: ds.id, date_min_ds_id: ds.id,
                                         dropdown_source: 'manual', dropdown_manual_entries: "ios,iOS\nandroid,Android",
                                       }
  end
  let (:seperator_filter) do
    FactoryBot.create :shared_filter, name: 'Separator', tenant_id: admin.tenant_id, data_source_id: ds.id, is_adhoc: false,
                                       settings: {
                                         name: 'Seperator', type: 'separator', label: 'Seperator',
                                         data_source_id: ds.id,
                                       }
  end

  let (:report) do
    query = <<~SQL
      with data as (
        select column1 as sales, column2 as country, column3 as platform
        from (values
          (12345, 'sg', 'ios'),
          (54321, 'sg', 'android'),
          (9876, 'vn', 'ios'),
          (9999, 'vn', 'android')
        ) t
      )
      select sum(sales) as the_number
      from data
      where [[country = {{country}}]] and [[platform = {{platform}}]]
      group by country, platform
    SQL

    FactoryBot.create :query_report, query: query, title: 'Dashboard Report that has Local Filters',
                                      data_source_id: ds.id, tenant_id: admin.tenant_id
  end
  let (:dashboard) { FactoryBot.create :dashboard, title: 'Reports have Local Filters', tenant_id: admin.tenant_id }

  def visit_dashboard
    safe_login :admin, dashboard_path(dashboard.id)
  end

  def expect_report_widget_data_table_to_have_rows(how_many)
    wait_expect(how_many) { page.all('.report-widget .ci-table-report-data .ag-row', wait: false).size }
  end

  def setup_dashboard_filters
    # without local filters, report widget should use dashboard filter values
    FactoryBot.create :filter_ownership, shared_filter_id: country_filter.id, var_name: 'country', filterable_id: dashboard.id, filterable_type: 'Dashboard'
    FactoryBot.create :filter_ownership, shared_filter_id: platform_filter.id, var_name: 'platform', filterable_id: dashboard.id, filterable_type: 'Dashboard'
    FactoryBot.create :filter_ownership, shared_filter_id: seperator_filter.id, var_name: 'separator', filterable_id: dashboard.id, filterable_type: 'Dashboard'

    report_widget = FactoryBot.create :dashboard_widget, dashboard: dashboard, tenant: dashboard.tenant, source_type: 'QueryReport', source_id: report.id
    FactoryBot.create :filter_value, filter_ownership: @platform_fo, filter_valuable: report_widget, value: { selected_value: 'android' }, settings: { filter_name: 'platform' }
    FactoryBot.create :filter_value, filter_ownership: @country_fo, filter_valuable: report_widget, value: { selected_value: 'vn' }, settings: { filter_name: 'country' }
    FactoryBot.create :filter_value, filter_ownership: @seperator_to, filter_valuable: report_widget
  end

  def select2(text, filter_name, base_selector = nil)
    wait_for { page.all("#{base_selector} .ci-report-filter-#{filter_name} .select2-container").count > 0 }
    page.find("#{base_selector} .ci-report-filter-#{filter_name} .select2-container").click
    page.find('input.select2-search__field').set(text)
    wait_for_element_load 'ul.select2-results__options .select2-results__option'
    page.first('ul.select2-results__options .select2-results__option').click
    sleep 0.5
  end

  def select_first_report
    safe_click('.ci-toggle-add-widget')
    safe_click('.ci-report-widget')
    sleep 1
    select_h_select_option('.ci-select-report', label: QueryReport.first.title)

    sleep 1
  end

  def open_edit_widget_modal
    wait_for_element_load('.ci-widget-controls', 10, { visible: false})
    safe_click('.ci-widget-controls', { visible: false })
    safe_click('.ci-widget-settings', { visible: false })
    sleep 3
  end

  def submit_edit
    click_sel '.h-modal-footer button'
    sleep 2
  end

  def check_local_filter(name)
    open_edit_widget_modal
    wait_for_element_load '.h-checkbox'
    h_checkbox_check(label: name)
    sleep 3
  end

  before do
    FactoryBot.create :tenant_subscription, tenant: admin.tenant, status: 'active'

    FilterOwnership.delete_all
    @country_fo = FactoryBot.create :filter_ownership, shared_filter_id: country_filter.id, var_name: 'country', filterable_id: report.id, filterable_type: 'QueryReport'
    @platform_fo = FactoryBot.create :filter_ownership, shared_filter_id: platform_filter.id, var_name: 'platform', filterable_id: report.id, filterable_type: 'QueryReport'
    @seperator_to = FactoryBot.create :filter_ownership, shared_filter_id: seperator_filter.id, var_name: 'seperator', filterable_id: report.id, filterable_type: 'QueryReport'
  end

  context 'when displaying report widgets' do
    before { setup_dashboard_filters }

    it 'should use local filter values and ignore dashboard filter values' do
      visit_dashboard

      # if local filters are applied correctly, report should have a single number, not data table
      wait_for_element_load('.ci-friendly-number')
      expect_report_widget_data_table_to_have_rows 0
      expect(page.find('.ci-friendly-number').text).to eq '10.00K'
    end
  end

  context 'during report widget creation' do
    def set_platform_local_filter_to_android
      h_checkbox_check(label: 'Platform')
      select2('android', 'platform')
    end

    it 'should allow adding local values of report filters' do
      visit_dashboard
      select_first_report

      set_platform_local_filter_to_android

      page.first('.ci-report-widget-modal .ci-submit-btn').click

      wait_for { Dashboard.last.dashboard_widgets.count > 0 }

      sleep 1

      local_filters = Dashboard.last.dashboard_widgets.first.filter_values

      expect(local_filters.size).to eq 1
      expect(local_filters.first.value).to eq(selected_value: 'android')
      expect(local_filters.first.filter_ownership_id).to eq @platform_fo.id
      expect(local_filters.first.settings).to eq(filter_name: @platform_fo.to_hash[:name])

      expect_report_widget_data_table_to_have_rows 2
    end
  end

  # for some reasons css selector fails
  context 'during report widget edit' do
    before { setup_dashboard_filters }

    it 'should allow editing local values of report filters' do
      visit_dashboard
      sleep 3 # load time

      page.find('.report-widget').hover
      check_local_filter('Country') && submit_edit
      expect_report_widget_data_table_to_have_rows 2

      page.find('.report-widget').hover
      check_local_filter('Platform') && submit_edit
      expect_report_widget_data_table_to_have_rows 4

      page.find('.report-widget').hover
      check_local_filter('Country') && select2('singapore', 'country', '.ci-report-widget-modal') && submit_edit
      expect_report_widget_data_table_to_have_rows 2

      page.find('.report-widget').hover
      check_local_filter('Platform') && select2('android', 'platform', '.ci-report-widget-modal') && submit_edit
      expect_report_widget_data_table_to_have_rows 0
    end
  end

  context 'when having seperator filter it should not to display' do
    before { setup_dashboard_filters }

    it 'should only display two dropdown filter' do
      visit_dashboard
      sleep 3 # load time
      select_first_report
      wait_expect(2) { page.all('.ci-filters .h-checkbox').length }
    end
  end

  describe 'widget filter tooltips' do
    let(:report2) do
      query = <<~SQL
        select
          nation
        from
          (
            values
              ('sg'),
              ('vn')
          ) as V(nation)
        where [[nation = {{nation}}]]
      SQL
      FactoryBot.create :query_report, query: query, title: 'Dashboard Report with Nation filter',
                                        data_source_id: ds.id, tenant_id: admin.tenant_id
    end
    let (:source) { dashboard }
    let (:source_path) { dashboard_path dashboard }

    let (:nation_filter) do
      FactoryBot.create :shared_filter, name: 'Nation', tenant_id: admin.tenant_id, data_source_id: ds.id, is_adhoc: false,
                                         settings: {
                                           name: 'Nation', type: 'dropdown', label: 'Nation',
                                           data_source_id: ds.id, date_max_ds_id: ds.id, date_min_ds_id: ds.id,
                                           dropdown_source: 'manual', dropdown_manual_entries: "sg,Singapore\nvn,Viet Nam",
                                         }
    end

    def hover_first_widget
      page.all(:css, '.report-widget').first.hover
      wait_for_element_load '.report-widget-filters'
      page.find('.report-widget-filters').hover
    end

    def hover_last_widget
      page.all(:css, '.report-widget').last.hover
      wait_for_element_load '.report-widget-filters'
      page.find('.report-widget-filters').hover
    end

    before do
      setup_dashboard_filters
      @hidden_fo = FactoryBot.create :filter_ownership, shared_filter_id: nation_filter.id, var_name: 'nation', filterable_id: dashboard.id, filterable_type: 'Dashboard', is_hidden: false
      report_widget2 = FactoryBot.create :dashboard_widget, dashboard: dashboard, tenant: dashboard.tenant, source_type: 'QueryReport', source_id: report2.id
      @nation_fo = FactoryBot.create :filter_ownership, shared_filter_id: nation_filter.id, var_name: 'nation', filterable_id: report2.id, filterable_type: 'QueryReport'
    end

    context 'signed in user' do
      it 'should only show filters in use' do
        visit_dashboard
        wait_for { page.all('.v-loading-container', wait: false).count == 0 } # widget loaded

        wait_expect(false) do
          hover_first_widget
          text = page.all('.report-widget-popover', wait: 1)[0]&.text
          text&.include?(nation_filter.name)
        end

        wait_expect(true) do
          hover_first_widget
          text = page.all('.report-widget-popover', wait: 1)[0]&.text
          text&.include?(country_filter.name) && text&.include?(platform_filter.name)
        end

        wait_expect(false) do
          hover_last_widget
          text = page.all('.report-widget-popover', wait: 1)[0]&.text
          # not include both filters name
          text&.include?(country_filter.name) || text&.include?(platform_filter.name)
        end

        wait_expect(true) do
          hover_last_widget
          page.all('.report-widget-popover', wait: 1)[0]&.text&.include?(nation_filter.name)
        end
      end

      it 'filters hidden from business users are hidden in filter tooltip' do
        @hidden_fo.is_hidden = true
        @hidden_fo.save!

        admin.share(user, :read, dashboard)
        page.reset!
        qlogin(user, source_path)
        wait_for_element_load '.report-widget'

        hover_first_widget
        expect(page.find('.report-widget-popover').text).not_to include(nation_filter.name)
        expect(page.find('.report-widget-popover').text).to include(country_filter.name, platform_filter.name)

        page.all(:css, '.report-widget').last.hover
        expect(page.has_css?('.report-widget-filters')).to be_falsy # has no filters
      end
    end

    context 'embedded analytics' do
      let (:embed_link) do
        el = FactoryBot.create :embed_link, source: dashboard, owner_id: admin.id
        el.add_many(:filter_ownership, :embed_link_identifier_variable, @nation_fo)
        el.set_public_user
        el.share_source
        token = jwt_encode(el.secret_key, { @nation_fo.var_name => 'vn' }, Time.now.to_i + 24 * 60 * 60)
        "/embed/#{el.hash_code}?_token=#{token}"
      end

      it 'should not show client_identifier_variable' do
        visit embed_link

        hover_first_widget
        expect(page.find('.report-widget-popover').text).not_to include(nation_filter.name)
        expect(page.find('.report-widget-popover').text).to include(country_filter.name, platform_filter.name)

        page.all(:css, '.report-widget').last.hover
        expect(page.has_css?('.report-widget-filters')).to be_falsy # has no filters
      end
    end
  end
end

