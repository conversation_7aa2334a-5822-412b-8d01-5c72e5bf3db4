# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Retention Heatmap rendering in report widget', js: true, stable: true do
  let(:query) do
    <<~SQL
      WITH R AS (
        VALUES
          ('2012-06-01', 374, 'Month 0', 374),
          ('2012-06-01', 374, 'Month 1', 300),
          ('2012-06-01', 374, 'Month 2', 250),
          ('2012-06-01', 374, 'Month 3', 100),
          ('2012-07-01', 422, 'Month 0', 100),
          ('2012-07-01', 422, 'Month 1', 200),
          ('2012-07-01', 422, 'Month 2', 230)
      )
      SELECT
        column1 AS "Month",
        column2 AS "Cohort Size",
        column3 AS "Period",
        column4 AS "Value"
      FROM R;
    SQL
  end
  let(:tenant) { get_test_tenant }
  let(:ds) { get_test_ds }
  let(:report) do
    settings = {
      column_types: %w[auto auto auto number],
      column_options: [{ name: 'Month', date_format: 'mmm dd yyyy', number_format: 'auto' },
                       { name: 'Cohort Size', date_format: 'mmm dd yyyy', number_format: 'auto' },
                       { name: 'Period', date_format: 'mmm dd yyyy', number_format: 'auto' },
                       { name: 'Value', date_format: 'mmm dd yyyy', number_format: 'auto' },], }
      viz = { has_viz: true,
      viz_type: 'retention',
      retention_settings: { legacy_percent_mode: false, percentage_display: true },
    }

    FactoryBot.create :query_report, title: 'Retention Heatmap',
                                      data_source_id: ds.id, tenant_id: tenant.id,
                                      query: query, settings: settings, viz: viz
  end

  context 'in report' do
    it 'should render empty cell with 0%, follow the settings' do
      safe_login(:admin, "/queries/#{report.to_param}/edit")

      wait_for_element_load '.ci-report-preview'
      safe_click('.ci-report-preview')
      wait_for_report_load

      # enable convert empty cell to 0%
      safe_click('.ci-viz-tab')
      page.find('.ci-retention-empty-cell').set(true)

      safe_click('button#upper-submit-button')
      wait_for_viz_load

      wait_for_element_load '[data-ci="ci-ag-grid-cohort-retention"]'
      retention_element = page.find('[data-ci="ci-ag-grid-cohort-retention"]')
      cell = find_cell_element(table_element: retention_element, row_index: 1, col_index: 5, row_id: nil)

      expect(cell.text).to eq '54.5%'
    end
  end

  context 'in dashboard' do
    let(:dashboard) { FactoryBot.create :dashboard, title: 'Product Overview', tenant_id: tenant.id }
    let(:dashboard_url) { dashboard_path(dashboard) }
    let!(:dashboard_widget) do
      FactoryBot.create :dashboard_widget, dashboard: dashboard, tenant: dashboard.tenant, source_type: 'QueryReport',
                                            source_id: report.id, grid: { col: 0, row: 0, sizeX: 16, sizeY: 10 },
                                            data: { view_mode: 'viz' }
    end
    it 'should render cell with same color and display text correctly' do
      safe_login(:admin, dashboard_url)
      wait_for_element_load '.grid-item'
      wait_for_element_load '[data-ci="ci-ag-grid-cohort-retention"]'
      expected_style = 'background-color: rgb(37, 93, 212); color: rgb(255, 255, 255);'
      retention_element = page.find('[data-ci="ci-ag-grid-cohort-retention"]')
      element = find_cell_element(table_element: retention_element, row_index: 0, col_index: 3, row_id: nil)
      rendered_text = '100%'
      expect(element[:style].include?(expected_style)).to be_truthy
      expect(element.text).to eq rendered_text

      safe_click('.ci-show-raw-values')
      wait_for_all_ajax_requests
      wait_for_element_load '[data-ci="ci-ag-grid-cohort-retention"]'
      element = find_cell_element(table_element: retention_element, row_index: 0, col_index: 3, row_id: nil)
      expect(element.text).to eq '374'
    end
  end
end
