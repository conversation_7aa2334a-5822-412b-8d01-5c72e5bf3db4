# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'downloading dashboards', js: true, require_puppeteer: true do
  include_context 'simple_image_dashboard'
  context 'download pdf file' do
    before do
      Timecop.return
      FeatureToggle.toggle_tenant(ImageExporters::PuppeteerRunner::FT_PDF_EXPORT, dashboard.tenant_id, true)
    end
    it 'returns pdf file' do
      safe_login(get_test_admin, dashboard_path(dashboard))
      wait_for_element_load '.ci-table-report-data'
      wait_for_element_load '.ci-toggle-download'
      page.find('.ci-toggle-download').click
      page.find('.ci-download-pdf').click
      wait_for_element_load('.ci-export-success', 30) # wait for exporting

      metadata = test_download_link
      expect(metadata.base_uri.to_s).to match(/\.pdf$/)
      # NOTE: cannot compare data due to time difference, while using Timecop causes S3 to refuse the uploading. Need to use Timecop + VCR
      # file_content = metadata.read.force_encoding('utf-8')
      # test_pdf_content!(file_content)
      # expected_content = fixture_read_file('image_exporters/db.pdf')
      # expect(strip_date_meta_from_pdf(file_content)).to eq strip_date_meta_from_pdf(expected_content)
    end
  end
end
