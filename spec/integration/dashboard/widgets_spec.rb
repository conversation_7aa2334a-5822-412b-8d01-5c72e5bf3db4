# typed: false

require 'rails_helper'

describe 'download widget data', js: true, require_puppeteer: true do
  # Tenant
  let(:avengers) { get_test_tenant }

  # Users
  let(:capt_america) do
    capt_america = User.new name: 'capt_america', tenant_id: avengers.id, email: '<EMAIL>',
                            password: 'secret'
    capt_america.role = 'admin'
    capt_america.save
    capt_america
  end
  let(:black_widow) do
    black_widow = User.new name: 'black_widow', email: '<EMAIL>', password: 'secret',
                           tenant_id: avengers.id
    black_widow.role = 'analyst'
    black_widow.save
    black_widow
  end
  let(:hulk) do
    hulk = User.new name: 'hulk', email: '<EMAIL>', password: 'secret', tenant_id: avengers.id
    hulk.role = 'user'
    hulk.save
    hulk
  end

  let(:report) do
    query = <<-SQL.strip_heredoc
      VALUES ('2013-01-01', 100, 200),
             ('2013-01-02', 200, 400),
             ('2013-01-03', 300, 600)
    SQL

    create :query_report, title: 'Test', query: query
  end
  let(:report2) do
    query = <<-SQL.strip_heredoc
      VALUES ('2013-02-01', 100, 200),
             ('2013-02-02', 200, 400),
             ('2013-02-03', 300, 600)
    SQL

    create :query_report, title: 'Test 2', query: query
  end
  let(:report3) do
    query = <<~SQL
      VALUES ({{input}}, {{input}})
    SQL

    create :query_report, title: 'Test 3', query: query, is_adhoc: true
  end
  let(:report4) do
    query = <<~SQL
      select
        label,
        weight,
        random_col
      from
        (
          values
            ('Bali', 1, 1),
            ('Singapore', 2, 2),
            ('Kuala Lumpur', 3, 3),
            ('Penang', 4, 4),
            ('Paris', 5, 5),
            ('New York', 6, 6)
        ) as V(label, weight, random_col)
      where
        [[random_col > {{ input|noquote }}]]
    SQL
    report = create :pie_chart_report, query: query
    filter = create :shared_filter, name: 'Input'
    create :filter_ownership, shared_filter: filter, filterable: report, var_name: 'input', label: 'input'

    report
  end

  let(:report5) do
    report = create :pivot_table_report
    report
  end
  let(:dashboard) do
    dashboard = create :dashboard, tenant_id: avengers.id, category_id: 0, id: 10
    filter = create :shared_filter, name: 'Input'
    create :filter_ownership, shared_filter: filter, filterable: dashboard, var_name: 'input', label: 'input'
    dashboard
  end
  let!(:dashboard_widget) do
    create :dashboard_widget, dashboard: dashboard, source: report
  end
  let!(:dashboard_widget2) do
    create :dashboard_widget, dashboard: dashboard, source: report2
  end
  let!(:dashboard_widget3) do
    create :dashboard_widget, dashboard: dashboard, source: report3
  end
  let!(:dashboard_widget4) do
    create :dashboard_widget, dashboard: dashboard, source: report4, data: { view_mode: 'viz' }
  end

  let!(:dashboard_widget5) do
    create :dashboard_widget, dashboard: dashboard, source: report5
  end

  before do
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('new_navigation_node', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
  end

  def ensure_dashboard_loaded
    wait_for_element_load('.dashboard-show')
    sleep 3 # ensure dashboard has changed
  end

  def ensure_expanded_widget_loaded
    wait_for_element_load '.expanded-widget'
    sleep 3 # ensure expanded widget has changed
  end

  def click_widget_title
    safe_click('.widget-title a')
  end

  # page.find('.widget-title > a') is not clickable in Chrome
  # Therefore, we open expanded widget via JS
  def expanded_widget(user)
    dashboard_path = dashboard_path(dashboard)
    qlogin(user, "#{dashboard_path}?input=") # prevent from redirection issue
    wait_for_report_data
    click_widget_title
    wait_for_report_data
  end

  def expanded_widget_via_direct_link(user, widget)
    safe_login(user, "/dashboards/#{dashboard.id}?_e=#{widget.id}&input=")
    ensure_expanded_widget_loaded
    wait_for_element_load '.h-tabs .collapsible'
    expand_tabular_data_tab if page.has_css?('.ci-tab-toggle', text: 'Table Data')
    wait_for_report_data
  end

  def test_button_available(btn_class, should_available)
    if should_available
      wait_for_element_load(btn_class)
    else
      expect(page).not_to have_selector(btn_class)
    end
  end

  def test_widget_data(values)
    wait_for_all_ajax_requests
    wait_for_element_load('.ci-table-report-data')
    wait_for_element_load('[data-ci="ci-ag-grid-data-table"]')
    wait_expect(values) do
      page.all('.ci-table-report-data .ag-row .ag-cell:not(.ag-column-first)', wait: 1).map(&:text)
    rescue StandardError => e
      sleep 0.5
      e.inspect
    end
  end

  def expand_report_dropdown
    retry_count = 0
    page.find("#navigation-node-Dashboard-#{dashboard.id} .navigation-node-caret").click
    expect(page.all("#navigation-node-DashboardWidget-#{dashboard_widget.id}").count).not_to equal(0)
  rescue RSpec::Expectations::ExpectationNotMetError
    if retry_count < 2
      sleep 1
      page.find("#navigation-node-Dashboard-#{dashboard.id} .navigation-node-caret").click
      retry_count += 1
    else
      raise
    end
  end

  control_button_classes = %w[.ci-toggle-download .ci-share-dropdown .ci-data-schedules-dropdown .ci-edit-report-link]

  describe 'Signed in users' do
    it 'infinity scroll - not show total row info' do
      FeatureToggle.toggle_global('table:infinite_scroll', true)
      expect(page.has_css?('.ci-total-row')).to be(false)
      FeatureToggle.toggle_global('table:infinite_scroll', false)
    end

    it 'Admin can see all control buttons' do
      # FeatureToggle.toggle_global('exportings:pdf', true)
      expanded_widget(capt_america)
      control_button_classes.each do |btn_class|
        test_button_available(btn_class, true)
      end
    end

    it 'Analyst who was shared data source can see all control buttons' do
      data_source = report.data_source
      expect do
        capt_america.share(black_widow, :read, data_source)
      end.not_to raise_error

      expanded_widget(black_widow)
      control_button_classes.each do |btn_class|
        test_button_available(btn_class, true)
      end
    end

    it 'Business can only see button export' do
      capt_america.share(hulk, :read, dashboard)
      expanded_widget(hulk)
      control_button_classes.reject { |cl| cl == '.ci-toggle-download' }.each do |btn_class|
        test_button_available(btn_class, false)
      end

      test_button_available('.ci-toggle-download', true)
    end

    context 'Expanded widget' do
      it 'User can access via direct link' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)
        control_button_classes.each do |btn_class|
          test_button_available(btn_class, true)
        end

        test_widget_data ['Jan 01 2013', '100', '200', 'Jan 02 2013', '200', '400', 'Jan 03 2013', '300', '600']
      end

      it 'infinity scroll - show pagination instead' do
        FeatureToggle.toggle_global('table:infinite_scroll', true)
        expanded_widget_via_direct_link(capt_america, dashboard_widget5)
        expect(page.has_css?('.ci-pagination-info')).to be(true)
        FeatureToggle.toggle_global('table:infinite_scroll', false)
      end

      it 'User can go back after access via direct link' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)
        safe_click('.dashboard-expanded-widget-meta button')
        wait_expect(5) { page.all('.widget').count }
        # increase the timeout to 10, because this fetches data for multiple widgets which
        # is a bit slow. On slow computer, it may take more than 5 seconds.
        wait_expect(true, 10) { page.all('.v-loading-container', wait: false).count == 0 }
        expect(page.all('.ci-table-report-data').count).to eq(3)
        tabular_data = page.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)
        expected_data = ['Jan 01 2013', '100', '200', 'Jan 02 2013', '200', '400', 'Jan 03 2013', '300', '600',
                         'Feb 01 2013', '100', '200', 'Feb 02 2013', '200', '400', 'Feb 03 2013', '300', '600',]
        expect(tabular_data).to include(*expected_data) # include first and second widgets
      end

      it 'User go back from expanded widget and can expand another correctly' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)
        page.first('.dashboard-expanded-widget-meta button').click

        sleep 5
        wait_for { page.all('.ci-table-report-data').count == 3 }
        click_js_by_index('.widget-title a', 1) # page.all('.widget-title > a')[1].click

        sleep 5
        test_widget_data ['Feb 01 2013', '100', '200', 'Feb 02 2013', '200', '400', 'Feb 03 2013', '300', '600']
      end

      it 'render all fields in table data for viz sql report' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget4)
        wait_for { page.all('.ci-table-report-data').count == 1 }
        test_widget_data ['Bali', '1', '1', 'Singapore', '2', '2',
                          'Kuala Lumpur', '3', '3', 'Penang', '4', '4',
                          'Paris', '5', '5', 'New York', '6', '6',]
      end

      it 'can sort data table in tabular data section' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget4)
        test_widget_data ['Bali', '1', '1', 'Singapore', '2', '2',
                          'Kuala Lumpur', '3', '3', 'Penang', '4', '4',
                          'Paris', '5', '5', 'New York', '6', '6',]
        # click on header to sort
        wait_for_element_load('.ci-table-report-data .ag-header-row')
        wait_for_all_holistics_loadings
        table = page.find('[data-ci="ci-ag-grid-data-table"]')
        open_context_menu_by_name(element: table, header_name: 'label')
        safe_click('.ci-sort-asc-btn')
        wait_for_all_ajax_requests
        wait_for_all_holistics_loadings
        test_widget_data [
          'Bali', '1', '1',
          'Kuala Lumpur', '3', '3',
          'New York', '6', '6',
          'Paris', '5', '5',
          'Penang', '4', '4',
          'Singapore', '2', '2',
        ]
      end

      it 'updates tabular data when dashboard filters change' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget4)
        test_widget_data ['Bali', '1', '1', 'Singapore', '2', '2',
                          'Kuala Lumpur', '3', '3', 'Penang', '4', '4',
                          'Paris', '5', '5', 'New York', '6', '6',]

        wait_for_element_load '.filter-item'
        safe_click('.ci-filter-header')
        wait_and_set('.ci-input-editable .ci-text', '3')
        safe_click('.ci-submit-filters-btn')

        wait_for_all_ajax_requests
        wait_for_all_holistics_loadings

        test_widget_data [
          'Penang', '4', '4',
          'Paris', '5', '5',
          'New York', '6', '6',
        ]
      end

      it 'render only selected field in table data for table sql report' do
        report4.viz_setting[:viz_type] = 'data_table'
        report4.viz_setting[:fields] = {
          table_fields: [
            {
              path_hash: { joins_path: [], field_name: 'label' },
            },
            {
              path_hash: { joins_path: [], field_name: 'weight' },
            },
          ],
        }
        report4.viz_setting.save!
        expanded_widget_via_direct_link(capt_america, dashboard_widget4)
        wait_for { page.all('.ci-table-report-data').count == 1 }
        test_widget_data ['Bali', '1', 'Singapore', '2',
                          'Kuala Lumpur', '3', 'Penang', '4',
                          'Paris', '5', 'New York', '6',]
      end

      it 'Correctly render filters' do
        safe_login(capt_america, "/dashboards/#{dashboard.id}?_e=#{dashboard_widget3.id}&input=")

        wait_for_element_load '.filter-item'
        safe_click('.ci-filter-header')
        wait_and_set('.ci-input-editable .ci-text', 'haha')
        safe_click('.ci-submit-filters-btn')

        test_widget_data ['haha', 'haha']
      end

      it 'Correctly render filters from query string' do
        qlogin(capt_america, "/dashboards/#{dashboard.id}?_e=#{dashboard_widget3.id}&input=haha")
        test_widget_data ['haha', 'haha']
      end

      it 'Filters still work when go back' do
        qlogin(capt_america, "/dashboards/#{dashboard.id}?_e=#{dashboard_widget3.id}")
        sleep 2
        wait_for_element_load '.filter-item'
        safe_click('.ci-filter-header')
        wait_and_set('.ci-input-editable .ci-text', 'haha')
        safe_click('.ci-submit-filters-btn')
        safe_click('.dashboard-expanded-widget-meta button')

        wait_for { page.all('.ci-table-report-data').count == 4 }

        first_widget = page.all('.ci-table-report-data')[0]
        filtered_widget = page.all('.ci-table-report-data')[2]

        expect(first_widget.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)).to eql(
          ['Jan 01 2013', '100', '200', 'Jan 02 2013', '200', '400', 'Jan 03 2013', '300', '600'],
        )

        expect(filtered_widget.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)).to eql(['haha', 'haha'])
      end

      it 'Can apply filters correctly after expand from dashboard' do
        qlogin(capt_america, "/dashboards/#{dashboard.id}?input=hihi")
        wait_for_all_ajax_requests
        wait_for_element_load '.ci-table-report-data'

        safe_click('.widget-title a', index: 2)
        wait_for_all_ajax_requests
        wait_for_element_load '.filter-item'
        safe_click('.ci-filter-header')
        fill_text('.ci-input-editable .ci-text', 'haha')
        safe_click('.ci-submit-filters-btn')
        test_widget_data ['haha', 'haha']
      end

      it 'Correctly render data when switch between expanded widget using node explorer' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)
        wait_for_element_load "#navigation-node-DashboardWidget-#{dashboard_widget2.id}"
        safe_click("#navigation-node-DashboardWidget-#{dashboard_widget2.id}")
        wait_for_all_holistics_loadings
        test_widget_data ['Feb 01 2013', '100', '200', 'Feb 02 2013', '200', '400', 'Feb 03 2013', '300', '600']
      end

      it 'Correctly render data when switch to parent dashboard from expanded widget using node explorer' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)

        navigation_selector = "#navigation-node-Dashboard-#{dashboard.id}"
        wait_for_element_load navigation_selector
        safe_click("#{navigation_selector} .title") # could not find element in this case T_T
        wait_for { page.all('.ci-table-report-data').count == 3 }

        widgets = page.all('.widget')
        expect(widgets.count).to eq 5

        first_widget = page.all('.ci-table-report-data')[0]
        second_widget = page.all('.ci-table-report-data')[1]

        expect(first_widget.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)).to eql(
          ['Jan 01 2013', '100', '200', 'Jan 02 2013', '200', '400', 'Jan 03 2013', '300', '600'],
        )

        expect(second_widget.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)).to eql(
          ['Feb 01 2013', '100', '200', 'Feb 02 2013', '200', '400', 'Feb 03 2013', '300', '600'],
        )
      end

      it 'Correctly render data when switch to expanded widget from parent dashboard using node explorer' do
        qlogin(capt_america, dashboard_path(dashboard))

        wait_for_element_load("#navigation-node-Dashboard-#{dashboard.id}")
        expand_report_dropdown
        safe_click("#navigation-node-DashboardWidget-#{dashboard_widget.id}")
        wait_for_submit_generate
        test_widget_data ['Jan 01 2013', '100', '200', 'Jan 02 2013', '200', '400', 'Jan 03 2013', '300', '600']
      end

      it 'Only show dashboard widget that user has permission in node explorer' do
        capt_america.share(black_widow, :read, report)
        safe_login(black_widow, dashboard_path(dashboard))
        wait_for_element_load("#navigation-node-Dashboard-#{dashboard.id} .navigation-node-caret")
        wait_for do
          safe_click("#navigation-node-Dashboard-#{dashboard.id} .navigation-node-caret")
          page.all("#navigation-node-DashboardWidget-#{dashboard_widget.id}").count > 0
        end
        expect(page.all("#navigation-node-DashboardWidget-#{dashboard_widget.id}").count).to eq 1
        expect(page.all("#navigation-node-DashboardWidget-#{dashboard_widget2.id}").count).to eq 0
        expect(page.all("#navigation-node-DashboardWidget-#{dashboard_widget3.id}").count).to eq 0
      end

      it 'Correctly render data with filter when switch between expanded widget using node explorer' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)

        navigation_selector = "#navigation-node-DashboardWidget-#{dashboard_widget3.id}"
        wait_for_element_load(navigation_selector)
        safe_click("#{navigation_selector} .title")
        sleep 2

        wait_for_element_load '.filter-item'
        safe_click('.ci-filter-header')

        wait_for_element_load('.ci-input-editable .ci-text')
        page.first('.ci-input-editable .ci-text').set('haha')
        safe_click('.ci-submit-filters-btn')

        wait_for_report_data
        sleep 1 # wait for attaching elements to DOM
        test_widget_data ['haha', 'haha']
      end
    end
  end

  # Disabled since public user can not expand the report
  xdescribe 'Public users' do
    before do
      FactoryBot.create :tenant_subscription, tenant: dashboard.tenant, status: 'active'
    end

    let(:shareable_link) do
      sl = FactoryBot.create :shareable_link, resource: dashboard, owner_id: capt_america.id
      sl.set_public_user
      sl.share_resource
      "#{dashboard_path dashboard}?_pl=#{sl.hash_code}"
    end
    let(:embed_link) do
      filter = FactoryBot.create :shared_filter, name: 'Input'
      FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: filter, var_name: 'test',
                                           is_hidden: false
      customer_fo = FactoryBot.create :filter_ownership, var_name: 'customer_key', filterable: dashboard,
                                                         shared_filter: filter
      el = FactoryBot.create :embed_link, source: dashboard, customer_filter_ownership: customer_fo,
                                          owner_id: capt_america.id
      el.set_public_user
      el.share_source
      token = jwt_encode(el.secret_key, 123, Time.now.to_i + (24 * 60 * 60))
      "/embed/#{el.hash_code}?_token=#{token}"
    end

    it 'shows only button download in embed link' do
      visit embed_link
      safe_click('body')

      click_widget_title
      control_button_classes.reject { |cl| cl == '.ci-toggle-download' }.each do |btn_class|
        test_button_available(btn_class, false)
      end

      test_button_available('.ci-toggle-download', true)
    end

    it 'shows only button download in shareable link' do
      visit shareable_link
      safe_click('body')

      click_widget_title
      control_button_classes.reject { |cl| cl == '.ci-toggle-download' }.each do |btn_class|
        test_button_available(btn_class, false)
      end

      test_button_available('.ci-toggle-download', true)
    end
  end
end
