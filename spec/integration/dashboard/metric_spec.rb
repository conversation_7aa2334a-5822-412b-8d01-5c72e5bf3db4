# typed: false

require 'rails_helper'
describe 'Metric widget', :js, stable: true do
  let(:dashboard) do
    FactoryBot.create(:dashboard)
  end
  let(:metric) do
    query = 'SELECT SUM(cnt) FROM public.test_pageviews WHERE {{time_where}}'
    FactoryBot.create(:query_metric,
                      title: 'Pageviews', query: query, time_field: 'date_d',
                      data_source_id: ds.id, tenant_id: get_test_tenant.id,)
  end
  let(:ds) { get_test_ds }

  before do
    pg_create_pageviews(ds)
    @dw = FactoryBot.create(:dashboard_widget, source_type: 'QueryMetric',
                                               source_id: metric.id, dashboard_id: dashboard.id,
                                               tenant: dashboard.tenant, data: { time_periods: ['today'] },)
  end

  after { pg_drop_pageviews(ds) }

  def open_edit_widget_modal
    page.find('.widget-wrapper').hover
    wait_for_element_load '.ci-widget-controls'
    safe_click('.ci-widget-controls')
    safe_click('.ci-widget-settings')
    sleep 1
  end

  it 'disable friendly format' do
    metric.settings = { number_format: '"£"#,##0.00' }
    metric.query = 'SELECT * FROM (values(123456)) R WHERE {{time_where}}'
    metric.save
    @dw.data = { time_periods: ['all_time'] }
    @dw.save
    qlogin(get_test_admin, dashboard_path(dashboard))
    wait_for_element_load '.ci-friendly-number'
    wait_expect('£123.46K') { page.first('.ci-friendly-number').text }

    @dw.data = { time_periods: ['all_time'], friendly_format: false }
    @dw.save
    page.evaluate_script 'window.location.reload()'
    wait_for_element_load '.ci-friendly-number'
    wait_expect('£123,456.00') { page.first('.ci-friendly-number').text }
  end

  it 'hide comparision' do
    qlogin(get_test_admin, dashboard_path(dashboard))
    wait_for_element_load('.ci-friendly-diff')

    open_edit_widget_modal
    safe_click('.ci-show-comparison')
    safe_click('.ci-save-metrics')
    sleep 1
    expect(page.has_css?('.ci-friendly-diff')).to be_falsy
  end

  it 'set opponent period' do
    Timecop.travel(Date.parse('2017-10-07')) do
      qlogin(get_test_admin, dashboard_path(dashboard))
      wait_for_element_load('.ci-friendly-diff')
      open_edit_widget_modal
      h_radio_check(value: 'yesterday')
      select_h_select_option('.ci-select-period', value: 'same_yesterday_week_before')
      safe_click('.ci-save-metrics')
      sleep 3
      # NOTE: when we are trying to hover on .ci-friendly-diff, the mouse unexpectedly move over some other tooltips
      # and make the desired tooltip close, thus we need to hover on the element twice to make the tooltip appear
      page.first('.ci-friendly-diff').hover
      page.first('.ci-widget-controls').hover
      page.first('.ci-friendly-diff').hover
      wait_for_element_load '.hui-tooltip-floating'
      expect(page.first('.hui-tooltip-floating').text.gsub(/\s+/,
                                                           ' ',)).to eq 'Yesterday Oct 06, 2017 0 Same day the week before Sep 29, 2017 0 Delta 0'
    end
  end

  it 'custom value format' do
    [%w["£"#,##0.00 £0.00], %w[0.00% 0.00%]].each do |value|
      metric.settings = { number_format: value[0] }
      metric.save
      qlogin(get_test_admin, dashboard_path(dashboard))
      wait_for_element_load '.ci-friendly-number'
      expect(page.first('.ci-friendly-number').text).to eq value[1]
    end
  end
end
