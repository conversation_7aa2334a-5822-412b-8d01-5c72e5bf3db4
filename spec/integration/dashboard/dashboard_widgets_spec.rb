# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'dashboards integration test', js: true do
  include_context 'test_tenant'

  before do
    FeatureToggle.toggle_global('data_models:manager', true)
  end

  let(:dashboard) { FactoryBot.create(:dashboard, title: 'Awesome title', tenant_id: tenant.id, owner: admin) }
  let(:viz_setting) do
    FactoryBot.create(
      :viz_setting,
      viz_type: 'data_table',
      fields: {
        "table_fields": [],
      },
      settings: {
        "misc": {
          "pagination_size": 25,
          "show_row_number": true,
          "row_limit": nil,
        },
        "conditional_formatting": [],
        "aggregation": {
          "show_total": false,
          "show_average": false,
        },
        "quick_pivot": false,
        "sort": {},
      },
      format: {},
      filters: [],
      adhoc_fields: [],
    )
  end
  let(:report) {
    FactoryBot.create :report_with_table_fields, query: 'SELECT * FROM "public"."users" where [[ id in ({{ user_ids }}) ]]', viz_setting: viz_setting, is_adhoc: true, owner: admin
  }

  let!(:widget) {
    FactoryBot.create :dashboard_widget, source: report, dashboard: dashboard, tenant: tenant, data: { is_adhoc: true }
  }

  let!(:shared_filter) {
    FactoryBot.create(
      :shared_filter,
      name: 'user_ids',
      settings: {
        data_source_id: ds.id,
        type: 'dropdown',
        dropdown_multiselect: false,
        dropdown_source: "sql",
        dropdown_sql: "select id, name from users",
      },
      tenant: tenant,
    )
  }

  let!(:fo2) {
    FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: shared_filter, var_name: 'user_ids', tenant_id: tenant.id, is_hidden: false
  }

  let!(:fo) {
    FactoryBot.create :filter_ownership, filterable: report, shared_filter: shared_filter, var_name: 'user_ids', tenant_id: tenant.id, is_hidden: false
  }

  it 'expanded widget should apply filter after refresh' do
    safe_login admin, dashboard_path(dashboard)

    safe_click '.filters-preview'
    safe_click ".ci-report-filter-#{fo2.var_name}"
    page.find('.select2-search__field').set('admin')
    safe_click('.select2-results .select2-results__option:first-child')

    safe_click '.ci-submit-filters-btn'

    wait_for_element_load '.widget-wrapper:first-child .result-viz'

    visit "#{current_url}&_e=#{widget.id}"

    wait_for_element_load '.result-viz'

    wait_expect(1) do
      page.find_all('.ci-table-report-data .ag-row').length
    end

    safe_click '.dashboard-expanded-widget-meta .refresh'

    sleep 2

    expect(page.find_all('.ci-table-report-data .ag-row').length).to eq 1
  end
end
