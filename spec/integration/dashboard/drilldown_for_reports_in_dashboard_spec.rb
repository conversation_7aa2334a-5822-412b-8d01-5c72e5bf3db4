# typed: false
require 'rails_helper'

# https://app.asana.com/0/161816130717323/212743426890789
describe 'Drilldowns of Reports in Dashboard', js: true, stable: true do
  let (:tenant) { get_test_tenant }
  let (:ds) { get_test_ds }
  let (:orig_report_query) {
    <<~SQL
      with items as
      (
        select
          column1 as id,
          column2 as country,
          column3 as item,
          column4 as sale
        from (
          values
            (1, 'Vietnam', 'Shoe', 5),
            (2, 'Thailand', 'Hat', 3),
            (3, 'Singapore', 'Shirt', 7),
            (4, 'Vietnam', 'Sock', 1)
        ) f
      )
      select
        country,
        sum(sale) as total_sale
      from items
      group by country
    SQL
  }
  let (:dest_report_query) {
    <<~SQL
      with items as
      (
        select
          column1 as id,
          column2 as country,
          column3 as item,
          column4 as sale
        from (
          values
            (1, 'Vietnam', 'Shoe', 5),
            (2, 'Thailand', 'Hat', 3),
            (3, 'Singapore', 'Shirt', 7),
            (4, 'Vietnam', 'Sock', 1)
        ) f
      )
      select
        country,
        item,
        sale
      from items
      where [[country = {{country}}]]
    SQL
  }

  def create_reports
    @orig_report_with_chart = FactoryBot.create :query_report, title: 'Orig With Chart',
                                                 query: orig_report_query, data_source_id: ds.id, tenant_id: tenant.id,
                                                 viz: {"has_viz"=>true,
                                                       "viz_type"=>"chart",
                                                       "chart_type"=>"pie",
                                                       "chart_options"=>{"x_axis_columns"=>[0], "y_axis_columns"=>[1]}}
    @orig_report_without_chart = FactoryBot.create :query_report, title: 'Orig Without Chart',
                                                    query: orig_report_query, data_source_id: ds.id, tenant_id: tenant.id,
                                                    viz: {'has_viz'=>false}

    @dest_report = FactoryBot.create :query_report, title: 'Dest Report', query: dest_report_query,
                                      data_source_id: ds.id, tenant_id: tenant.id, viz: {'has_viz'=>false}
  end

  def create_filters_and_drilldowns to_dashboard: false
    # filter for destination report
    sf = FactoryBot.create :shared_filter, name: "country", tenant_id: tenant.id, data_source_id: ds.id,
                            settings:
                              {
                                :name=>"country", :type=>"dropdown", :order=>0, :dropdown_source=>"manual",
                                :dropdown_manual_entries=>"Vietnam, Vietnam\nThailand, Thailand\nSingapore, Singapore"
                              }
    fo = FactoryBot.create :filter_ownership, shared_filter_id: sf.id, var_name: 'country', filterable_id: @dest_report.id, filterable_type: 'QueryReport'

    if to_dashboard
      @dest_dashboard = FactoryBot.create :dashboard, title: 'Destination Dashboard for Drilldown', tenant_id: tenant.id
      FactoryBot.create :filter_ownership, shared_filter_id: sf.id, var_name: 'country', filterable_id: @dest_dashboard.id, filterable_type: 'Dashboard'
      FactoryBot.create :dashboard_widget, dashboard: @dest_dashboard, tenant: @dest_dashboard.tenant, source_type: 'QueryReport',
        source_id: @dest_report.id, grid: {"col"=>0, "row"=>0}

      drilldown_dest = @dest_dashboard
      dest_type = 'Dashboard'
    else
      drilldown_dest = @dest_report
      dest_type = 'QueryReport'
    end

    # drilldown to destination report
    FactoryBot.create :drilldown, from_report_id: @orig_report_with_chart.id, tenant_id: tenant.id, dest_id: drilldown_dest.id, dest_type: dest_type,
                       mapping: {"dest_fo_id"=>fo.id, "link_column_index"=>"1", "value_column_index"=>"0"}
    FactoryBot.create :drilldown, from_report_id: @orig_report_without_chart.id, tenant_id: tenant.id, dest_id: drilldown_dest.id, dest_type: dest_type,
                       mapping: {"dest_fo_id"=>fo.id, "link_column_index"=>"1", "value_column_index"=>"0"}
  end

  def create_origin_dashboard
    @origin_dashboard = FactoryBot.create :dashboard, title: 'Drilldown Dashboard', tenant_id: tenant.id
    FactoryBot.create :dashboard_widget, dashboard: @origin_dashboard, tenant: @origin_dashboard.tenant, source_type: 'QueryReport', source_id: @orig_report_with_chart.id, grid: {col: 0, row: 0, sizeX: 6, sizeY: 6}, data: {view_mode: 'viz'}
    FactoryBot.create :dashboard_widget, dashboard: @origin_dashboard, tenant: @origin_dashboard.tenant, source_type: 'QueryReport', source_id: @orig_report_without_chart.id, grid: {col: 12, row: 0, sizeX: 6, sizeY: 6}, data: {view_mode: 'data'}
    @origin_dashboard
  end

  def visit_origin_dashboard
    safe_login :admin, dashboard_path(@origin_dashboard)
    wait_for_element_load '.highcharts-container' # first widget
    wait_for_element_load '.ci-table-report-data' # second widget
  end

  context 'when drilldown to report' do
    before do
      create_reports
      create_filters_and_drilldowns
      create_origin_dashboard
    end

    it 'should work when report has chart' do
      visit_origin_dashboard
      wait_for_element_load('.highcharts-data-labels')
      click_highchart('.highcharts-data-labels .highcharts-label.highcharts-data-label.highcharts-data-label-color-0 text')
      sleep 0.5
      wait_for { page.all('.ci-table-report-data').count > 0 }
      wait_expect("/queries/#{@dest_report.to_param}") { URI.parse(current_url).path }
    end

    it 'should work when report has no chart' do
      visit_origin_dashboard
      safe_click('.ci-table-report-data .ag-row-first .ag-cell:nth-child(2) span')
      wait_for { page.all('.ci-table-report-data').count > 0 }
      wait_expect("/queries/#{@dest_report.to_param}") { URI.parse(current_url).path }
    end
  end

  context 'when drilldown to dashboard' do
    before do
      create_reports
      create_filters_and_drilldowns to_dashboard: true
      create_origin_dashboard
    end

    it 'should work when report has chart' do
      visit_origin_dashboard
      wait_for_all_ajax_requests
      wait_for_all_holistics_loadings
      click_highchart('.highcharts-data-labels .highcharts-label.highcharts-data-label.highcharts-data-label-color-0 text')
      wait_for_all_ajax_requests
      wait_expect("/dashboards/#{@dest_dashboard.to_param}") { URI.parse(current_url).path }
    end

    it 'should work when report has no chart' do
      visit_origin_dashboard
      safe_click('.ci-table-report-data .ag-row-first .ag-cell:nth-child(2) span')
      wait_for { page.all('.ci-table-report-data').count > 0 }
      wait_expect("/dashboards/#{@dest_dashboard.to_param}") { URI.parse(current_url).path }
    end
  end
end
