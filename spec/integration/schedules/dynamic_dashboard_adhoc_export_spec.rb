# typed: ignore

require 'rails_helper'

describe 'Dynamic Dashboard Adhoc Export', js: true, stable: true do
  include_context 'query_model_based_report'

  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(::Dashboard::FT_V3_CONVERSION, true)
    FeatureToggle.toggle_global('integrations:slack', true)
    FeatureToggle.toggle_global('slack_schedules:dashboard', true)
    FeatureToggle.toggle_global('data_schedules:test_execution', true)
    FeatureToggle.toggle_global(SlackDest::DASHBOARD_FEATURE_TOGGLE, true)
    FactoryBot.create :slack_authorization, user_id: admin.id
    update_tenant_slack_settings
    VcrHelper.ignore_hosts('127.0.0.1', 'localhost')
  end

  let(:admin) { get_test_admin }
  let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
  let(:qr) { query_model_based_report }
  let!(:report_widget) { FactoryBot.create :dashboard_widget, source: qr, dashboard: dashboard }
  let(:manual_filter_definition) { FactoryBot.create :dynamic_filter_definition }
  let!(:lonely_filter) do
    FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, order: 1,
                                       dynamic_filter_definition: manual_filter_definition
  end
  let!(:mapping) do
    field_path = DataModeling::Values::FieldPath.new(field_name: 'name')
    FactoryBot.create :dynamic_filter_mapping, viz_conditionable: report_widget, dynamic_filter: lonely_filter,
                                               field_path: field_path
  end

  def wait_for_widget_load
    wait_for(30) { page.all('.v-loading-container',  wait: false).count == 0 } # widget loaded
  end

  before(:each) do
    Capybara.current_window.resize_to 1200, 800
    qlogin(admin, "/dashboards/v3/#{dashboard.id}")
    wait_for_element_load('.ci-filters') # filters loaded
    wait_for_widget_load
  end

  context 'Once as Default Option' do
    it 'Send to Email' do
      recipients = '<EMAIL>'
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-email-dest-adhoc')
      wait_for_element_load('.email-dest-form')
      expect(page.find('.ci-ss-repeatby').text).to eq('Once')
      safe_click('.ci-es-recipients')
      recipients_input_selector = '.ci-es-recipients > div.h-input > div > input'
      fill_text(recipients_input_selector, recipients)
      find(recipients_input_selector).native.send_keys(:return)
      tree_select_dropdown('.widget-select .tree-select', value: "#{report_widget.id}")
      wait_for_element_load('.ci-filter-labels-list')
      safe_click('.ci-submit-btn')
      wait_for_element_load('.label-success', 30)
      wait_expect('Success') { page.first('.ci-test-status .label')&.text }
    end

    it 'Send to Slack' do
      allow(StringUtils).to receive(:now_random_string).and_return('2020_10_19_09_24_qzjuevyz')
      VCR.use_cassette('slack/send_once_dashboard') do
        safe_click('.ci-schedule-dropdown')
        safe_click('.ci-slack-dest-adhoc')
        wait_for_element_load('.slack-dest-form')
        wait_for_element_load('.ci-channels-dropdown')
        safe_click('.ci-channels-dropdown input')
        page.first('.ci-channels-dropdown input').send_keys('test')
        wait_expect(true) do
          page.all('.hui-select-floating .v-vl-visible-items .hui-select-option').map(&:text).include?('test-bot')
        end
        select_h_select_option('.ci-channels-dropdown', value: 'G63STMBCK') # test-bot
        wait_expect(false) do # wait until the search resets and test-bot is no longer listed
          page.all('.hui-select-floating .v-vl-visible-items .hui-select-option', wait: false).map(&:text).include?('test-bot')
        end
        expect(page.first('.ci-channels-dropdown .hui-select-trigger').text).to eq('test-bot') # the selected channel name should still be displayed correctly
        wait_for_element_load('.ci-filter-labels-list')
        expect(page.find('.ci-ss-repeatby').text).to eq('Once')
        safe_click('.ci-submit-btn')
        wait_for_element_load('.label-success', 30)
        wait_expect('Success') { page.first('.ci-test-status .label')&.text }
      end
    end
  end

  context 'Normal Export' do
    it 'Open Email Schedule with Daily Frequency, send test & create email schedule' do
      recipients = '<EMAIL>'
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-schedule-export')
      wait_for_element_load('.email-dest-form')
      expect(page.find('.ci-ss-repeatby').text).to eq('Daily')
      safe_click('.ci-es-recipients')
      recipients_input_selector = '.ci-es-recipients > div.h-input > div > input'
      fill_text(recipients_input_selector, recipients)
      find(recipients_input_selector).native.send_keys(:return)
      tree_select_dropdown('.widget-select .tree-select', value: "#{report_widget.id}")
      wait_for_element_load('.ci-filter-labels-list')
      page.find('.ci-test-run-es', text: 'Send Test').click
      safe_click('.ci-confirm')

      # TODO: mock the image exporting to reduce time?
      wait_for_element_load('.label-success', 40)
      wait_expect('Success') { page.first('.ci-test-status .label')&.text }
      safe_click('.ci-submit-btn')
      wait_expect(1) { page.all('table.h-table > tbody > tr').count }
    end

    it 'Open Email Schedule with Daily Frequency, change to adhoc export' do
      recipients = '<EMAIL>'
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-schedule-export')
      wait_for_element_load('.email-dest-form')
      expect(page.find('.ci-ss-repeatby').text).to eq('Daily')
      expect(page.find('.ci-submit-btn').text).to eq('Save')
      safe_click('.ci-es-recipients')
      recipients_input_selector = '.ci-es-recipients > div.h-input > div > input'
      fill_text(recipients_input_selector, recipients)
      find(recipients_input_selector).native.send_keys(:return)

      # Select the widget
      tree_select_dropdown('.widget-select .tree-select', value: "#{report_widget.id}")
      # Close the widget dropdown, otherwise it would hide the next dropdown, making it not clickable
      safe_click('.widget-select')

      wait_for_element_load('.ci-filter-labels-list')
      wait_for_element_load('.ci-ss-repeatby')
      select_h_select_option('.ci-ss-repeatby', value: 'Once')
      expect(page.find('.ci-ss-repeatby').text).to eq('Once')
      page.find('.ci-submit-btn', text: 'Send Now').click
      wait_for_element_load('.label-success', 30)
      wait_expect('Success') { page.first('.ci-test-status .label')&.text }
    end
  end

  context 'edit mode' do
    let(:dest) { FactoryBot.create :email_dest, recipients: ['<EMAIL>'], options: { body_text: 'This is body text' } }
    let(:data_schedule) { FactoryBot.create :email_schedule, dest: dest, source: dashboard, tenant_id: admin.tenant_id }
    let!(:dynamic_filter_preset) do
      FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: data_schedule, dynamic_filter: lonely_filter,
                                                preset_condition: { operator: 'is', values: ['bong'], modifier: nil }
    end

    it 'contains old data to edit & should not show Once option in Frequency' do
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-manage-schedules')
      safe_click('.ci-actions')
      safe_click('.ci-es-edit')
      wait_for_element_load('.email-dest-form')
      expect(page.find('div.ci-es-recipients > div.h-input > div > span:nth-child(1)').text).to eq('<EMAIL>')
      schedule_container = page.first('.schedule-selector .hui-select-trigger')

      widget_select_btn_selector = 'div.widget-select > div.tree-select div.tree-select-control-wrapper > .tree-select-control > button'
      safe_click(widget_select_btn_selector)
      wait_for_element_load('.tree-select-options')
      safe_click('.ci-select-all-btn')

      # open the panel
      schedule_container.click
      wait_for_all_ajax_requests

      wait_for_element_load('.hui-select-option')
      expect(page.all('.hui-select-option').map(&:text)).to match_array(['Every', 'Hourly', 'Daily', 'Weekly', 'Monthly'])
      expect(page).to have_css('.ci-attach-png svg')
      expect(page).to have_css('.ci-preview svg')
      # expect that the embedded filter label is shown
      expect(page.find('.data-schedule .ci-filter-label').text).to include('is "bong"')

      # check the email body
      scroll_to_js('.toggle-email-dest-form')
      safe_click('.toggle-email-dest-form .h-icon')
      expect(page.find('[data-ci="ci-email-body"]').value).to eq 'This is body text'
    end
  end

  context 'confirmation modal' do
    it 'should not show the confirmation modal when there is no change' do
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-email-dest-adhoc')
      wait_for_element_load('.email-dest-form')
      expect(page.find('.ci-ss-repeatby').text).to eq('Once')
      safe_click('.ci-close-modal')
      wait_expect(0) { page.all('.email-dest-form', wait: false).size }
    end

    it 'should show the confirmation modal when there are changes' do
      recipients = '<EMAIL>'
      safe_click('.ci-schedule-dropdown')
      safe_click('.ci-schedule-export')
      wait_for_element_load('.email-dest-form')
      expect(page.find('.ci-ss-repeatby').text).to eq('Daily')
      expect(page.find('.ci-submit-btn').text).to eq('Save')
      safe_click('.ci-es-recipients')
      recipients_input_selector = '.ci-es-recipients > div.h-input > div > input'
      fill_text(recipients_input_selector, recipients)
      find(recipients_input_selector).native.send_keys(:return)
      wait_for_element_load('.ci-filter-labels-list')
      wait_for_element_load('.ci-ss-repeatby')
      select_h_select_option('.ci-ss-repeatby', value: 'Once')
      expect(page.find('.ci-ss-repeatby').text).to eq('Once')
      safe_click('.ci-close-modal')
      safe_click('.ci-close')
      wait_expect(1) { page.all('.email-dest-form').size }
      safe_click('.ci-close-modal')
      safe_click('.ci-confirm')
      wait_expect(0) { page.all('.email-dest-form', wait: false).size }
    end
  end
end
