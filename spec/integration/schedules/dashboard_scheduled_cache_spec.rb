# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Dashboard scheudled cache', js: true, stable: true do
  let(:admin) { get_test_admin }
  let!(:dashboard_with_auto_preload) { FactoryBot.create :dashboard_with_cache, owner: admin }
  let!(:scheduled_cache) { FactoryBot.create :scheduled_cache, source: dashboard_with_auto_preload, tenant: admin.tenant }
  let!(:scheduled_cache_preload) { FactoryBot.create :scheduled_cache_preload, source: dashboard_with_auto_preload, tenant: admin.tenant }
  let!(:lazy_settings) do
    {
      type: 'dropdown',
      selected_value: 'Spain',
      dropdown_source: 'manual',
      is_child_filter: false,
      override: true,
      dropdown_manual_entries: "Spain, Spain\nUK, UK",
      dropdown_lazy_loading: true,
    }
  end
  let!(:filter) { create :shared_filter, name: 'dropdown', settings: lazy_settings }
  let!(:filter_value) { FactoryBot.create :filter_value, filter_valuable: scheduled_cache, value: { selected_value: 'Spain' },
                            filter_ownership: fo1, settings: lazy_settings }
  let!(:fo1) do
    create :filter_ownership, filterable: dashboard_with_auto_preload, shared_filter: filter, var_name: 'Country'
  end

  describe 'filters in email schedule edit modal' do
    it 'shows the correct default filter' do
      safe_login(admin, (dashboard_path dashboard_with_auto_preload).to_s)

      wait_for_element_load('.cache-status')
      page.find('.cache-status').hover
      wait_for_element_load('.cache-status__popover')
      page.find('.cache-status__popover').click_button('Cache Settings')
      safe_click('.ci-additional-preloads')
      safe_click('.ci-edit')

      expect(page.find('.select2-selection__rendered').has_content?('Spain')).to be true
    end
  end
end
