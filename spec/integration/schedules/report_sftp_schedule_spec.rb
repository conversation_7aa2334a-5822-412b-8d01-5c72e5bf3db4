# typed: false
require 'rails_helper'

# @tag #data_delivery/sftp
describe 'report adding new sftp schedule', js: true, stable: true do
  let!(:sftp_source) { FactoryBot.create :test_ds_sftp }
  let(:es_list_container) {
    '.data-schedule-list.vue-datatable-wrapper'
  }

  before do
    FeatureToggle.toggle_global('integrations:sftp', true)

    sql = <<-SQL.strip_heredoc
         select generate_series(1, 25) as num;
    SQL
    @report = FactoryBot.create :query_report, title: 'Report For Emails', query: sql
    qlogin(:admin, query_report_path(@report))
  end

  it 'should be able to modify sftp schedule' do
    wait_for_element_load '.ci-toggle'
    page.find('.ci-toggle').click
    test_create
    test_edit

    safe_click('.ci-actions')
    page.first('.ci-es-delete').click
    wait_for_element_load '.ci-confirm-delete'
    page.first('.ci-confirm-delete').click

    wait_expect('No Schedules') { page.first('.vue-datatable-wrapper tbody td').text }
  end

  def test_edit
    safe_click('.ci-actions')
    wait_for_element_load('.ci-es-edit')
    page.first('.ci-es-edit').click

    wait_for_element_load('.ci-file-path')
    fill_text('.ci-file-path', 'new_path_name')

    wait_for_element_load('.ci-es-done')
    page.find('.ci-es-done').click

    sleep 0.5
    es = EmailSchedule.last

    expect(es.dest_type).to eq('SftpDest')
    expect(es.dest.format).to eq('csv')
    expect(es.dest.file_path).to eq('new_path_name')
  end

  def test_create
    wait_for_element_load '.ci-data-schedules-dropdown'
    page.find('.ci-data-schedules-dropdown').click
    page.find('.ci-new-sftp-dest').click
    wait_for_element_load('.ci-source-ds')
    fill_text('.ci-file-path', 'tmp_file')
    wait_for_element_load '.ci-es-done'
    sleep(2)
    page.find('.ci-es-done').click
    sleep(2)
    es = EmailSchedule.last
    expect(es.dest_type).to eq('SftpDest')
    expect(es.dest.format).to eq('csv')
    expect(es.dest.file_path).to eq('tmp_file')
    wait_for_element_load("#{es_list_container} tbody tr")
    expect(page.all("#{es_list_container} tbody tr").length).to eq 1
  end
end
