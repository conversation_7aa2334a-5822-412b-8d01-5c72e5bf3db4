# typed: false
require 'rails_helper'

describe 'Enable password-protect for excel attachment in excel', js: true, stable: true do
  let (:report) {
    FactoryBot.create :query_report, title: 'Report For Emails', query: 'select generate_series ( 1, 25 ) as num'
  }

  before do
    FeatureToggle.toggle_global 'data_schedules:test_execution', true
    qlogin :admin, query_report_path(report)

    fill_in_required_info_for_schedule
  end

  it 'works' do
    enable_password_protected_mode_for_excel

    expect_generated_password_is_displayed_for_copy

    click_test_run

    expect_attached_excel_is_encrypted

    save_schedule

    expect_to_see_lock_icon_in_schedule_list

    edit_schedule

    expect_to_see_stars_in_password_box
  end

  def fill_in_required_info_for_schedule
    wait_for_element_load '.ci-data-schedules-dropdown'
    page.find('.ci-data-schedules-dropdown').click
    page.first('.hui-dropdown-floating .ci-new-email-dest').click
    wait_for_element_load '.ci-es-recipients'
    fill_text '.ci-es-recipients', '<EMAIL>'
  end

  def enable_password_protected_mode_for_excel
    safe_click'.ci-enable-password'
  end

  def expect_saving_to_warn_missing_password
    find('.ci-es-done').click
    wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
    warning = page.find('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]').text
    expect(warning).to include 'generate password'
  end

  def generate_password
    find('.regenerate-password').click
  end

  def expect_generated_password_is_displayed_for_copy
    wait_for_element_load('.ci-password')
    password = page.find('.ci-password').value
    expect(password).to match /\d+|\w+/ # reject empty field or all stars field
  end

  def click_test_run
    safe_click('.ci-test-run-es')
    safe_click('.modal-dialog .ci-confirm')
  end

  def expect_attached_excel_is_encrypted
    footer = page.first '.h-modal-footer'

    wait_for_element_load { footer.first('.ci-test-status .label')&.text == 'Success' }

    email = ActionMailer::Base.deliveries.last

    wait_for_element_load('.ci-password')
    password = page.find('.ci-password').value
    test_encrypted_excel! email.attachments.first.body.raw_source, password
  end

  def save_schedule
    find('.ci-es-done').click
  end

  def expect_to_see_lock_icon_in_schedule_list
    wait_for_element_load '.ci-password-protected'
  end

  def edit_schedule
    safe_click('.ci-actions')
    find('.ci-es-edit').click
  end

  def expect_to_see_stars_in_password_box
    wait_for_element_load('.ci-password')
    password = page.find('.ci-password').value
    expect(password =~ /^\*+$/).to be 0
  end
end
