# typed: ignore
require 'rails_helper'

describe 'slack schedules on dashboard v1', js: true, stable: true do
  before do
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('integrations:slack', true)
    FactoryBot.create :slack_authorization, user_id: admin.id
    update_tenant_slack_settings
    VcrHelper.ignore_hosts('127.0.0.1', 'localhost')
  end

  let(:admin) { get_test_admin }
  let(:query_report) { FactoryBot.create :query_report, title: 'Test Report' }

  it 'can create new schedule and appear on data schedule table' do
    safe_login admin, query_report_path(query_report)
    safe_click('.ci-data-schedules-dropdown')

    VCR.use_cassette('slack/list_all_channels') do
      VCR.use_cassette('slack/get_team_info') do
        safe_click('.ci-new-slack-dest')
        wait_for_element_load('.slack-dest-form')
        wait_for_element_load('.ci-channels-dropdown')
        select_h_select_option('.ci-channels-dropdown', value: 'CHANNEL_ID')
      end
    end
    page.find('.ci-es-done').click
    wait_expect(1) { page.all('table.h-table > tbody > tr').count }
  end

  it 'can create new schedule with multi-destination' do
    safe_login admin, query_report_path(query_report)
    safe_click('.ci-data-schedules-dropdown')

    VCR.use_cassette('slack/list_all_channels') do
      VCR.use_cassette('slack/get_team_info') do
        safe_click('.ci-new-slack-dest')
        wait_for_element_load('.slack-dest-form')
        wait_for_element_load('.ci-channels-dropdown')
        # select 2 channels
        select_h_select_option('.ci-channels-dropdown', value: 'CHANNEL_ID')
        select_h_select_option('.ci-channels-dropdown', value: 'CHANNEL_ID_2')
      end
    end
    page.find('.ci-es-done').click
    # have 2 destinations
    wait_expect(2) { page.all('.slackdest-destination').count }
  end
end
