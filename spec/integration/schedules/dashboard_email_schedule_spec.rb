# typed: false
require 'rails_helper'

describe 'Dashboard Email Schedule', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }

  before do
    sql = <<-SQL.strip_heredoc
      select generate_series(1, 25) as num;
    SQL
    @report = FactoryBot.create :query_report, title: 'Report For Emails', query: sql
    @dashboard = FactoryBot.create :dashboard

    qlogin(:admin, dashboard_path(@dashboard))
    sleep 1
  end

  def check_for_all_widgets_selected
    check_for_widgets_selected @dashboard.items.length.times.to_a
  end

  def selection_mode_elems
    page.all('.checkbox-select input[type="radio"]')
  end

  def check_for_widgets_selected expected_widget_indices
    elems = []
    wait_for { elems = page.all('.checkbox-select input[type="checkbox"]'); elems.length > 0 }

    elems.each_with_index do |elem, index|
      if expected_widget_indices.include? index
        expect(elem[:checked]).to eq "true"
      else
        expect(elem[:checked]).to eq nil
      end
    end
  end

  # commented this out to fix later (need to rewrite the test)
  xit 'does not modify values when switching between selection modes' do
    recipients = ['<EMAIL>', '<EMAIL>']

    page.find('.ci-toggle-option').click
    page.find('.ci-email-schedules').click
    page.find('.ci-emaildest-schedule').click
    fill_text('.ci-es-recipients', recipients.join("\n"))

    expect(selection_mode_elems[0][:checked]).to eq "true"
    selection_mode_elems[1].click
    page.all('.checkbox-select input[type="checkbox"]').each { |elem| uncheck elem[:id] }
    selection_mode_elems[0].click
    page.find('.ci-es-done').click
    wait_for { first 'table' }

    wait_expect('_all') do
      es = EmailSchedule.first
      unless es.nil?
        es.options[:selected_widgets]
      end
    end

    page.find('table tbody tr a', :text => 'Edit').click
    selection_mode_elems[1].click
    page.all('.checkbox-select input[type="checkbox"]').each { |elem| uncheck elem[:id] }
    selection_mode_elems[0].click
    selection_mode_elems[1].click

    page.all('.checkbox-select input[type="checkbox"]').each do |checkbox_elem|
      expect(checkbox_elem[:checked]).to eq nil
    end

    check 'checkbox-select-option-1'

    page.find('.ci-es-done').click
    wait_for { first 'table' }

    es = EmailSchedule.first
    expect(es.options[:selected_widgets]).to eq %w(u26zijn)
  end

  context 'Select all widgets to email' do
    def create_default_schedule
      recipients = ['<EMAIL>', '<EMAIL>']
      page.find('.ci-toggle-option').click
      page.find('.ci-email-schedules').click
      page.find('.ci-emaildest-schedule').click

      fill_text('.ci-es-recipients', recipients.join("\n"))

      expect(selection_mode_elems[0][:checked]).to eq "true"

      page.find('.ci-es-done').click
      wait_for { first 'table' }
    end

    xit 'creates schedule with correct selected_widgets value' do
      create_default_schedule

      es = EmailSchedule.first
      expect(es.options[:selected_widgets]).to eq '_all'
    end

    xit 'updates schedule with same selected_widgets value if modify nothing' do
      create_default_schedule

      page.find('table tbody tr a', :text => 'Edit').click

      expect(selection_mode_elems[0][:checked]).to eq "true"
      page.find('.ci-es-done').click
      wait_for { first 'table' }

      es = EmailSchedule.first
      expect(es.options[:selected_widgets]).to eq '_all'
    end

    xit 'updates schedule with correct selected_widgets value with changes' do
      create_default_schedule

      page.find('table tbody tr a', :text => 'Edit').click

      expect(selection_mode_elems[0][:checked]).to eq "true"
      selection_mode_elems[1].click

      check_for_all_widgets_selected
      page.all('.checkbox-select input[type="checkbox"]').each { |elem| uncheck elem[:id] }
      check 'checkbox-select-option-1'

      page.find('.ci-es-done').click
      wait_for { first 'table' }

      es = EmailSchedule.first
      expect(es.options[:selected_widgets]).to eq %w(u26zijn)
    end
  end

  xcontext 'Custom options for selecting widgets to email' do
    def create_custom_widgets_schedule
      recipients = ['<EMAIL>', '<EMAIL>']

      page.find('.ci-toggle-option').click
      page.find('.ci-email-schedules').click
      page.find('.ci-emaildest-schedule').click

      selection_mode_elems[1].click
      check_for_all_widgets_selected

      fill_text('.ci-es-recipients', recipients.join("\n"))

      page.all('.checkbox-select input[type="checkbox"]').each { |elem| uncheck elem[:id] }

      check 'checkbox-select-option-2'
      check 'checkbox-select-option-3'
      check 'checkbox-select-option-4'

      page.find('.ci-es-done').click
      wait_for { first 'table' }
    end

    it 'creates schedule with correct select_widgets value' do
      create_custom_widgets_schedule

      es = EmailSchedule.first
      expect(es.options[:selected_widgets]).to eq %w(bno1msz erbokr9 09ikozf)
    end

    it 'updates schedule with correct selected_widgets value' do
      create_custom_widgets_schedule

      page.find('table tbody tr a', :text => 'Edit').click

      expect(selection_mode_elems[1][:checked]).to eq "true"
      check_for_widgets_selected [2, 3, 4]

      page.all('.checkbox-select input[type="checkbox"]').each { |elem| uncheck elem[:id] }
      check 'checkbox-select-option-1'

      page.find('.ci-es-done').click
      wait_for { first 'table' }

      es = EmailSchedule.first
      expect(es.options[:selected_widgets]).to eq %w(u26zijn)
    end
  end

  describe 'filters in email schedule edit modal', js: true do
    let!(:lazy_settings) { {
      "type": 'dropdown',
      "selected_value": 'Spain',
      "dropdown_source": 'manual',
      "is_child_filter": false,
      "override": true,
      "dropdown_manual_entries": "Spain, Spain\nUK, UK",
      "dropdown_lazy_loading": true,
      }
    }
    let!(:email_dest) { FactoryBot.create :email_dest }
    let!(:email_schedule) do
      FactoryBot.create :email_schedule, tenant_id: admin.tenant.id, creator_id: admin.id, dest_type: 'EmailDest', dest_id: email_dest.id, source: @dashboard
    end
    let!(:filter) { create :shared_filter, name: 'dropdown', settings: lazy_settings }
    let!(:fo1) { create :filter_ownership, filterable: @dashboard, shared_filter: filter, var_name: 'Country' }
    let!(:filter_value1) { create :filter_value, filter_valuable: email_schedule, value: { selected_value: 'Spain' }, filter_ownership: fo1, settings: lazy_settings }

    it 'shows the correct default filter' do
      safe_login(admin, (dashboard_path @dashboard).to_s)

      safe_click '.ci-email-schedules'
      safe_click '.ci-actions'
      safe_click '.ci-es-edit'
      wait_for_element_load '.data-schedule-edit-modal'

      expect(page.find('.select2-selection__rendered').has_content?('Spain')).to be true
    end
  end
end
