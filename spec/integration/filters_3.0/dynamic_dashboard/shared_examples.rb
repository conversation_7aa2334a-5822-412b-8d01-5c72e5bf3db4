# typed: false

require 'rails_helper'
require_relative 'shared_contexts'

shared_examples 'Filter 3.0 - dynamic dashboard - executing dashboard 3.0' do
  it 'can toggle filters panel' do
    safe_click('.ci-filter-label')
    wait_expect(1) { page.all('.ci-filters-panel').count }
    wait_expect(1) { page.all('.ci-filter-form.focus').count } # focus check
    wait_expect(1) { page.all('.h-dynamic-filter.highlighted').count }
    expect(page.first('.ci-filters-panel .ci-submit').disabled?).to be true # disabled since there's no change
    page.first('.ci-filters-panel .ci-cancel').click
    wait_expect(0) { page.all('.ci-filters-panel', wait: false).count }
  end

  it 'shows mapped conditions on widget' do
    page.first('.ci-widget-conditions-trigger').hover
    wait_for_element_load('.ci-widget-condition')
    expect(page.first('.ci-widget-condition').text.gsub("\n", ' ')).to eq 'new_sql_model Name is "alice"'
  end

  it 'allows interaction with filter input' do
    safe_click('.ci-filter-label')
    wait_for_element_load '.ci-filters-panel'
    safe_click('.ci-filters-panel')
    safe_click('.ci-filters-panel .ci-value-select')
    # sleep 100
    if page.has_css?('.ci-filters-panel .ci-value-select.h-select')
      wait_expect(['bob']) do
        page.all('.h-select__options-item').map(&:text)
      end
      wait_for_element_load('.dynamic-tags-selected-values')
      if page.has_css?('.dynamic-tags-selected-values .clear-all-btn')
        safe_click('.dynamic-tags-selected-values .clear-all-btn')
      else
        safe_click('.h-select-panel .h-select-clear-selected-values')
      end
      wait_expect(['alice', 'bob']) do
        page.all('.h-select__options-item').map(&:text)
      end
      page.find('.ci-filters-panel .ci-value-select input').send_keys('c')
      wait_expect(['alice', "+\nAdd option c"]) do
        page.all('.h-select__options-item').map(&:text)
      end
      page.find('.ci-filters-panel .ci-value-select input').send_keys('a')
      wait_expect(["+\nAdd option ca"]) do
        page.all('.h-select__options-item').map(&:text)
      end
      page.find('.ci-filters-panel .ci-value-select input').send_keys(:enter)
      wait_expect(['alice', 'bob']) do
        page.all('.h-select__options-item').map(&:text)
      end
    else
      wait_for_element_load('.dynamic-tags-selected-values')
      safe_click('.dynamic-tags-selected-values .clear-all-btn')
      wait_expect(false) { page.has_css?('.dynamic-tags-selected-values', wait: false) }
      page.find('.ci-filters-panel .ci-value-select input').send_keys('c')
      wait_expect(true) { page.find('.h-popover').text.include?('Add option c') }
      page.find('.ci-filters-panel .ci-value-select input').send_keys('a')
      wait_expect(true) { page.find('.h-popover').text.include?('Add option ca') }
      page.find('.ci-filters-panel .ci-value-select input').send_keys(:enter)
      wait_expect(true) { page.find('.h-popover').text.include?('Clear All (1)') }
    end

    page.first('.ci-filters-panel .ci-submit').click
    wait_for_widget_load
    wait_for_element_load('.result-viz')
    wait_expect('No data match the applied condition') { page.first('.h-report-widget .result-viz').text }
  end

  context 'many options' do
    include_context 'dm_field filter source'
    let(:query_model_sql) do
      <<~SQL
        with t(name,address) as (
          values('alice', '11'),
            ('bob', '21'),
            ('bob1', '22'),
            ('bob2', '23'),
            ('bob3', '24'),
            ('bob4', '25'),
            ('bob5', '26')
        )
        select * from t
      SQL
    end

    it 'shows total options count' do
      safe_click('.ci-filter-label')
      wait_for_element_load '.ci-filters-panel'
      safe_click('.ci-filters-panel')

      expect do
        safe_click('.ci-filters-panel .ci-value-select .h-select__container .h-select-input-container')
        wait_expect(['bob', 'bob1', 'bob2', 'bob3', 'bob4', 'bob5']) do
          page.all('.h-select__options-item').map(&:text)
        end
      end.to change { QueryRun.where(tenant_id: admin.tenant_id).count }.by(1)

      expect do
        wait_for_element_load('.h-select-panel .dynamic-tags-selected-values')
        if page.has_css?('.h-select-panel .dynamic-tags-selected-values .clear-all-btn')
          safe_click('.h-select-panel .dynamic-tags-selected-values .clear-all-btn')
        else
          safe_click('.h-select-panel .h-select-clear-selected-values')
        end
        wait_expect(['alice', 'bob', 'bob1', 'bob2', 'bob3', 'bob4', 'bob5']) do
          page.all('.h-select__options-item').map(&:text)
        end
        expect(page.first('.ci-total-options-count').text).to match(/Loaded 7 out of 7 available options/i)
        safe_click('.ci-filters-panel .ci-value-select')
        page.first('.ci-filters-panel .ci-value-select input').send_keys('b')
        wait_expect(['bob', 'bob1', 'bob2', 'bob3', 'bob4', 'bob5', "+\nAdd option b"]) do
          page.all('.h-select__options-item').map(&:text)
        end
        expect(page.first('.ci-total-options-count').text).to match(/Loaded 6 out of 6 available options/i)
      end.not_to change { QueryRun.where(tenant_id: admin.tenant_id).count }
      expect do
        if page.has_css?('.ci-filters-panel .ci-value-select .ci-refresh-search-cache')
          safe_click('.ci-filters-panel .ci-value-select .ci-refresh-search-cache')
          sleep 1
          wait_for_element_load '.ci-filters-panel .ci-value-select .ci-refresh-search-cache'
        else
          safe_click('.ci-refresh-search-cache')
          sleep 1
          wait_for_element_load '.ci-refresh-search-cache'
        end
      end.to change { QueryRun.where(tenant_id: admin.tenant_id).count }.by(1)
    end
  end

  it 'can change filter value' do
    safe_click('.ci-filter-label')
    wait_for_element_load '.ci-filters-panel'
    select_filter_value('.ci-filters-panel', operator: 'is_not')
    page.first('.ci-filters-panel .ci-submit').click

    wait_for_widget_load
    wait_for_element_load('.result-viz')
    wait_expect('bob') { page.first('.h-report-widget .result-viz').text }

    # test the new starts_with operator
    safe_click('.ci-filter-label')
    select_filter_value('.ci-filters-panel', operator: 'starts_with')
    page.find('.h-input.ci-value-select').set('ali')
    page.first('.ci-filters-panel .ci-submit').click

    wait_for_widget_load
    wait_for_element_load('.result-viz')
    wait_expect('alice') { page.first('.h-report-widget .result-viz').text }
  end

  it 'can clear filter value' do
    safe_click('.ci-filter-label')
    clear_all_values
    page.first('.ci-filters-panel .ci-submit').click

    wait_for_widget_load
    wait_for_element_load('.result-viz')
    wait_expect("Name\n1\nalice\n2\nbob") { page.first('.h-report-widget .result-viz').text }
  end

  it 'can store and retrieve filter states' do
    safe_click('.ci-filter-label')
    wait_for_element_load '.ci-filters-panel'
    select_filter_value('.ci-filters-panel', operator: 'is_not')
    page.first('.ci-filters-panel .ci-submit').click
    wait_for_viz_load
    wait_expect('bob') { page.first('.h-report-widget .result-viz').text }
    bob_url = page.current_url

    safe_click('.ci-filter-label')
    wait_for_element_load '.ci-filters-panel'
    select_filter_value('.ci-filters-panel', operator: 'is')
    page.first('.ci-filters-panel .ci-submit').click
    wait_for_viz_load
    wait_expect('alice') { page.first('.h-report-widget .result-viz').text }
    alice_url = page.current_url

    visit(bob_url)
    wait_for_viz_load
    wait_expect('bob') { page.first('.h-report-widget .result-viz').text }

    visit(alice_url)
    wait_for_viz_load
    wait_expect('alice') { page.first('.h-report-widget .result-viz').text }
  end

  context 'related filters' do
    include_context 'dm_field filter source'
    let(:default_condition) do
      DataModeling::Values::Condition.new(operator: 'is', values: [])
    end
    let(:filter_definition) do
      FactoryBot.create :dynamic_filter_definition, filter_source: filter_source, default_condition: default_condition,
                                                    label: 'name'
    end
    let(:query_model_sql) do
      <<~SQL
        with t(country,city,name) as (
          values('vn', 'hcm', 'a'),
            ('vn', 'hn', 'b'),
            ('vn', 'hn', 'c'),
            ('sg', 'sg', 'd')
        )
        select * from t
      SQL
    end
    let!(:name_filter) { lonely_filter }

    let(:filter_source2) do
      FactoryBot.create :dm_field_filter_source, data_set: query_model_data_set,
                                                  field_path: field_path.merge(field_name: 'country')
    end
    let(:filter_definition2) do
      FactoryBot.create :dynamic_filter_definition, filter_source: filter_source2, default_condition: default_condition,
                                                    label: 'country'
    end
    let!(:country_filter) do
      FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, order: 1,
                                          dynamic_filter_definition: filter_definition2
    end

    let(:filter_source3) do
      FactoryBot.create :dm_field_filter_source, data_set: query_model_data_set,
                                                  field_path: field_path.merge(field_name: 'city')
    end
    let(:filter_definition3) do
      FactoryBot.create :dynamic_filter_definition, filter_source: filter_source3, default_condition: default_condition,
                                                    label: 'city'
    end
    let!(:city_filter) do
      FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, order: 2,
                                          dynamic_filter_definition: filter_definition3
    end

    let!(:mapping_country_name) do
      FactoryBot.create :dynamic_filter_mapping, dynamic_filter: country_filter, viz_conditionable: name_filter,
                                                  field_path: field_path.merge(field_name: 'country')
    end
    let!(:mapping_country_city) do
      FactoryBot.create :dynamic_filter_mapping, dynamic_filter: country_filter, viz_conditionable: city_filter,
                                                  field_path: field_path.merge(field_name: 'country')
    end
    let!(:mapping_city_name) do
      FactoryBot.create :dynamic_filter_mapping, dynamic_filter: city_filter, viz_conditionable: name_filter,
                                                  field_path: field_path.merge(field_name: 'city')
    end

    before do
      safe_login(admin, url)
      wait_for_dashboard_load
    end

    it 'shows suggestions correctly' do
      safe_click('.ci-filter-label')
      wait_for_element_load '.ci-filters-panel'
      safe_click('.ci-filters-panel')
      safe_click("#dynamic-filter-#{name_filter.id} .ci-value-select")
      wait_expect(['a', 'b', 'c', 'd']) do
        page.all('.h-select__options-item').map(&:text)
      end
      safe_click("#dynamic-filter-#{city_filter.id} .ci-value-select")
      wait_expect(['hcm', 'hn', 'sg']) do
        page.all('.h-select__options-item').map(&:text)
      end

      safe_click("#dynamic-filter-#{country_filter.id} .ci-value-select")
      wait_expect(['sg', 'vn']) do
        page.all('.h-select__options-item').map(&:text)
      end
      select_filter_value("#dynamic-filter-#{country_filter.id}", operator: 'is', values: ['vn'])

      safe_click("#dynamic-filter-#{name_filter.id} .ci-value-select")
      wait_expect(['a', 'b', 'c']) do
        page.all('.h-select__options-item').map(&:text)
      end

      safe_click("#dynamic-filter-#{city_filter.id} .ci-value-select")
      wait_expect(['hcm', 'hn']) do
        page.all('.h-select__options-item').map(&:text)
      end

      select_filter_value("#dynamic-filter-#{city_filter.id}", operator: 'is', values: ['hn'])
      safe_click("#dynamic-filter-#{name_filter.id} .ci-value-select")
      wait_expect(['b', 'c']) do
        page.all('.h-select__options-item').map(&:text)
      end
    end
  end
end

shared_examples 'Filter 3.0 - dynamic dashboard - expanded widget 3.0' do
  it 'can expand widget' do
    page.find('.ci-report-widget').hover
    page.find('.ci-expand-widget', match: :first).click
    wait_expect(1) { page.all('.ci-expanded-widget').count }
    wait_for_widget_load

    wait_for_element_load('.result-viz')
    wait_expect("Name\nalice") { page.first('.ci-expanded-widget .result-viz').text }
  end

  context 'with multiple widgets' do
    let!(:report_widget2) { FactoryBot.create :dashboard_widget, source: qr, dashboard: dashboard }

    it 'expands the correct when visiting direct link' do
      job_count = Job.count

      visit(StringUtils.add_param_to_url(url, '_e' => report_widget2.id))
      wait_expect(1) { page.all('.ci-expanded-widget').count }
      wait_for_all_ajax_requests
      wait_expect("Name\n1\nalice\n2\nbob\n1 - 2 of 2\n1\n25") do
        page.first('.ci-expanded-widget .result-viz').text
      end
      # make sure only the current 1 widget is being loaded
      expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 1, wait: true)
      expect(Job.count).to eq(job_count + 1)

      # all widgets should be loaded after collapsing the current widget
      safe_click('.ci-collapse-widget')
      wait_expect(0) { page.all('.ci-expanded-widget', wait: false).count }
      wait_for_all_ajax_requests
      expected_result_texts = [
        'alice',
        "Name\n1\nalice\n2\nbob",
      ]

      wait_expect(expected_result_texts) do
        page.all('.h-report-widget .result-viz').map(&:text)
      end
    end
  end

  it 'filters work' do
    page.find('.ci-report-widget').hover
    page.find('.ci-expand-widget', match: :first).click
    wait_expect(1) { page.all('.ci-expanded-meta').count }
    wait_for_widget_load

    safe_click('.ci-filter-label')
    clear_all_values
    page.first('.ci-filters-panel .ci-submit').click

    wait_for_widget_load
    wait_for_element_load('.result-viz')
    wait_expect(['alice', 'bob']) do
      page.all('.ci-expanded-widget .result-viz .ag-row').map { |row| row.text.split("\n")[1..].join(' ') }
    end
    # check pagination: https://app.asana.com/0/1119333906108473/1201034932028036
    wait_expect(1) { page.all('.ci-expanded-widget .result-viz .pagination-wrapper').count }
  end
end