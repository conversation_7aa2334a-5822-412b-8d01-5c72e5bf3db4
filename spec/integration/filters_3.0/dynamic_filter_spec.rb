require 'rails_helper'

describe 'Dynamic dashboard (filters 3.0)', js: true do
  include_context 'dynamic_dashboard'

  let(:filter_type) { DynamicFilters::Constants::FilterTypes::STRING }
  let!(:name_filter_source) do
    FactoryBot.create :manual_filter_source, manual_options: nil
  end
  let!(:name_filter_definition) do
    FactoryBot.create :dynamic_filter_definition, default_condition: { operator: 'is', values: [] },
                                                  filter_source: name_filter_source, filter_type: filter_type
  end

  shared_context 'with dynamic filter specs' do
    let(:select_container_selector) { '.h-select__container-values-value' }
    context 'when using manualtext filter' do
      it 'does not show panel' do
        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        page.find('#dynamic-filter-1 .viz-filter-value-input .dynamic-tags').click
        expect(page).not_to have_css('.dynamic-tags-popover')
      end

      it 'can insert mass input' do
        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        page.find('#dynamic-filter-1 .viz-filter-value-input .dynamic-tags').click

        contents = (0..10).to_a.inject('') { |content, n| "#{content},#{n}" }
        input = page.find('.dynamic-tags-input')
        input.fill_in(with: contents)
        emulate_copy_paste_input(input)

        expect(page).to have_css('.dynamic-tags-popover .dynamic-tags-selected-values')
        expect(page.find('.dynamic-tags-popover .dynamic-tags-selected-values .clear-all-btn').text).to eq 'Clear All (11)'
      end
    end

    context 'number filter' do
      let!(:filter_type) { DynamicFilters::Constants::FilterTypes::NUMBER }

      it 'number filter should limit display values and not show panel if empty' do
        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        page.find('#dynamic-filter-1 .viz-filter-value-input .dynamic-tags').click
        expect(page).not_to have_css('.dynamic-tags-popover')

        name_filter_definition.update!(default_condition: { operator: 'is',
                                                            values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
                                                                      16, 17, 18, 19, 20, 21,], })

        visit current_path

        safe_click('.h-filter-label:first-child')
        expect(page).to have_css('#dynamic-filter-1 .selected-value.hidden-tag')
        page.find('#dynamic-filter-1 .viz-filter-value-input .dynamic-tags .toggle-btn').click
        expect(page).to have_css('.dynamic-tags-popover .dynamic-tags-selected-values')
      end

      it 'can insert mass input separated by spacing' do
        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        page.find('#dynamic-filter-1 .viz-filter-value-input .dynamic-tags').click
        expect(page).not_to have_css('.dynamic-tags-popover')
        contents = (0..10).to_a.inject('') { |content, n| "#{content} #{n}" }
        input = page.find('.dynamic-tags-input')
        input.fill_in(with: contents)
        emulate_copy_paste_input(input)

        expect(page).to have_css('.dynamic-tags-popover .dynamic-tags-selected-values')
        expect(page.find('.dynamic-tags-popover .dynamic-tags-selected-values .clear-all-btn').text).to eq 'Clear All (11)'
      end
    end

    context 'single select input filter' do
      let!(:name_filter_source) do
        FactoryBot.create :dm_field_filter_source, data_set_id: data_set.id,
                                                    field_path: { model_id: products_model.id, field_name: 'name' }
      end
      let!(:name_filter_definition) do
        FactoryBot.create :dynamic_filter_definition, default_condition: { operator: 'is', values: ['Lame'] },
                          input_type: 'single_select', filter_source: name_filter_source, filter_type: filter_type
      end

      it 'displays selected value as pill' do
        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        page.find(select_container_selector).assert_matches_style('border-radius' => '4px')
        page.find(select_container_selector).assert_matches_style('background-color' => 'rgba(232, 242, 253, 1)') # bg-blue-50
      end
    end

    context 'data model field filter' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end
      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: ['Lame', 'Khaby'] },
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end
      it 'be able to deselect selected value' do
        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        expect(page.has_css?('.dynamic-tags-input-container .deselect-item')).to eq(true)
      end
    end
  end

  context 'when ui:use_filter_select is disabled' do
    include_context 'with dynamic filter specs'
  end

  context 'when ui:use_filter_select is enabled' do
    before do
      FeatureToggle.toggle_global('ui:use_filter_select', true)
    end
    include_context 'with dynamic filter specs' do
      let(:select_container_selector) { '.selected-value-wrapper .selected-value' }
    end

    context 'single select input filter that allows empty values' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end

      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: ['egg'] },
          input_type: 'single_select',
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end

      it 'is able to allow empty values' do
        qlogin(admin, "/dashboards/#{dashboard.id}")

        open_filter_definition_form

        # toggle the allow empty values toggle to on
        page.first('[data-ci="ci-allow-empty-value-toggle"]').click

        # remove the current default value, expect that there is no validation error and we are able to save the form
        page.find('.dynamic-tags-input-container .deselect-item').click
        expect(page).to have_css('input.dynamic-tags-input[placeholder="Type or paste from a list..."]')
        expect(page).not_to have_css('.h-form-invalid-feedback', visible: true)
        safe_click('.h-modal-footer .ci-submit')
        wait_for_element_load '.ci-filters'

        # verify that the input tag is removable
        wait_for_all_holistics_loadings # widget loaded
        safe_click('.h-filter-label:first-child') unless is_filters_panel_open?
        expect(page.has_css?('.dynamic-tags-input-container .deselect-item')).to eq(true)

        # try to remove the current filter and verify the correctness of the filter
        page.find('.dynamic-tags-input-container .deselect-item').click
        safe_click('.ci-filters .ci-submit')

        wait_for_all_holistics_loadings # widget loaded
        close_filters_panel_if_open

        rows = page.all('.ag-row').map { |row| row.text.split("\n").join(' ') }
        expect(rows).to match_array ['1 bread 2.25 available', '2 milk 3 available', '3 egg 5 available', '4 bread 2.25 expired']
      end
    end

    context 'single select input filter that does not allow empty values' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end

      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: ['egg'] },
          input_type: 'single_select',
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end

      it "doesn't allow remove current input value" do
        qlogin(admin, "/dashboards/#{dashboard.id}")

        open_filter_definition_form

        # toggle the allow empty values toggle to on
        page.first('[data-ci="ci-allow-empty-value-toggle"]').click

        # ---- remove the current default value, expect that there is validation error and we have to choose a default value in order to save the form
        page.find('.dynamic-tags-input-container .deselect-item').click

        # toggle the allow empty values toggle to off
        page.first('[data-ci="ci-allow-empty-value-toggle"]').click
        expect(page).to have_css('input.dynamic-tags-input[placeholder="Type or paste from a list..."]')
        expect(page.all('.h-form-invalid-feedback').map(&:text)).to include('Default value is required')

        # select new default value and save the form
        page.find('[data-ci="ci-default-value-input"]').click
        wait_for_element_load('.ci-viz-filter-value-input-option')

        # select the milk option and expect that the dropdown will be closed
        page.find('.filter-select-option[data-value="milk"]').click
        expect(page).not_to have_css('.ci-viz-filter-value-input-option')

        # finally, save the form
        safe_click('.h-modal-footer .ci-submit')
        wait_for_element_load '.ci-filters'
        # ------ end of remove the current default value and select new value

        # verify that the input tag is non-removable
        wait_for_all_holistics_loadings # widget loaded
        safe_click('.h-filter-label:first-child') unless is_filters_panel_open?
        expect(page.has_css?('.dynamic-tags-input-container .deselect-item')).to eq(false)

        close_filters_panel_if_open

        # expect that the dashboard result is filtered with the current value 'egg', instead of the new default value
        rows = page.all('.ag-row').map { |row| row.text.split("\n").join(' ') }
        expect(rows).to match_array ['3 egg 5 available']
      end

      it 'works correctly with boolean filter' do
        filter_source = ManualFilterSource.create!(manual_options: nil, user: admin, tenant: admin.tenant)
        name_filter_definition.filter_source = filter_source
        name_filter_definition.filter_type = DynamicFilters::Constants::FilterTypes::BOOLEAN
        name_filter_definition.default_condition = { operator: 'is', values: [false] }
        name_filter_definition.save!

        qlogin(admin, "/dashboards/#{dashboard.id}")
        safe_click('.h-filter-label:first-child')
        expect(page.find(select_container_selector).text).to eq("false")
      end
    end
  end

  # https://www.notion.so/holistics/Behavior-when-updating-Input-types-8f2bf0f0a9684091a6737ce5059fe498?pvs=4
  context 'refresh behavior when changing input_type' do
    before do
      FeatureToggle.toggle_global('ui:use_filter_select', true)
    end

    context 'change from a multi-select, empty value -> single-select that does not allow empty values' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end

      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: [] },
          input_type: 'multi_operator',
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end

      it 'should refresh the dashboard' do
        qlogin(admin, "/dashboards/#{dashboard.id}")

        open_filter_definition_form

        # change the input type to single-select
        select_h_select_option('[data-ci="ci-filter-input-type-select"]', value: 'single_select')
        expect(page).to have_css('input.dynamic-tags-input[placeholder="Type or paste from a list..."]')
        expect(page.all('.h-form-invalid-feedback').map(&:text)).to include('Default value is required')

        # select new default value and save the form
        page.find('[data-ci="ci-default-value-input"]').click
        wait_for_element_load('.ci-viz-filter-value-input-option')
        # select the milk option and expect that the dropdown will be closed
        page.find('.filter-select-option[data-value="milk"]').click
        expect(page).not_to have_css('.ci-viz-filter-value-input-option')
        # finally, save the form
        safe_click('.h-modal-footer .ci-submit')
        wait_for_element_load '.ci-filters'

        # verify that the input tag is non-removable
        wait_for_all_ajax_requests
        wait_for_all_holistics_loadings # widget loaded
        safe_click('.h-filter-label:first-child') unless is_filters_panel_open?
        wait_expect('milk') { page.find('[data-ci="ci-tag-label-text"]').text }
        expect(page.has_css?('.dynamic-tags-input-container .deselect-item')).to eq(false)

        close_filters_panel_if_open

        # expect that the dashboard result is filtered with the current value 'milk'
        rows = page.all('.ag-row').map { |row| row.text.split("\n").join(' ') }
        expect(rows).to match_array ['2 milk 3 available']
      end
    end

    context 'change from a multi-select, > 1 values -> single-select that does not allow empty values' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end

      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: %w[egg bread] },
          input_type: 'multi_operator',
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end

      it 'should refresh the dashboard' do
        qlogin(admin, "/dashboards/#{dashboard.id}")

        open_filter_definition_form

        # change the input type to single-select
        select_h_select_option('[data-ci="ci-filter-input-type-select"]', value: 'single_select')

        # select new default value and save the form
        page.find('[data-ci="ci-default-value-input"]').click
        wait_for_element_load('.ci-viz-filter-value-input-option')
        # select the milk option and expect that the dropdown will be closed
        page.find('.filter-select-option[data-value="milk"]').click
        expect(page).not_to have_css('.ci-viz-filter-value-input-option')
        # finally, save the form
        safe_click('.h-modal-footer .ci-submit')
        wait_for_element_load '.ci-filters'

        # verify that the input tag is non-removable and have the correct label
        wait_for_all_holistics_loadings # widget loaded
        safe_click('.h-filter-label:first-child') unless is_filters_panel_open?
        wait_expect('milk') { page.find('[data-ci="ci-tag-label-text"]').text }
        expect(page.has_css?('.dynamic-tags-input-container .deselect-item')).to eq(false)
        expect(page.find('.ci-filter-label').text).to include('is "milk"')
        expect(page.find('.ci-filter-label').text).not_to include('is "egg"')

        close_filters_panel_if_open

        # expect that the dashboard result is filtered with the current value 'milk'
        rows = page.all('.ag-row').map { |row| row.text.split("\n").join(' ') }
        expect(rows).to match_array ['2 milk 3 available']
      end
    end

    context 'change from a multi-select, > 1 values -> single-select that allows empty values' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end

      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: %w[egg bread] },
          input_type: 'multi_operator',
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end

      it 'should refresh the dashboard' do
        qlogin(admin, "/dashboards/#{dashboard.id}")

        # edit the DynamicFilterDefinitionForm
        wait_for_element_load '.ci-filters'
        safe_click('.h-filter-label:first-child')
        filter_el = page.all('.ci-dynamic-filter')[0]
        filter_el.hover
        filter_el.find('.ci-filter-controls', visible: true).click
        page.first('.ci-edit-filter').click
        wait_for_element_load '.ci-dynamic-filter-modal'
        wait_for_element_load '.ci-filter-default-value-select'

        # change the input type to single-select
        select_h_select_option('[data-ci="ci-filter-input-type-select"]', value: 'single_select')

        # toggle the allow empty values toggle to on
        page.first('[data-ci="ci-allow-empty-value-toggle"]').click

        # finally, save the form
        safe_click('.h-modal-footer .ci-submit')
        wait_for_element_load '.ci-filters'

        # verify that the input tag is empty and have the correct label
        wait_for_widget_load
        safe_click('.h-filter-label:first-child')
        expect(page.find('.ci-filter-label').text).to include('any value')
        close_filters_panel_if_open

        # verify that the data is unfiltered
        wait_for_widget_load
        wait_expect( ['1 bread 2.25 available', '2 milk 3 available', '3 egg 5 available', '4 bread 2.25 expired'] ) do
          page.all('.ag-row').map { |row| row.text.split("\n").join(' ') }.sort
        end
      end
    end

    context 'change from a single select with current empty values -> single select that does not allow empty values' do
      let!(:name_filter_source) do
        FactoryBot.create(
          :dm_field_filter_source,
          data_set_id: data_set.id,
          field_path: { model_id: products_model.id, field_name: 'name' }
        )
      end

      let!(:name_filter_definition) do
        FactoryBot.create(
          :dynamic_filter_definition,
          default_condition: { operator: 'is', values: [] },
          input_type: 'single_select_with_empty_value',
          filter_source: name_filter_source,
          filter_type: filter_type
        )
      end

      it 'should refresh the dashboard' do
        qlogin(admin, "/dashboards/#{dashboard.id}")

        open_filter_definition_form

        # change the input type to single-select (not allow empty values)
        page.first('[data-ci="ci-allow-empty-value-toggle"]').click
        # select new default value and save the form
        page.find('[data-ci="ci-default-value-input"]').click
        wait_for_element_load('.ci-viz-filter-value-input-option')
        # select the milk option and expect that the dropdown will be closed
        safe_click('.filter-select-option[data-value="milk"]')
        expect(page).not_to have_css('.ci-viz-filter-value-input-option')
        # finally, save the form
        safe_click('.h-modal-footer .ci-submit')
        wait_for_element_load '.ci-filters'

        # verify that the input tag is not empty and have the correct filter label
        wait_for_all_holistics_loadings # widget loaded
        safe_click('.h-filter-label:first-child') unless is_filters_panel_open?
        wait_expect('milk') { page.find('[data-ci="ci-tag-label-text"]').text }
        wait_expect(false) { page.has_css?('.dynamic-tags-input-container .deselect-item') }

        close_filters_panel_if_open

        # verify that the dashboard is filtered with the new value 'milk'
        rows = page.all('.ag-row').map { |row| row.text.split("\n").join(' ') }
        expect(rows).to match_array ['2 milk 3 available']
      end
    end
  end

  context 'toggle between different input types' do
    let!(:name_filter_source) do
      FactoryBot.create(
        :dm_field_filter_source,
        data_set_id: data_set.id,
        field_path: { model_id: products_model.id, field_name: 'name' }
      )
    end

    let!(:name_filter_definition) do
      FactoryBot.create(
        :dynamic_filter_definition,
        default_condition: { operator: 'is', values: %w[bread] },
        input_type: 'single_select',
        filter_source: name_filter_source,
        filter_type: filter_type
      )
    end

    it 'reset the default conditions when you switch between different filter types' do

      qlogin(admin, "/dashboards/#{dashboard.id}")

      open_filter_definition_form

      # change the input type to single-select
      page.first('[data-ci="ci-allow-empty-value-toggle"]').click
      # change the filter types to text
      select_h_select_option('[data-ci="ci-filter-type-select"]', value: 'string')

      # expect that the field and input type is not visible
      expect(page).to have_no_css('[data-ci="ci-filter-source-select"]')
      expect(page).to have_css('input.dynamic-tags-input[placeholder="Type or paste from a list..."]')
    end
  end

  context 'URL Params Filter' do
    let!(:filter_type) { DynamicFilters::Constants::FilterTypes::NUMBER }
    let!(:name_filter_definition) do
      FactoryBot.create :dynamic_filter_definition, default_condition: { operator: 'is', values: ['2'] },
                                                    filter_source: name_filter_source, filter_type: filter_type
    end

    it 'returns empty value of equal operator after refetching dynamic filter' do
      qlogin(admin, "/dashboards/#{dashboard.id}?text_filter=abc")
      wait_expect(true) { page.find('[data-icon="exclamation-triangle"]').present? }
      url = URI.parse(current_url)
      expect(url.query).to eq 'text_filter=abc'
      safe_click('.h-filter-label:first-child')
      expect(page.find('.h-input').text).to eq 'Invalid abc for number type'
      expect(page.find('.h-input [data-icon="cancel"]').present?).to be true
      safe_click('[data-ci="ci-refetch-control"]')
      # expect empty value after re-fetching filter
      wait_expect('') { page.find('.filter-select-control').text }
      # expect URL removes the query value
      url = URI.parse(current_url)
      expect(url.query).to eq nil
    end
  end

  private

  def open_filter_definition_form
    # edit the DynamicFilterDefinitionForm
    wait_for_element_load '.ci-filters'
    safe_click('.h-filter-label:first-child')
    filter_el = page.all('.ci-dynamic-filter')[0]
    filter_el.hover
    safe_click('.ci-filter-controls', { visible: false })
    safe_click('.ci-edit-filter')
    wait_for_element_load '.ci-dynamic-filter-modal'
    wait_for_element_load '.ci-filter-default-value-select'
  end

  def close_filters_panel_if_open
    if is_filters_panel_open?
      begin
        safe_click('.ci-cancel')
      rescue StandardError
        # ignore error since the filter panel could be closed during the time looking for ci-cancel
      end
    end
  end

  def is_filters_panel_open?
    page.has_css?('.ci-filters-panel', visible: true)
  end
end
