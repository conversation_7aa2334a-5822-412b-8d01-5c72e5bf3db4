# frozen_string_literal: true

# typed: ignore
require 'rails_helper'

describe 'Dynamic dashboard (filters 3.0)', js: true do
  include_context 'simple_query_model_dataset'
  let(:admin) { get_test_admin }
  let(:dashboard) do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FactoryBot.create :dashboard, version: 3, owner: admin
  end
  let(:url) { dashboard_path(dashboard) }
  let(:should_open_dashboard) { true }

  let(:query_model_sql) do
    <<~SQL
      with t(id, name, category, created_at, price, rating) as (
        values(1, 'choco', 'drink', '2020-01-01', 10, 9),
          (2, 'beer', 'drink', '2020-02-04', 6, 7),
          (3, 'chicken', 'food', '2020-03-02', 6, 8),
          (4, 'beer', 'drink', '2020-01-03', 9, 10)
      )
      select id, name, category, created_at::timestamp, price, rating from t
    SQL
  end

  def create_filter(dashboard, field_name, filter_type = 'string')
    field_path = DataModeling::Values::FieldPath.new(field_name: field_name, model_id: query_data_model.id)
    filter_source = FactoryBot.create :dm_field_filter_source, data_set: query_model_data_set, field_path: field_path
    filter_definition = FactoryBot.create :dynamic_filter_definition, filter_source: filter_source, label: field_name, default_condition: nil, filter_type: filter_type
    FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, dynamic_filter_definition: filter_definition, drillthrough_enabled: true
  end
  let!(:filter_created_at) { create_filter(dashboard, 'created_at', 'date') }
  let!(:filter_name) { create_filter(dashboard, 'name') }
  let!(:filter_category) { create_filter(dashboard, 'category') }
  let!(:filter_price) { create_filter(dashboard, 'price') }
  let!(:filter_rating) { create_filter(dashboard, 'rating') }

  def wait_for_dashboard_load
    wait_for_all_ajax_requests
    wait_for_element_load('.ci-filters') # filters loaded
    sleep 0.5
    wait_for_widget_load
  end

  def trigger_drillthrough(trigger_el, target_dashboard_id)
    retry_until_success(retries: 3) do
      trigger_el.hover
      trigger_el.right_click
      reliable_hover('.h-context-menu-content .ci-drillthrough', ".ci-drillthrough-#{target_dashboard_id}")
    end
    safe_click(".ci-drillthrough-#{target_dashboard_id}")
  end

  before do
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global('drillthrough:enabled', true)
  end

  shared_context 'dashboard 3.0 loaded' do
    before do
      Capybara.current_window.resize_to 1200, 800

      if should_open_dashboard
        qlogin(admin, url)
        wait_for_dashboard_load
      end
    end
  end

  context 'column chart' do
    let(:qr_full_column) do
      FactoryBot.create :query_report, data_set_id: query_model_data_set.id, viz_setting: viz_setting_full_column
    end
    let(:viz_setting_x_axis) do
      {
        format: { type: 'date', sub_type: 'mmm yyyy' },
        type: 'date',
        sub_type: 'mmm yyyy',
        path_hash: { model_id: query_data_model.id, field_name: 'created_at' },
        model_id: query_data_model.id,
        field_name: 'created_at',
        transformation: 'datetrunc month',
      }
    end
    let(:viz_setting_series) do
      {
        type: 'text',
        format: { type: 'string', sub_type: 'string' },
        path_hash: { model_id: query_data_model.id, field_name: 'name' },
      }
    end
    let(:viz_setting_y_axes) do
      [
        {
          label: 'Y Axis 1',
          columns: [
            {
              type: 'auto',
              color: 'auto',
              format: { type: 'number', sub_type: 'auto' },
              path_hash: { model_id: query_data_model.id, field_name: 'price' },
              aggregation: 'sum',
            },
            {
              color: 'auto',
              format: { type: 'number', sub_type: 'auto' },
              path_hash: { model_id: query_data_model.id, field_name: 'rating' },
              aggregation: 'sum',
            },
          ],
        },
        {
          label: 'Y Axis 2',
          columns: [
            {
              type: 'auto',
              color: 'auto',
              format: { type: 'number', sub_type: 'auto' },
              path_hash: { model_id: query_data_model.id, field_name: 'id' },
              aggregation: 'count',
            },
          ],
        },
      ]
    end
    let(:viz_setting_sort) do
      nil
    end
    let(:viz_setting_full_column) do
      FactoryBot.create(
        :viz_setting,
        viz_type: 'column_chart',
        fields: {
          x_axis: viz_setting_x_axis,
          series: viz_setting_series,
          y_axes: viz_setting_y_axes,
        },
        settings: {
          x_axis: { title: nil },
          legend: { enabled: true, alignment: 'bottom' },
          y_axes: [
            { max: nil, min: nil, align: 'left', title: nil, scale_type: 'linear', stack_type: 'normal', stack_series: false, maximum_groups: 2, group_long_tail: true, show_data_label: false, show_group_total: false, show_stack_total: false, show_series_percentage: false },
            { max: nil, min: nil, align: 'left', title: nil, scale_type: 'linear', stack_type: 'normal', stack_series: false, maximum_groups: 5, group_long_tail: false, show_data_label: false, show_group_total: false, show_stack_total: false, show_series_percentage: false },
          ],
          'misc' => { 'pagination_size' => 25, 'show_row_number' => true },
          'sort' => viz_setting_sort,
          'aggregation' => { 'show_total' => false, 'show_average' => false },
          'conditional_formatting' => {},
        },
        format:
          { 'name' => { 'type' => 'string', 'index' => 0, 'sub_type' => 'auto' } },
      )
    end
    let!(:report_widget_full_column) { FactoryBot.create :dashboard_widget, source: qr_full_column, dashboard: dashboard }

    include_context 'dashboard 3.0 loaded'

    context 'with dashboard timezone' do
      let(:dashboard) do
        FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
        FactoryBot.create :dashboard, version: 3, owner: admin, settings: { timezone: 'Asia/Singapore' }
      end
      let(:dashboard2) do
        FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
        create :dashboard, version: 3, owner: admin, title: 'MyLife'
      end
      let(:viz_setting) do
        create(
          :viz_setting,
          fields: {
            table_fields: [
              {
                path_hash: { field_name: 'created_at', model_id: query_data_model.id },
                type: 'timestamp',
                format: { type: 'timestamp' },
              },
              {
                path_hash: { field_name: 'id', model_id: query_data_model.id },
                type: 'auto',
                format: { type: 'auto' },
              },
            ],
          },
        )
      end
      let(:qr) { create :query_report, viz_setting: viz_setting, data_set_id: query_model_data_set.id }
      let(:data_table_widget) do
        create :dashboard_widget, source: qr, dashboard: dashboard2
      end
      let(:values) do
        ['2020-01-01 08:00:00', '1', '2020-01-03 08:00:00', '4']
      end

      before do
        FeatureToggle.toggle_global(::Tenant::FT_NEW_TIMEZONE_CONFIG, true)
        FeatureToggle.toggle_global(::Timezone::Helper::FT_DASHBOARD_TIMEZONE, true)
        FeatureToggle.toggle_global('viz_setting:stricter_format_string_to_date', true)

        create_filter(dashboard2, 'created_at', 'timestamp')
        data_table_widget
      end

      it 'should use timezone from source dashboard' do
        visit(url)
        wait_for_dashboard_load
        wait_for_element_load('.highcharts-series .highcharts-point')

        trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[4], dashboard2.id)

        expect(page.first('.ci-drillthrough-modal .query-processing-timezone').text).to eq('Asia/Singapore')
        wait_for_report_data

        wait_expect(values) do
          page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }.flatten
        end
      end

      context 'when user uses timezone dropdown in source dashboard' do
        before do
          dashboard[:settings][:allow_to_change_timezone] = true
          dashboard.save!
        end
        let(:values) do
          ['2020-01-01 09:00:00', '1', '2020-01-03 09:00:00', '4']
        end

        it 'should use timezone from source dashboard' do
          visit(url)
          wait_for_dashboard_load

          search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
          select_h_select_option('.ci-query-processing-timezone', value: 'Asia/Tokyo')
          wait_for_dashboard_load
          wait_for_element_load('.highcharts-series .highcharts-point')

          trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[4], dashboard2.id)

          expect(page.first('.ci-drillthrough-modal .query-processing-timezone').text).to eq('Asia/Tokyo')
          wait_for_report_data

          wait_expect(values) do
            page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }.flatten
          end

          page.find('.ci-drillthrough-modal .ci-report-widget').hover

          safe_click('.ci-widget-controls-trigger')
          page.find('.ci-download').hover
          safe_click('.ci-export-csv-widget')

          wait_expect(true, 30) do
            raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

            page.all('.ci-download-link').present?
          end

          download_link = page.first('.ci-download-link')['href'].to_s.presence ||
                          page.first('.ci-download-link')['data-download-url'].to_s
          cookie = page.driver.browser.manage.cookie_named('_holistics_session')[:value]

          file = URI.open(download_link, 'Cookie' => "_holistics_session=#{cookie}")

          # explicit timezone should be applied
          expect(file.read).to eql("Created At,Id\n2020-01-01 09:00:00 +0000,1\n2020-01-03 09:00:00 +0000,4\n")
        end
      end

      context 'embed' do
        before do
          FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', true)
        end
        let(:embed_link) do
          el = FactoryBot.create :embed_link, source: dashboard, filter_ownerships: [], tenant: admin.tenant, version: 3
          el.set_public_user
          el.share_source
          el
        end

        let(:embed_payload) do
          {
            drillthroughs: {
              dashboard2.id => {},
            },
            settings: {
              default_timezone: 'Asia/Tokyo',
              allow_to_change_timezone: false,
              enable_export_data: true,
            }
          }
        end
        let(:embed_token) do
          sk = embed_link.secret_key
          jwt_encode(sk, embed_payload, Time.now.to_i + 1000)
        end
        let(:should_open_dashboard) { false }
        let(:url) { "/embed/#{embed_link.hash_code}?_token=#{embed_token}" }
        let(:values) do
          ['2020-01-01 09:00:00', '1', '2020-01-03 09:00:00', '4']
        end

        it 'should use timezone from source dashboard' do
          visit(url)
          wait_for_dashboard_load
          wait_for_element_load('.highcharts-series .highcharts-point')

          trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[4], dashboard2.id)

          expect(page.first('.ci-drillthrough-modal .query-processing-timezone').text).to eq('Asia/Tokyo')
          wait_for_report_data

          wait_expect(values) do
            page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }.flatten
          end

          page.find('.ci-drillthrough-modal .ci-report-widget').hover

          safe_click('.ci-widget-controls-trigger')
          page.find('.ci-download').hover
          safe_click('.ci-export-csv-widget')

          wait_expect(true, 30) do
            raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

            page.all('.ci-download-link').present?
          end

          download_link = page.first('.ci-download-link')['href'].to_s.presence ||
                          page.first('.ci-download-link')['data-download-url'].to_s
          cookie = page.driver.browser.manage.cookie_named('_holistics_session')[:value]

          file = URI.open(download_link, 'Cookie' => "_holistics_session=#{cookie}")

          # explicit timezone should be applied
          expect(file.read).to eql("Created At,Id\n2020-01-01 09:00:00 +0000,1\n2020-01-03 09:00:00 +0000,4\n")
        end

        context 'allow to change timezone' do
          let(:embed_payload) do
            {
              drillthroughs: {
                dashboard2.id => {},
              },
              settings: {
                default_timezone: nil,
                allow_to_change_timezone: true,
                enable_export_data: true,
              }
            }
          end

          it 'should use timezone from dropdown' do
            visit(url)
            wait_for_dashboard_load

            search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
            select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
            wait_for_dashboard_load

            wait_for_element_load('.highcharts-series .highcharts-point')

            trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[4], dashboard2.id)

            wait_for_dashboard_load
            expect(page.first('.ci-drillthrough-modal .query-processing-timezone').text).to eq('Asia/Tokyo')
            wait_for_report_data

            wait_expect(values) do
              page.all('.ci-table-report-data .ag-row').map{ |row| row.all('.ag-cell').map(&:text) }.flatten
            end

            page.find('.ci-drillthrough-modal .ci-report-widget').hover

            safe_click('.ci-widget-controls-trigger')
            page.find('.ci-download').hover
            safe_click('.ci-export-csv-widget')

            wait_expect(true, 30) do
              raise page.find('.ci-export-error').text if page.all('.ci-export-error').present?

              page.all('.ci-download-link').present?
            end

            download_link = page.first('.ci-download-link')['href'].to_s.presence ||
                            page.first('.ci-download-link')['data-download-url'].to_s
            cookie = page.driver.browser.manage.cookie_named('_holistics_session')[:value]

            file = URI.open(download_link, 'Cookie' => "_holistics_session=#{cookie}")

            # explicit timezone should be applied
            expect(file.read).to eql("Created At,Id\n2020-01-01 09:00:00 +0000,1\n2020-01-03 09:00:00 +0000,4\n")
          end
        end
      end
    end

    context 'embed' do
      let(:embed_link) do
        el = FactoryBot.create :embed_link, source: dashboard, filter_ownerships: [], tenant: admin.tenant, version: 3
        el.set_public_user
        el.share_source
        el
      end
      let(:dashboard2) { create :dashboard, version: 3, owner: admin, title: 'MyLife' }
      let(:report_widget_full_column2) { FactoryBot.create :dashboard_widget, source: qr_full_column, dashboard: dashboard2 }
      let(:embed_payload) do
        {
          drillthroughs: {
            dashboard2.id => {},
          },
        }
      end
      let(:embed_token) do
        sk = embed_link.secret_key
        jwt_encode(sk, embed_payload, Time.now.to_i + 1000)
      end
      let(:should_open_dashboard) { false }
      let(:url) { "/embed/#{embed_link.hash_code}?_token=#{embed_token}" }
      before do
        create_filter(dashboard2, 'created_at', 'date')
        create_filter(dashboard2, 'name')
        create_filter(dashboard2, 'category')
        create_filter(dashboard2, 'price')
        create_filter(dashboard2, 'rating')
        report_widget_full_column2
      end

      it 'opens drillthrough modal' do
        visit(url)
        wait_for_dashboard_load
        wait_for_element_load('.highcharts-series .highcharts-point')

        trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[4], dashboard2.id)

        wait_for_element_load('.ci-drillthrough-modal .highcharts-series .highcharts-point')
        expect(page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point').size).to eq(3)
        expect(page.all('.ci-drillthrough-modal .ci-filter-label').map(&:text)).to eq(
          [
            "created_at\nmatches \"2020-01-01 until 2020-02-01\"",
            "name\nis \"choco\", \"chicken\"",
            "category\nany value",
            "price\nany value",
            "rating\nany value",
          ],
        )
      end
    end

    it 'opens drillthrough modal and applies correct filter values' do
      wait_for_dashboard_load
      wait_for_element_load('.highcharts-series .highcharts-point')

      trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[4], dashboard.id)

      wait_for_element_load('.ci-drillthrough-modal .highcharts-series .highcharts-point')
      expect(page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point').size).to eq(3)
      expect(page.all('.ci-drillthrough-modal .ci-filter-label').map(&:text)).to eq(
        [
          "created_at\nmatches \"2020-01-01 until 2020-02-01\"",
          "name\nis \"choco\", \"chicken\"",
          "category\nany value",
          "price\nany value",
          "rating\nany value",
        ],
      )

      page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point')[1].right_click
      page.find('.h-context-menu-content .ci-drillthrough').hover
      safe_click(".ci-drillthrough-#{dashboard.id}")
      wait_expect([
        "created_at\nmatches \"2020-01-01 until 2020-02-01\"",
        "name\nis \"choco\"",
        "category\nany value",
        "price\nany value",
        "rating\nany value",
      ]) do
        page.all('.ci-drillthrough-modal .ci-filter-label').map(&:text)
      end
    end

    context 'with sort' do
      let(:viz_setting_x_axis) do
        {
          type: 'text',
          format: { type: 'string', sub_type: 'string' },
          path_hash: { model_id: query_data_model.id, field_name: 'name' },
        }
      end
      let(:viz_setting_series) do
        {}
      end
      let(:viz_setting_y_axes) do
        [
          {
            label: 'Y Axis',
            columns: [
              {
                type: 'auto',
                color: 'auto',
                format: { type: 'number', sub_type: 'auto' },
                path_hash: { model_id: query_data_model.id, field_name: 'id' },
                aggregation: 'count',
              },
            ],
          },
        ]
      end
      let(:viz_setting_sort) do
        {
          option: {
            idx: 0,
            type: 'yaxis',
          },
          isAscending: false,
        }
      end

      it 'opens drillthrough modal and applies correct filter values' do
        wait_for_dashboard_load
        wait_for_element_load('.highcharts-series .highcharts-point')

        trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[0], dashboard.id)

        wait_for_element_load('.ci-drillthrough-modal .highcharts-series .highcharts-point')
        expect(page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point').size).to eq(1)
        expect(page.all('.ci-drillthrough-modal .ci-filter-label').map(&:text)).to eq(
          [
            "created_at\nany time",
            "name\nis \"beer\"",
            "category\nany value",
            "price\nany value",
            "rating\nany value",
          ],
        )
      end
    end
  end

  context 'pie chart' do
    let(:qr_pie) do
      FactoryBot.create :query_report, data_set_id: query_model_data_set.id, viz_setting: viz_setting_pie
    end
    let(:viz_setting_pie) do
      FactoryBot.create(
        :viz_setting,
        viz_type: 'pie_chart',
        fields: {
          series: {
            type: 'text',
            format: { type: 'string', sub_type: 'string' },
            path_hash: { model_id: query_data_model.id, field_name: 'name' },
          },
          y_axis: {
            label: 'Value',
            columns: [
              {
                type: 'auto',
                color: 'auto',
                format: { type: 'number', sub_type: 'auto' },
                path_hash: { model_id: query_data_model.id, field_name: 'price' },
                aggregation: 'sum',
              },
              {
                color: 'auto',
                format: { type: 'number', sub_type: 'auto' },
                path_hash: { model_id: query_data_model.id, field_name: 'rating' },
                aggregation: 'sum',
              },
            ],
          },
        },
        settings: {
          legend: { enabled: true, alignment: 'bottom' },
          donut_chart: { show_donut: false },
          other: { show_label: false, show_stack_total: false, show_percentage: false, group_long_tail: true, maximum_groups: 3 },
          'misc' => { 'pagination_size' => 25, 'show_row_number' => true },
          'sort' => nil,
          'aggregation' => { 'show_total' => false, 'show_average' => false },
          'conditional_formatting' => {},
        },
        format:
          { 'name' => { 'type' => 'string', 'index' => 0, 'sub_type' => 'auto' } },
      )
    end
    let!(:report_widget_pie) { FactoryBot.create :dashboard_widget, source: qr_pie, dashboard: dashboard }

    include_context 'dashboard 3.0 loaded'

    it 'opens drillthrough modal and applies correct filter values' do
      wait_for_dashboard_load
      wait_for_element_load('.highcharts-series .highcharts-point')

      trigger_drillthrough(page.all('.highcharts-series .highcharts-point')[3], dashboard.id)

      wait_for_element_load('.ci-drillthrough-modal .highcharts-series .highcharts-point')
      expect(page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point').size).to eq(4)
      expect(page.all('.ci-drillthrough-modal .ci-filter-label').map(&:text)).to eq(
        [
          "created_at\nany time",
          "name\nis \"choco\", \"chicken\"",
          "category\nany value",
          "price\nany value",
          "rating\nany value",
        ],
      )

      page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point')[1].right_click
      page.find('.h-context-menu-content .ci-drillthrough').hover
      sleep 0.5 # wait for popover loaded
      safe_click(".ci-drillthrough-#{dashboard.id}")

      wait_expect([
        "created_at\nany time",
        "name\nis \"choco\"",
        "category\nany value",
        "price\nany value",
        "rating\nany value",
      ]) do
        page.all('.ci-drillthrough-modal .ci-filter-label').map(&:text)
      end
      wait_expect(2) { page.all('.ci-drillthrough-modal .highcharts-series .highcharts-point').size }
    end
  end
end
