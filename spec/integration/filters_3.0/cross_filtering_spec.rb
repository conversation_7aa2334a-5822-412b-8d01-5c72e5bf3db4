require 'rails_helper'

describe 'Cross-filtering', js: true do

  shared_context 'dashboard 3.0 loaded' do
    before(:each) do
      Capybara.current_window.resize_to 1200, 1000
      safe_login(admin, url)
      wait_for_dashboard_load
    end
  end

  describe 'toggling cross filtering' do
    include_context 'cross_filtering_on_dynamic_dashboard_context'
    include_context 'dashboard 3.0 loaded'

    it 'Can toggle cross filtering in dashboard preferences modal' do
      safe_click '.ci-preferences-toggle'
      safe_click '.ci-preference'

      cf_tab = page.find('a', text: 'Cross-filtering')
      cf_tab.click

      wait_for_element_load '.ci-cross-filtering-settings'
    end
  end

  describe 'doing cross filtering' do
    include_context 'cross_filtering_on_dynamic_dashboard_context'
    include_context 'dashboard 3.0 loaded'

    it 'shows cf indicator on dashboard' do
      wait_expect(1) {
        page.all('.ci-dashboard-cf-indicator').count
      }
    end

    it 'can cross filter on column chart' do
      widget = page.find('.ci-report-widget', text: /Column Chart/)
      wait_for_element_load '#widget-1 .widget-viz-container .highcharts-container .highcharts-series-group .highcharts-series-0'
      wait_for_element_load '#widget-1 .highcharts-point.highcharts-color-0'
      column = widget.first('.highcharts-point.highcharts-color-0')
      column.hover # to remove the cover
      column.click

      wait_for_widget_load

      wait_expect(2) {
        page.all('.ci-cross-filter-label').count
      }
      sleep 1
      # check for highlighted points
      wait_expect([1, 0]) do
        [
          widget.all('.highcharts-point[fill="#479980"]').size + widget.all('.highcharts-point[fill="rgb(96,178,153)"]').size,
          widget.all('.highcharts-point[fill="#fcc870"]').size,
        ]
      end
      # check for data & icon color
      table_widget = page.find('.ci-report-widget', text: /Data Table/)

      funnel_icon = table_widget.find('.ci-widget-conditions-trigger')
      expect(funnel_icon[:class].include?('text-gray-700')).to be_truthy

      wait_expect(['Jan 2020', 'beer']) {
        table_widget.all('.ag-cell').map {|td| td.text}
      }
    end

    it 'can show indicator when doing cross filtering on multiple widgets' do
      column_widget = page.find('.ci-report-widget', text: /Column Chart/)
      pie_widget = page.find('.ci-report-widget', text: /Pie Chart/)

      # cross filter on column chart
      column = column_widget.first('.highcharts-point.highcharts-color-0')
      column.hover # to remove the cover
      column.click

      wait_for_widget_load
      wait_expect([2, 4]) {
        [
          page.all('.ci-cross-filter-label').count,
          pie_widget.all('.highcharts-point').count
        ]
      }

      # cross filter on pie chart
      pie = pie_widget.first('.highcharts-point')
      pie.hover # to remove the cover
      pie.click

      wait_for_widget_load
      wait_expect([1, 8, 1]) {
        [
          page.all('.ci-cross-filter-label').count,
          pie_widget.all('.highcharts-point').count,
          pie_widget.all('.highcharts-point.highcharts-point-select').count
        ]
      }
    end

    it 'wont trigger cross-filtering when the widget is expanded' do
      column_widget = page.find('.ci-report-widget', text: /Column Chart/)
      column_widget.hover
      column_widget.first('.ci-expand-widget').click

      wait_for_widget_load

      # try clicking on the chart
      widget = page.find('.ci-report-widget')
      column = widget.first('.highcharts-point.highcharts-color-0')
      column.hover # to remove the cover
      column.click

      # CF not triggered
      wait_expect(0) { page.all('.ci-cross-filter-label', wait: false).count }
    end
  end

  describe 'loading states with cross filtering' do
    include_context 'cross_filtering_on_dynamic_dashboard_context'
    include_context 'dashboard 3.0 loaded'

    it 'show correct loading with pending state' do
      wait_for_all_ajax_requests

      ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '1'
      column_widget = page.find('.ci-report-widget', text: /Column Chart/)
      pie_widget = page.find('.ci-report-widget', text: /Pie Chart/)

      # cross filter on column chart
      column = column_widget.first('.highcharts-point.highcharts-color-0')
      column.hover # to remove the cover
      column.click

      wait_for_element_load('.v-loading-border')
      wait_for_element_load('.job-queue-status__display-text')
      wait_for { page.first('.job-queue-status__display-text').text.include?('Pending') } # check pending text
      page.first('.job-queue-status__display-text').hover
      page.first('.job-queue-status').text.include?('report queue') # check pending message

      # # increase loading time to check loading steps
      allow_any_instance_of(Connectors::PostgresConnector).to receive(:exec_sql) do
        sleep 2
        [[], []]
      end

      t = Thread.new do
        Job.last.send_to_workers
      end

      wait_for { page.first('.v-loading-progress').text.include?('%') } # check loading status

      t.join
      wait_for_widget_load # widgets loaded

      expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 5)
    end

    it 'show correct loading without pending state' do
      wait_for_all_ajax_requests

      column_widget = page.find('.ci-report-widget', text: /Column Chart/)
      pie_widget = page.find('.ci-report-widget', text: /Pie Chart/)

      # increase loading time to check loading steps
      allow_any_instance_of(Connectors::PostgresConnector).to receive(:exec_sql) do
        sleep 3
        [[], []]
      end

      # cross filter on column chart
      column = column_widget.first('.highcharts-point.highcharts-color-0')
      column.hover # to remove the cover
      column.click

      wait_for_element_load('.v-loading-text')
      wait_for { page.first('.v-loading-text').text.include?('Cross-filter') } # check loading text
      wait_for { page.first('.v-loading-progress').text.include?('%') } # check progress loading step
    end
  end
end
