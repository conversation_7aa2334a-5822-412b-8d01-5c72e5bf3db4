# typed: false
require 'rails_helper'

describe 'manage color palettes', js: true, stable: true do
  let(:admin) { get_test_admin }

  EXISTING_COLORS = 4
  let(:colors) do
    [
      { code: '#FFFF00', series_label: 'banana' },
      { code: '#1EFFBB', series_label: 'aplle' },
      { code: '#FFA500', series_label: 'orange' },
      { code: '#800080', series_label: 'grape' },
    ]
  end
  let(:result_colors) do
    [
      { code: '#FFFF00FF', series_label: 'banana' },
      { code: '#1EFFBBFF', series_label: 'aplle' },
      { code: '#FFA500FF', series_label: 'orange' },
      { code: '#800080FF', series_label: 'grape' },
    ]
  end
  let(:palette_name) { 'Fruits' }

  def create_color_palette
    qlogin(admin, '/manage/settings#manage-colors')
    safe_click('.ci-new-palette-btn')
    expect_notifier_content(/palette created/)
    expect(ColorPalette.last).not_to be nil

    # set palette_name
    wait_for_element_load '.ci-palette-title'
    page.find('.ci-palette-title').set(palette_name)

    # delete old colors
    EXISTING_COLORS.times do |_i|
      page.find('.ci-delete-color-0').click
    end

    # update new colors
    colors.each_with_index do |item, index|
      safe_click('.ci-new-color-btn')
      until page.find('.hui-popover-floating .vc-input__input').value == item[:code]
        page.find('.hui-popover-floating .vc-input__input').set(item[:code])
      end
      page.find('.hui-popover-floating').find('.ci-picker-add-color').click
      page.find(".ci-color-label-#{index}").set(item[:series_label])
      color_el = page.find(".ci-color-#{index}")
      color_el.find('.ci-color-picker-trigger').click
      page.find('.hui-popover-floating').find('.ci-picker-add-color').click
      sleep 0.5
    end

    # save
    page.find('.ci-save-palette-btn').click
    expect_notifier_content(/palette saved/)
  end

  context 'setting color palette in color management' do
    it 'color picker should show current selected color' do
      create_color_palette

      # check that the palette is saved
      palette = ColorPalette.last
      expect(palette.colors).to eq result_colors
      expect(palette.name).to eq palette_name

      safe_click('.list-group-item:last-child')

      wait_for_element_load('.ci-color-picker-trigger')
      page.all('.ci-color-picker-trigger').each_with_index do |trigger, idx|
        trigger.click
        wait_for_element_load('.color-picker-base')
        wait_expect(colors[idx][:code]) { page.find('.color-picker-base .vc-input__input').value }
      end
    end

    it 'creates and update color palette' do
      create_color_palette

      # check that the palette is saved
      palette = ColorPalette.last
      expect(palette.colors).to eq result_colors
      expect(palette.name).to eq palette_name

      # delete palette softly
      wait_and_click('.ci-new-palette-btn')
      wait_expect(true) { ColorPalette.last.deleted_at.nil? }

      wait_and_click('.ci-delete-palette-btn-10')
      wait_and_click('.ci-confirm')
      wait_expect(false) { ColorPalette.last.deleted_at.nil? }
    end
  end

  context 'apply color palette in viz setting' do
    include_context 'dynamic_dashboard'

    let!(:ds_report2) { create :products_modeling_report, owner: admin, data_set: data_set }
    let!(:dashboard_widget2) do
      FactoryBot.create :dashboard_widget, title: 'Widget 2', source: ds_report2, dashboard: dashboard
    end
    let!(:mapping) do
      field_path = DataModeling::Values::FieldPath.new(field_name: 'name', model_id: products_model.id)
      FactoryBot.create :dynamic_filter_mapping, viz_conditionable: dashboard_widget2, dynamic_filter: name_filter,
                                                 field_path: field_path
    end

    it 'apply color palette correctly' do
      create_color_palette # create new color palette

      visit "/queries/#{ds_report2.id}/edit_model"
      safe_click('.ci-explorer-control-get-results')
      wait_for_element_load('.highcharts-series')
      page.all('.viz-section:nth-child(1) .ci-x-axis-row .h-icon[data-icon="cancel"]').last.click
      # set legend
      select_h_select_option('.viz-section:nth-child(1) .ci-x-axis-row', label: 'Created at')
      select_h_select_option('.viz-section:nth-child(2) .ci-x-axis-row', label: 'Name')
      page.find('.ci-explorer-control-get-results').click
      wait_expect(3) { page.all('.color-picker').count }

      # set color palette
      page.first('.color-picker').click
      # increase popover height for selecting the lattest palettes
      page.execute_script("document.querySelector('.color-palettes').style['max-height'] = '500px'")
      page.all('.colors').last.hover
      page.find('.palettes-wrapper').all('.palette').last.find('.hui-radio-el').click # apply the new color palette

      safe_click('.ci-save-ds-based-report')
      expect_notifier_content(/success/)

      visit "dashboards/#{dashboard.id}"
      wait_expect(false, 10) do
        page.has_css?('.grid-item:last-child .ci-viz-result .v-loading-wrapper')
      end
      wait_for_element_load('.highcharts-series')

      # color applied correctly
      expect(page.first('.highcharts-series-0 rect').style('fill')['fill']).to eq 'rgb(255, 255, 0)' # bread
      expect(page.first('.highcharts-series-1 rect').style('fill')['fill']).to eq 'rgb(30, 255, 187)' # egg
      expect(page.first('.highcharts-series-2 rect').style('fill')['fill']).to eq 'rgb(255, 165, 0)' # milk

      # apply filter
      safe_click('.ci-filter-label')
      h_select_dropdown('.ci-value-select', value: 'egg')
      h_select_dropdown('.ci-value-select', value: 'milk')
      page.find('.ci-submit').click
      sleep 0.5

      # refresh cache
      page.find('.refresh').click
      wait_expect(false, 10) do
        page.has_css?('.grid-item:last-child .ci-viz-result .v-loading-wrapper')
      end
      wait_for_element_load('.highcharts-series')

      # correct color after filtered
      expect(page.first('.highcharts-series-0 rect').style('fill')['fill']).to eq 'rgb(30, 255, 187)' # egg
      expect(page.first('.highcharts-series-1 rect').style('fill')['fill']).to eq 'rgb(255, 165, 0)' # milk
    end
  end
end
