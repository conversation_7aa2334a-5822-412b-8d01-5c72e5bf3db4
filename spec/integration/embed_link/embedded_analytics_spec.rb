# frozen_string_literal: true

# typed: false

require 'rails_helper'

describe 'Embed link in dashboard page', :js do
  include_context 'dynamic_dashboard'
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CONVERSION, true)
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
  end

  let!(:tsub) { create(:tenant_subscription, embed_workers: 3, tenant: admin.tenant) }
  let(:url) { dashboard_path(dashboard) }

  def open_preferences_modal
    safe_click('.ci-preferences-toggle')
    safe_click('.ci-preference')
    safe_click('.tab-link.ci-embedded-analytics')
  end

  def generate_embed_link
    safe_click('.ci-new-embed-link')
  end

  def open_preview_modal
    safe_click('.ci-preview')
  end

  def run_preview
    safe_click('.ci-run-embed-preview')
  end

  def select(parent_selector, option_selector, global: false)
    safe_click "#{parent_selector} .h-select__container-input"
    options_selector = global ? "#_h_poppers_container_ .h-select__panel .h-select__options #{option_selector}" : "#{parent_selector} .h-select__panel .h-select__options #{option_selector}"
    safe_click(option_selector)
  end

  def expect_result
    page.should have_css '.embedded-dashboard-iframe'
    within_frame(page.find('iframe.embedded-dashboard-iframe')) do
      wait_for_all_ajax_requests
      wait_for_element_load '.h-dashboard-body'
      wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
      wait_for_element_load '.ci-table-report-data'
      table = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
      expect(table.count).to eq 1
      expect(table[0]).to include('milk')
    end
  end

  shared_examples 'embed_dashboard_3.0' do
    it 'applies filter values' do
      qlogin(admin, url)
      name_filter_definition[:default_condition] = DataModeling::Values::Condition.new(
        operator: 'is',
        values: ['milk'],
        modifier: nil,
      )
      name_filter_definition.save!

      open_preferences_modal
      generate_embed_link
      open_preview_modal
      run_preview
      expect_result
    end

    it 'applies permission values' do
      qlogin(admin, url)
      open_preferences_modal
      generate_embed_link
      open_preview_modal

      safe_click '.ci-add-permission'
      select_h_select_option('.ci-data-set-options', value: data_set.id)
      select_h_select_option('.ci-data-model-field-options', value: "#{products_model.id}$!name")

      safe_click '.ci-data-set-model-field-select-button'

      safe_click('.permissions-display div:first-child .ci-value-select input')
      wait_expect(false, 10) do
        page.has_css?('.filter-display .filter:first-child .viz-filter-value-input .h-select .h-select__search-info')
      end
      page.first('.permissions-display div:first-child .ci-value-select input').send_keys('m')
      wait_expect(false, 10) do
        page.has_css?('.filter-display .filter:first-child .viz-filter-value-input .h-select .h-select__search-info')
      end
      wait_expect(['milk', "+\nAdd option m"]) do
        page.all('.h-select-popover .h-select-panel .h-select__options-item').map(&:text)
      end
      page.first('.permissions-display div:first-child .ci-value-select input').send_keys(:enter)

      run_preview
      expect_result
    end

    it 'hide filter' do
      qlogin(admin, url)
      open_preferences_modal
      generate_embed_link
      open_preview_modal
      select('.filter-display .filter:first-child', 'div[data-value="bread"]', global: true)
      safe_click('.filter-hidden-checkbox')
      run_preview

      within_frame find('iframe.embedded-dashboard-iframe') do
        wait_for_element_load '.h-dashboard-body'
        wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
        wait_for_element_load '.ci-table-report-data'
        table = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
        expect(table.count).to eq 2
        expect(table[0]).not_to include('egg')
        expect(table[0]).not_to include('milk')
      end
    end

    it 'when expanding widget' do
      qlogin(admin, url)
      open_preferences_modal
      generate_embed_link
      open_preview_modal

      run_preview

      within_frame find('iframe.embedded-dashboard-iframe') do
        wait_for_element_load '.h-dashboard-body'
        wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
        safe_click '.h-report-widget .ci-expand-widget'
        wait_for_element_load '.ci-table-report-data'
        table = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
        expect(table).to contain_exactly('1 bread 2.25 available', '2 milk 3 available', '3 egg 5 available',
                                        '4 bread 2.25 expired',)
      end
    end

    context 'follow feature toggle embed_link:allow_public_user_bust_cache' do
      it 'show when on' do
        FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', true)
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          expect(page).to have_css('.h-dashboard-header .cache-status div')
        end
      end

      it 'show cache popover without cache setting button when on' do
        FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', true)
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          wait_for_element_load('.h-dashboard-header')
          page.first('.cache-status.refresh').click
          expect(page).to have_css('.last-cache.detail')
          expect(page).to have_no_css('[data-ci="ci-cache-setting-btn"]')
        end
      end

      it 'hide when off' do
        FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', false)
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          expect(page).to have_no_css('.h-dashboard-header .cache-status div')
        end
      end
    end

    context 'auto rotate embed token when it nearly to expire' do
      it 'not raise token expired' do
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          wait_for_element_load '.h-dashboard-body'
          wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
          sleep 3 # to make sure the init token exp is over
          safe_click '.h-report-widget .ci-expand-widget'
          wait_for_element_load '.ci-table-report-data'
          table = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
          expect(table).to contain_exactly('1 bread 2.25 available', '2 milk 3 available', '3 egg 5 available',
                                          '4 bread 2.25 expired',)
        end
      end
    end

    context 'with timezone settings' do
      include_context 'timezone_dynamic_dashboard'
      let(:url) { dashboard_path(timezone_dashboard) }

      it 'no default timezone, use dashboard timezone' do
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          wait_for_element_load '.h-dashboard-body'
          wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
          safe_click '.h-report-widget .ci-expand-widget'
          wait_for_element_load '.ci-table-report-data'
          rows = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
          expect(rows).to contain_exactly('10 2021-10-20T23:30:00.000+00:00 2021-10-20',
                                          '20 2021-10-21T06:00:00.000+00:00 2021-10-20', '30 2021-10-21T19:00:00.000+00:00 2021-10-21',)
        end
      end

      it 'change default timezone in embed config, use embed default timezone' do
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        search_h_select('.ci-default-timezone', text: 'Asia/Tokyo')
        select_h_select_option('.ci-default-timezone', value: 'Asia/Tokyo')
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          wait_for_element_load '.h-dashboard-body'
          wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
          safe_click '.h-report-widget .ci-expand-widget'
          wait_for_element_load '.ci-table-report-data'
          rows = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
          expect(rows).to contain_exactly('10 2021-10-21T01:30:00.000+00:00 2021-10-20',
                                          '20 2021-10-21T08:00:00.000+00:00 2021-10-20', '30 2021-10-21T21:00:00.000+00:00 2021-10-21',)
        end
      end

      it 'allow to change timezone' do
        qlogin(admin, url)
        open_preferences_modal
        generate_embed_link
        open_preview_modal
        safe_click('.ci-allow-to-change-timezone')
        run_preview
        within_frame find('iframe.embedded-dashboard-iframe') do
          wait_for_element_load '.h-dashboard-body'
          wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
          search_h_select('.ci-dashboard-timezone', text: 'Asia/Tokyo')
          select_h_select_option('.ci-dashboard-timezone', value: 'Asia/Tokyo')
          wait_for_element_load '.h-dashboard-body'
          wait_for_element_load '.h-dashboard-widget:first-child .result-viz'
          safe_click '.h-report-widget .ci-expand-widget'
          wait_for_element_load '.ci-table-report-data'
          rows = page.all('.ag-row').map{ |row| row.all('.ag-cell').map(&:text).join(' ') }.flatten
          expect(rows).to contain_exactly('10 2021-10-21T01:30:00.000+00:00 2021-10-20',
                                          '20 2021-10-21T08:00:00.000+00:00 2021-10-20', '30 2021-10-21T21:00:00.000+00:00 2021-10-21',)
        end
      end
    end
  end

  context 'with portal_configs_for_dashboard off' do
    before do
      FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
    end

    it_behaves_like 'embed_dashboard_3.0'
  end

  context 'with portal_configs_for_dashboard on' do
    before do
      FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
    end

    it_behaves_like 'embed_dashboard_3.0'
  end
end
