# typed: false
require 'rails_helper'

describe 'Filter label', js: true, legacy: true do
  let(:dashboard) { FactoryBot.create :dashboard }
  let(:admin) { get_test_admin }
  let(:filter) { FactoryBot.create :text_sf, name: 'custom', settings: {label: "filter test label", type: 'input'}}

  def show_filter_dropdown
    wait_for_element_load('.add-filter-button')
    safe_click('.add-filter-button')
  end

  def open_new_filter_modal
    wait_for_element_load('.ci-create-sf')
    safe_click('.ci-create-sf')
    wait_for_element_load('.ci-modal-edit-filter')
  end

  before do
    filter.save!
  end

  after do
    clear_page_unload_events
  end

  describe 'Shared filter' do
    before do
      safe_login(admin, shared_filters_path)
    end

    it 'filter name label should show the same value with name-label field when submiting filter preview' do
      open_new_filter_modal
      select_h_select_option('.ci-select-item', value: 'date')
      fill_text('.ci-sf-name', 'date_1')
      fill_text('.ci-sf-label-name', 'date label test')
      safe_click('.ci-sf-preview')
      wait_expect('date label test') { page.find('.filter-component .field-unit .title').text }
    end

    it 'after create filter the label should show right value' do
      open_new_filter_modal
      select_h_select_option('.ci-select-item', value: 'date')
      fill_text('.ci-sf-name', 'date_2')
      fill_text('.ci-sf-label-name', 'date label test')
      safe_click('.ci-shared-filter-save')
      expect_notifier_content('filter template added successfully')
      safe_click('.ci-actions', { index: 1 })
      page.find('.hui-dropdown-floating .ci-edit').click
      wait_expect('date label test') { page.find('.ci-sf-label-name').value }
    end

    it 'after update filter the label should show right value' do
      wait_for_element_load('.ci-actions')
      page.all('.ci-actions')[0].click
      page.find('.hui-dropdown-floating .ci-edit').click
      fill_text('.ci-sf-label-name', 'update label filter')
      safe_click('.ci-shared-filter-save')
      wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
      page.all('.ci-actions')[0].click
      page.find('.hui-dropdown-floating .ci-edit').click
      wait_expect('update label filter') { page.find('.ci-sf-label-name').value }
    end
   end

  describe 'dashboard' do
    before do
      safe_login(admin, dashboard_path(dashboard))
    end

    it 'filter name label should show the same value with name-label field when submiting filter preview' do
      # Add first filter in dropdown list
      show_filter_dropdown
      page.find('.hui-dropdown-floating .ci-filter-type-0').click
      # Fill label & click preview
      fill_text('.ci-sf-label-name', 'date label test')
      safe_click('.ci-sf-preview')
      wait_for_element_load('.filter-component')
      # check label
      wait_expect('date label test') { page.find('.filter-component .field-unit .title').text }
    end

    it 'filter label should show right value when adding filter to dashboard' do
      # Select first filter template
      show_filter_dropdown
      page.find('.hui-dropdown-floating .ci-filter-templates').hover
      page.find('.items-list div', match: :first).click
      wait_expect('filter test label') { page.find('.filter-value div.ci-filter b').text }
    end
  end

  describe 'add filter to report' do
    before do
      safe_login(admin, new_query_report_path)
    end

    it 'filter label should show right value when adding filter to dashboard' do
      # Add filter template
      show_filter_dropdown
      page.find('.hui-dropdown-floating .ci-filter-templates').hover
      page.find('.items-list div', match: :first).click
      wait_expect('filter test label') { page.find('.filter-value b').text }
    end
  end
end
