# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Dropdown Filter', js: true, legacy: true do
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }
  let(:filter_sql) do
    "values ('vnd','Viet Nam Dong'),
            ('\"usd\"','US Dollar'),
            ('thb','Baht'),
            ('sgd','Singapore Dollar')"
  end
  let(:settings) do
    {
      type: 'dropdown',
      hide_all: false,
      permissions: '',
      data_source_id: ds.id,
      date_max_ds_id: ds.id,
      date_min_ds_id: ds.id,
      dropdown_source: 'sql',
      parent_filter_id: nil,
      dropdown_multiselect: false,
      dropdown_sql: filter_sql
    }
  end
  let(:multiple_filter) do
    settings[:dropdown_multiselect] = true
    FactoryBot.create :dropdown_shared_filter, name: 'multi', settings: settings
  end
  let(:single_filter) do
    settings[:dropdown_multiselect] = false
    FactoryBot.create :dropdown_shared_filter, name: 'single', settings: settings
  end
  let(:lazy_filter) do
    settings[:dropdown_multiselect] = false
    settings[:dropdown_lazy_loading] = true
    FactoryBot.create :dropdown_shared_filter, name: 'lazy', settings: settings
  end

  describe 'dropdown: select option' do
    shared_examples 'selecting option' do
      it 'correctly select option' do
        report = FactoryBot.create :query_report,
                                    query: "select * from ( #{filter_sql} ) R where [[column1 IN ({{#{test_filter.name}}})]]",
                                    data_source: ds
        FactoryBot.create :filter_ownership, filterable: report, shared_filter: test_filter, var_name: test_filter.name

        path = query_report_path report
        qlogin(admin, path)
        wait_for_report_load
        wait_for_element_load '.filter-editable'

        input_name = test_filter.name
        selected_selector = '.select2-selection__placeholder'
        option_selector = '.select2-results li'
        if test_filter.settings[:dropdown_multiselect]
          selected_selector = '.dropdown-filter__text'
          option_selector = '.dropdown-filter__option'
          input_name += '[]'
        end

        # test placeholder or selected
        expect(page.first(selected_selector).text.gsub(/\s+/, ' ')).to eq expected_selected_text

        # test options
        page.first('.filter-editable').click
        expect(page.all(option_selector).map(&:text)).to eq(expected_options)
        sleep 1
        expect(page.all('.ci-table-report-data .ag-row > .ag-cell:nth-child(1)').map(&:text)).to eq(expected_codes)

        # select one option and submit
        if test_filter.settings[:dropdown_multiselect]
          page.all(option_selector)[0].click
          page.all(option_selector)[1].click
          page.first('.filter-editable').click
        elsif test_filter.settings[:dropdown_lazy_loading]
          page.first('.select2-search__field').set 'do'
          sleep 1.5
          expect(page.all(option_selector).map(&:text)).to eq(['Viet Nam Dong', 'US Dollar', 'Singapore Dollar'])
          page.first(option_selector).click
        else
          page.first(option_selector).click
        end
        safe_click('.panel-filters .ci-submit-filters-btn')
        sleep 0.5

        wait_expect(expected_codes_after) do
          sleep 1
          page.all('.ci-table-report-data .ag-row > .ag-cell:nth-child(1)').map(&:text)
        end

        # test hidden input
        expect(page.all("input[name='#{input_name}']", visible: false).map(&:value)).to eq expected_codes_after
      end
    end

    describe 'Multiple select: select option' do
      let(:test_filter) { multiple_filter }
      let(:expected_selected_text) { 'All' }
      let(:expected_options) { ['Viet Nam Dong', 'US Dollar', 'Baht', 'Singapore Dollar'] }
      let(:expected_codes) { ['vnd', '"usd"', 'thb', 'sgd'] }
      let(:expected_codes_after) { ['vnd', '"usd"'] }
      it_behaves_like 'selecting option'
    end

    describe 'Single select: select option' do
      let(:test_filter) { single_filter }
      let(:expected_selected_text) { 'All' }
      let(:expected_options) { ['Viet Nam Dong', 'US Dollar', 'Baht', 'Singapore Dollar'] }
      let(:expected_codes) { ['vnd', '"usd"', 'thb', 'sgd'] }
      let(:expected_codes_after) { ['vnd'] }
      it_behaves_like 'selecting option'
    end

    describe 'Lazy select: select option' do
      let(:test_filter) { lazy_filter }
      let(:expected_selected_text) { 'All' }
      let(:expected_options) { ['Please enter 2 or more characters'] }
      let(:expected_codes) { ['vnd', '"usd"', 'thb', 'sgd'] }
      let(:expected_codes_after) { ['vnd'] }
      it_behaves_like 'selecting option'
    end
  end

  describe 'dropdown: set default value' do
    shared_examples 'set default value' do
      it 'correctly set default value' do
        test_filter.settings[:default] = selected_value
        test_filter.save
        report = FactoryBot.create :query_report,
                                    query: "select * from ( #{filter_sql} ) R where [[column1 IN ({{#{test_filter.name}}})]]",
                                    data_source: ds
        FactoryBot.create :filter_ownership, filterable: report, shared_filter: test_filter, var_name: test_filter.name

        path = query_report_path report
        qlogin(admin, path)
        wait_for_report_load
        wait_for_element_load '.filter-editable'

        input_name = test_filter.name
        selected_selector = placeholder ? '.select2-selection__placeholder' : '.select2-selection__rendered'
        selected_option_selector = '.select2-results li[aria-selected=true]'
        if test_filter.settings[:dropdown_multiselect]
          selected_selector = '.dropdown-filter__text'
          selected_option_selector = '.dropdown-filter__option.selected'
          input_name += '[]'
        end

        # test hidden input
        hidden_values = placeholder === true ? expected_hidden_values : expected_codes
        expect(page.all("input[name='#{input_name}']", visible: false).map(&:value)).to eq hidden_values

        # test placeholder or selected
        expect(page.first(selected_selector).text(normalize_ws: true)).to eq expected_selected_text

        # test options
        page.first('.filter-editable').click
        expect(page.all(selected_option_selector).map(&:text)).to eq(expected_selected_options)
        sleep 1
        expect(page.all('.ci-table-report-data .ag-row > .ag-cell:nth-child(1)').map(&:text)).to eq(expected_codes)
      end
    end

    describe 'Multiple select: set default value' do
      let(:placeholder) { false }
      let(:test_filter) { multiple_filter }
      let(:selected_value) { 'vnd,foo,"usd",bar' }
      let(:expected_selected_text) { '2 selected' }
      let(:expected_selected_options) { ['Viet Nam Dong', 'US Dollar'] }
      let(:expected_codes) { ['vnd', '"usd"'] }
      it_behaves_like 'set default value'

      describe 'default all value' do
        let(:placeholder) { true }
        let(:expected_hidden_values) { [] }
        let(:expected_codes) { ['vnd', '"usd"', 'thb', 'sgd'] }
        let(:expected_selected_text) { 'All' }
        let(:expected_selected_options) { [] }

        # test with empty value
        let(:selected_value) { '' }
        it_behaves_like 'set default value'

        # test with _all value
        let(:selected_value) { '_all' }
        it_behaves_like 'set default value'
      end
    end

    describe 'Single select: set default value' do
      let(:placeholder) { false }
      let(:test_filter) { single_filter }
      let(:selected_value) { '"usd"' }
      let(:expected_selected_text) { "× US Dollar" }
      let(:expected_selected_options) { ['US Dollar'] }
      let(:expected_codes) { ['"usd"'] }

      it_behaves_like 'set default value'

      describe 'default all value' do
        let(:placeholder) { true }
        let(:expected_hidden_values) { ['_all'] }
        let(:expected_codes) { ['vnd', '"usd"', 'thb', 'sgd'] }
        let(:expected_selected_text) { 'All' }
        let(:expected_selected_options) { [] }

        # test with empty value
        let(:selected_value) { '' }
        it_behaves_like 'set default value'

        # test with _all value
        let(:selected_value) { '_all' }
        it_behaves_like 'set default value'
      end
    end

    describe 'Lazy select: set default value' do
      let(:placeholder) { false }
      let(:test_filter) { lazy_filter }
      let(:selected_value) { '"usd"' }
      let(:expected_selected_text) { "× US Dollar" }
      let(:expected_selected_options) { [] }
      let(:expected_codes) { ['"usd"'] }
      it_behaves_like 'set default value'

      describe 'default all value' do
        let(:placeholder) { true }
        let(:expected_hidden_values) { ['_all'] }
        let(:expected_codes) { ['vnd', '"usd"', 'thb', 'sgd'] }
        let(:expected_selected_text) { 'All' }
        let(:expected_selected_options) { [] }

        # test with empty value
        let(:selected_value) { '' }
        it_behaves_like 'set default value'

        # test with _all value
        let(:selected_value) { '_all' }
        it_behaves_like 'set default value'
      end
    end
  end
end
