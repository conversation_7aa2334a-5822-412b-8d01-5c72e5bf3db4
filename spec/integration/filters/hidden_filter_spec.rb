# typed: false
require 'rails_helper'

describe 'Hidden Filter', js: true, legacy: true do
  let (:admin) {get_test_admin}
  let (:analyst) {get_test_analyst}
  let (:user) {get_test_user}

  let (:filter) {
    FactoryBot.create :shared_filter, name: 'Input'
  }

  let (:filter2) {
    FactoryBot.create :shared_filter, name: 'Input2'
  }

  let (:fo) {
    FactoryBot.create :filter_ownership, filterable: source, shared_filter: filter, var_name: 'test', order: 0
  }
  
  let (:fo2) {
    FactoryBot.create :filter_ownership, filterable: source, shared_filter: filter2, var_name: 'test2', order: 2
  }

  let (:report) {
    FactoryBot.create :query_report, title: 'Report', query: 'select 1;'
  }

  let (:dashboard) {
    FactoryBot.create :dashboard
  }

  let (:shareable_link) {
    sl = FactoryBot.create :shareable_link, resource: dashboard, owner_id: admin.id
    sl.set_public_user
    sl.share_resource
    "#{dashboard_path dashboard}?_pl=#{sl.hash_code}"
  }
  let (:embed_link) {
    identifier_variable_filter = FactoryBot.create :filter_ownership, var_name: 'customer_key', filterable: dashboard, shared_filter: filter
    el = FactoryBot.create :embed_link, source: dashboard
    el.add_many(:filter_ownership, :embed_link_identifier_variable, identifier_variable_filter)
    el.set_public_user
    el.share_source
    token = jwt_encode(el.secret_key, { identifier_variable_filter.var_name => 123 }, Time.now.to_i + 24*60*60)
    "/embed/#{el.hash_code}?_token=#{token}"
  }

  let (:source) {dashboard}
  let (:source_path) {dashboard_path dashboard}

  before do
    FactoryBot.create :tenant_subscription, tenant: admin.tenant, status: 'active'
  end

  def test_visibility (role, filter_count = 1)
    page.reset!
    qlogin(role, source_path)
    if source_path.include? 'queries'
      wait_for_element_load '#filters-form'
    else
      # filter list will be hidden if there is no filter
      wait_for_element_load '.filter-value'
      safe_click('.filter-header')
    end
    wait_for_element_load '.ci-report-filter' if filter_count > 0
    wait_expect(filter_count) do
      page.all('.ci-report-filter').count
    end
  end

  def test_share_link(link, filter_count)
    visit link
    wait_for_element_load '.filter-value'
    safe_click('.dashboard-header-filters')
    wait_expect(filter_count) do
      page.all('.ci-report-filter').count
    end
  end

  shared_examples 'hiding filter' do
    it 'hide the filter on business user' do
      fo.is_hidden = true
      fo.save!
      fo2.is_hidden = false
      fo2.save!
      # share resource
      admin.share(analyst, :read, source)
      admin.share(analyst, :read, get_test_ds)
      admin.share(user, :read, source)

      # test visibility
      test_visibility(user, 1)
      test_visibility(admin, 2)
      test_visibility(analyst, 2)

      fo_updated_at = fo.reload.updated_at
      fo2_updated_at = fo2.reload.updated_at
      # uncheck hidden option
      page.first('.ci-report-filter').hover
      safe_click('.ci-filter-controls')
      safe_click('.ci-edit-default')
      wait_for_element_load '.ci-filter-visibility'
      safe_click('.ci-filter-visibility')
      safe_click('.ci-modal-save-btn')
      wait_for do
        fo.reload.updated_at != fo_updated_at || fo2.reload.updated_at != fo2_updated_at
      end
      test_visibility user, 2
    end
  end

  describe 'Hiding filter in dashboard show' do
    it_behaves_like 'hiding filter'
  end

  describe 'Hiding filter in report show' do
    let (:source) {report}
    let (:source_path) {query_report_path report}

    it_behaves_like 'hiding filter'
  end

  describe 'Hiding filter in shareable link' do
    it 'work correctly' do
      # turn on
      fo.is_hidden = true
      fo.save!
      fo2.is_hidden = false
      fo2.save!
      test_share_link(shareable_link, 1)

      # turn off
      fo.is_hidden = false
      fo.save!
      fo2.is_hidden = false
      fo2.save!
      test_share_link(shareable_link, 2)
    end
  end

  describe 'Hiding filter in embed link' do
    it 'work correctly' do
      filter3 = FactoryBot.create :shared_filter, name: 'Input'
      fo3 = FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: filter3, var_name: 'haha'
      filter4 = FactoryBot.create :shared_filter, name: 'Input1'
      fo4 = FactoryBot.create :filter_ownership, filterable: dashboard, shared_filter: filter4, var_name: 'haha1'

      # turn on
      fo3.is_hidden = true
      fo3.save!
      test_share_link(embed_link, 1)

      # turn off
      fo3.is_hidden = false
      fo3.save!
      test_share_link(embed_link, 2)
    end
  end

end
