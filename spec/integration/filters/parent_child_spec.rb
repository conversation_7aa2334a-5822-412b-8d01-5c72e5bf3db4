# typed: false

require 'rails_helper'

describe 'Parent Child Filter', js: true, legacy: true do
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }
  let(:settings) do
    {
      type: 'dropdown',
      hide_all: false,
      permissions: '',
      data_source_id: ds.id,
      date_max_ds_id: ds.id,
      date_min_ds_id: ds.id,
      dropdown_source: 'sql',
      parent_filter_id: nil,
      dropdown_multiselect: false,
      dropdown_sql: '',
    }
  end
  let(:grandparent_filter) do
    settings[:dropdown_sql] = <<-SQL
      values
        ('as','Asia'),
        ('"eu"','Europe'),
        ('af','Africa'),
        ('am','America'),
        ('au','Australia')
    SQL
    FactoryBot.create :dropdown_shared_filter, name: 'grandparent', settings: settings
  end
  let(:parent_filter) do
    settings[:parent_filter_id] = grandparent_filter.id
    settings[:is_child_filter] = true
    settings[:dropdown_sql] = <<-SQL.strip_heredoc
      values
        ('vnm','Vietnam','as'),
        ('"jpn"','Japan', 'as'),
        ('spa','Spain','"eu"'),
        ('bza','Brazil', 'af'),
        ('ger','Germany', '"eu"')
    SQL
    FactoryBot.create :dropdown_shared_filter, name: 'parent', settings: settings
  end
  let(:child_filter) do
    settings[:parent_filter_id] = parent_filter.id
    settings[:is_child_filter] = true
    settings[:dropdown_sql] = <<-SQL.strip_heredoc
      values
        ('hcm','Ho Chi Minh','vnm'),
        ('"hn"','Ha Noi', 'vnm'),
        ('rea','Real Madrid', 'spa'),
        ('bar','Barcelona', 'spa')
    SQL
    FactoryBot.create :dropdown_shared_filter, name: 'child', settings: settings
  end
  let(:report) do
    sql = <<-SQL.strip_heredoc
      select *
      from
        (
          values
            ('as','vnm','hcm'),
            ('as','vnm','"hn"'),
            ('"eu"','spa', 'rea'),
            ('"eu"','spa', 'bar'),
            ('as','"jpn"', 'tok'),
            ('as','"jpn"', 'ran'),
            ('au','aus', 'syd'),
            ('am','mex', 'foo')
        ) R
      where [[column1 IN ({{grandparent}})]]
      and   [[column2 IN ({{parent}})]]
      and   [[column3 IN ({{child}})]]
    SQL
    report = FactoryBot.create :query_report, query: sql, data_source: ds
    FactoryBot.create :filter_ownership, filterable: report, shared_filter: child_filter, var_name: 'child'
    FactoryBot.create :filter_ownership, filterable: report, shared_filter: parent_filter, var_name: 'parent'
    FactoryBot.create :filter_ownership, filterable: report, shared_filter: grandparent_filter, var_name: 'grandparent'
    report
  end

  def set_filter_type(filter, type)
    case type
    when 'multi'
      filter.settings[:dropdown_multiselect] = true
    when 'single'
      filter.settings[:dropdown_lazy_loading] = false
      filter.settings[:dropdown_multiselect] = false
    else
      filter.settings[:dropdown_lazy_loading] = true
      filter.settings[:dropdown_multiselect] = false
    end
    filter.save
  end

  def expand_filters_panel
    safe_click('.ci-expand-filter') unless page.has_css?('.ci-filter-list')
    wait_for_all_filter_loaded
  end

  def select_options(type, selection, expected_options, position = 0)
    option_selector = '.select2-results li'
    wait_for { page.all(".ci-#{type}-dropdown-filter .filter-editable").count > position }
    page.all(".ci-#{type}-dropdown-filter .filter-editable")[position].click
    case type
    when 'multi'
      option_selector = '.dropdown-filter__option'
      selection.each { |idx| page.all(option_selector)[idx].click }
      expect(page.all(option_selector).map { |u| u.text }).to eq(expected_options)
      page.first(".ci-#{type}-dropdown-filter .filter-editable").click
    when 'single'
      expect(page.all(option_selector).map { |u| u.text }).to eq(expected_options)
      page.all(option_selector)[selection].click
    else
      page.first('.select2-search__field').set selection[0]
      sleep 1.5
      expect(page.all(option_selector).map { |u| u.text }).to eq(expected_options)
      page.all(option_selector)[selection[1]].click
    end
  end

  context 'testing adding filters' do
    before do
      safe_login(admin, new_query_report_path)

      wait_for_editor
      fill_in_ace('.editor', 'select 1')

      safe_click('.ci-report-name')
      wait_and_set('.ci-title-input', 'Test report')
    end

    context 'add two new parent-child filter to report and remove the parent' do
      before do
        add_new_report_filter('.ci-filter-type-2', 'parent') do
          first('.table-filter-manual-entries').double_click
          first('.ci-dropdown-manual-textarea').set("opt_1,Option 1\nopt_2,Option 2\nopt_3,Option 3")
        end

        add_new_report_filter('.ci-filter-type-2', 'child') do
          first('.table-filter-manual-entries').double_click
          first('.ci-dropdown-manual-textarea').set("opt_1,Child Option 1,opt_1\nopt_2,Child Option 2,opt_2\nopt_3,Child Option 3,opt_3")

          # check child filter checkbox
          h_checkbox_check(label: 'This is a child filter')
          # parent dropdown should be shown in list
          dropdown_parent_list = find('.ci-select-parent').all('option').collect(&:text)

          expect(dropdown_parent_list.any? { |each| each.include?('parent') }).to be true

          # select parent
          find('.ci-select-parent').first('option').click
        end

        safe_click('.filter-header')
        safe_click('.ci-filter-setting-toggle')
        safe_click('.hui-dropdown-floating .ci-delete-item')
        safe_click('.ci-confirm-delete')
      end

      it 'should show error when user click save' do
        safe_click '.ci-save-report'
        wait_for_element_load('.save-report-modal')
        safe_click('.ci-save-report-from-explore-modal')

        test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', 'Could not find parent filter `parent` for filter `child`')

        safe_click '.ci-discard'

        wait_for_element_load('.confirm-modal')
        safe_click '.confirm-modal .ci-confirm'
        sleep(1)
      end
    end

    context 'then edit the filters' do
      before do
        add_new_report_filter('.ci-filter-type-2', 'parent') do
          first('.table-filter-manual-entries').double_click
          first('.ci-dropdown-manual-textarea').set("opt_1,Option 1\nopt_2,Option 2")
        end

        add_new_report_filter('.ci-filter-type-2', 'child') do
          first('.table-filter-manual-entries').double_click
          first('.ci-dropdown-manual-textarea').set("opt_1,Child Option 1,opt_1\nopt_2,Child Option 2,opt_2\nopt_3,Child Option 3,opt_3")

          # check child filter checkbox
          h_checkbox_check(label: 'This is a child filter')
          # parent dropdown should be shown in list
          dropdown_parent_list = find('.ci-select-parent').all('option').collect(&:text)
          expect(dropdown_parent_list.any? { |each| each.include?('parent') }).to be true

          # select parent
          safe_click('.ci-select-parent option:first-child')
        end

        safe_click('.ci-save-report')
        wait_for_element_load('.save-report-modal')
        safe_click('.ci-save-report-from-explore-modal')
        test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', 'successfully')

        wait_expect(1) { QueryReport.count }
        visit(edit_query_report_path(QueryReport.last))
        # there could be warning alert in different browser
        begin
          page.driver.browser.switch_to.alert.accept
        rescue Selenium::WebDriver::Error::NoSuchAlertError
          nil # no further action if there is no alert
        end

        wait_for_element_load('.ci-expand-filter')
      end

      context 'remove parent filter and create a new one' do
        before do
          expand_filters_panel

          wait_for_element_load('.add-filter-btn')

          safe_click('.ci-filter-setting-toggle')
          safe_click('.hui-dropdown-floating .ci-delete-item')
          safe_click('.ci-confirm-delete')

          # Not sure why we need to double click here
          add_new_report_filter('.ci-filter-type-2', 'new_parent') do
            first('.table-filter-manual-entries').double_click
            first('.ci-dropdown-manual-textarea').set('opt_3,Option 3')
          end

          expand_filters_panel

          safe_click('.ci-filter-edit')
          safe_click('.ci-select-parent option:first-child')
          safe_click('.ci-shared-filter-save')

          safe_click('.ci-save-report')
          test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', 'successfully')
        end

        it 'should restrict child filter by the new parent' do
          wait_for_report_data

          # NOTE: somehow both filters have order = 1
          # So we have to hack the order here to ensure the test consistency
          # TODO: fix the order? https://app.asana.com/0/1184979857064325/1203599661087262/f
          FilterOwnership.where(var_name: 'child').update(order: 0)
          FilterOwnership.where(var_name: 'new_parent').update(order: 1)
          reload_page
          wait_for_report_data

          select_options('single', 0, ['Child Option 3'], 0)
          select_options('single', 0, ['Option 3'], 1)
        end
      end

      context 'create new filter and change parent to newly created filter' do
        before do
          expand_filters_panel
          wait_for_element_load('.add-filter-btn')

          add_new_report_filter('.ci-filter-type-2', 'new_parent') do
            first('.table-filter-manual-entries').double_click
            first('.ci-dropdown-manual-textarea').set('opt_3,Option 3')
          end
          expand_filters_panel
          page.all('.ci-filter-edit')[1].click
          sleep 1
          find(:xpath, '//option[contains(text(), "new_parent")]').select_option
          safe_click('.ci-shared-filter-save')
          sleep 2 # loading

          safe_click('.ci-save-report')
          test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', 'successfully')
        end

        # TODO: should double check this spec later
        it 'should restrict child filter by the new parent' do
          wait_for_viz_load
          select_options('single', 0, ['Child Option 3'], 1)
          select_options('single', 0, ['Option 3'], 2)
        end
      end
    end
  end

  context 'report has normal filter with `parent_filter_name`' do
    before do
      report.filter_ownerships.where(var_name: 'parent').delete_all
      child_sf = report.filter_ownerships.find_by(var_name: 'child').shared_filter
      child_sf.settings[:is_child_filter] = false
      child_sf.settings[:parent_filter_id] = nil
      child_sf.settings[:parent_filter_name] = 'parent'
      child_sf.save
    end

    it 'should save normally' do
      safe_login(admin, edit_query_report_path(report))
      wait_for_element_load('.editor-sql')

      safe_click('.ci-save')
      test_error_notifier('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]', 'successfully')
    end
  end

  describe 'correctly restrict the records in child filters' do
    it 'multi > single > lazy' do
      set_filter_type(grandparent_filter, 'multi')
      set_filter_type(parent_filter, 'single')
      set_filter_type(child_filter, 'lazy')
      path = query_report_path report
      safe_login(admin, path)
      wait_for_report_load

      regions = ['Asia', 'Europe', 'Africa', 'America', 'Australia']

      select_options('multi', [0, 2], regions)
      select_options('single', 0, ['Vietnam', 'Japan', 'Brazil'])
      select_options('lazy', ['ha', 0], ['Ha Noi'])
      select_options('lazy', ['re', 0], ['No results found'])

      select_options('multi', [1, 2], regions)
      select_options('single', 2, ['Vietnam', 'Japan', 'Spain', 'Germany'])
      select_options('lazy', ['re', 0], ['Real Madrid'])
      select_options('lazy', ['ha', 0], ['No results found'])

      safe_click('.ci-lazy-dropdown-filter .filter-editable')
      safe_click('.panel-filters .ci-submit-filters-btn')
      sleep 1
      wait_expect(['"eu"', 'spa', 'rea']) { page.all('.ci-table-report-data .ag-row .ag-cell').map(&:text) }
    end

    it 'lazy > multi > single' do
      set_filter_type(grandparent_filter, 'lazy')
      set_filter_type(parent_filter, 'multi')
      set_filter_type(child_filter, 'single')
      path = query_report_path report
      safe_login(admin, path)
      wait_for_report_load

      select_options('lazy', ['ia', 0], ['Asia', 'Australia'])
      select_options('multi', [0, 1], ['Vietnam', 'Japan'])
      select_options('single', 0, ['Ho Chi Minh', 'Ha Noi'])

      select_options('lazy', ['ia', 1], ['Asia', 'Australia'])
      select_options('lazy', ['ia', 0], ['Asia', 'Australia'])
      select_options('single', 0, ['Ho Chi Minh', 'Ha Noi'])

      safe_click '.panel-filters .ci-submit-filters-btn'
      wait_for_report_data
      sleep 1
      values = page.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)
      expect(values).to eq(['as', 'vnm', 'hcm'])
    end
  end
end
