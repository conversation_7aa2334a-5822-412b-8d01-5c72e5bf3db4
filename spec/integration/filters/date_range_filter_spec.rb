# typed: false
require 'rails_helper'

describe 'Date Range Filter', js: true, stable: true do
  let(:ds) { get_test_ds }
  let(:filter) { FactoryBot.create :date_range_shared_filter }
  let(:report) { FactoryBot.create :query_report }

  context 'when selecting date range for report' do
    before do
      filter.settings.merge!(
        date_min: '2017-01-11',
        date_max: '2017-02-01',
        default_start: '2017-01-18',
        default_end: '2017-01-25'
      )
      filter.save!

      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: 'date_range'

      qlogin(:admin, query_report_path(report))
    end

    it 'should not allow user to select dates outside of min/max values' do
      wait_and_click('div.date-range-picker')

      start_date_before = page.find('input[name="date_range_start"]', visible: false).value
      # select invalid date 2017-01-10
      page.first('.mx-calendar-content td.cell[title="2017-01-10"]').click
      start_date_after = page.find('input[name="date_range_start"]', visible: false).value

      # invalid selection will not set start date
      expect(start_date_after).to eq start_date_before

      # select valid start date '2017-01-11'
      # this set both start and end to '2017-01-11'
      page.first('.mx-calendar-content td.cell[title="2017-01-11"]').click

      # select invalid date 2017-02-02
      page.first('.mx-calendar-content td.cell[title="2017-02-02"]').click
      end_date_after = page.find('input[name="date_range_end"]', visible: false).value

      # invalid selection will not set end date
      # at this point start and end are still the same date
      expect(end_date_after).to eq '2017-01-25'
    end
  end
end
