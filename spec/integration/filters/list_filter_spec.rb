# typed: false
require 'rails_helper'

describe 'List Input Filter', js: true, legacy: true do
  let(:ds) {get_test_ds}
  let(:max_items) {'2'}

  before do
    @report = FactoryBot.create :query_report, title: 'List input report', data_source_id: ds.id
    FactoryBot.create :tenant_subscription, tenant_id: ds.tenant_id, status: 'active'
    qlogin(:admin)
  end

  def click sel
    page.find(sel).click
  end

  context 'when creating new filter' do
    def navigate_to_filter_modal
      wait_for_element_load '.ci-toggle'
      safe_click('.ci-toggle')
      wait_for_element_load('a.ci-edit-report-link')
      safe_click('a.ci-edit-report-link')

      wait_for_element_load '.ci-filter-tab'
      page.find('.ci-filter-tab').click
      wait_for_element_load '.ci-add-adhoc-filter-btn'
      safe_click('.ci-add-adhoc-filter-btn')
    end

    def fill_max_items
      # set filter name
      page.find('input.ci-sf-name').set 'list'

      select_h_select_option('.ci-items-limit-control', value: 100)

      safe_click('button.ci-shared-filter-save')
      # save and continue editing
      safe_click('button#upper-save-only-button')
    end

    it 'should provide options to set maximum number of input items' do
      visit query_report_path @report
      wait_for_report_load
      navigate_to_filter_modal
      wait_for_element_load('.ci-modal-edit-filter')
      sleep 1
      select_h_select_option('.ci-select-item', value: 'list_input')

      # input label
      expect(page).to have_content 'Maximum number of items'
      # input control
      expect(page.find('.ci-items-limit-control')).to be_truthy
      # help text denote server side global limit
      expect(page).to have_content 'limit is 5000 items'

      fill_max_items

      # check the new list filter to see if max items are set correctly
      safe_click('a.ci-edit-filter-link')
      wait_for_element_load '.ci-items-limit-control'
      sleep 1
      safe_click('.ci-items-limit-control')
      sleep 1
      expect(page.all('.hui-select-floating .v-vl-visible-items .hui-select-option').map {|u| u.text}).to eq(["Default", "50", "100", "200", "500", "1000", "2000", "5000"])

      clear_page_unload_events
    end
  end

  context 'with valid max items settings' do
    before do
      # mock list filter with max items
      filters = [
        {
          "name" => "list",
          "type" => "list_input",
          "order" => 0,
          "max_items" => max_items,
          "data_source_id" => ds.id,
          "date_max_ds_id" => ds.id,
          "date_min_ds_id" => ds.id,
          "parent_filter_id" => nil
        }
      ]

      @dashboard = FactoryBot.create :dashboard
      FactoryBot.create :dashboard_widget, dashboard: @dashboard, tenant: @dashboard.tenant, source_type: 'QueryReport',
                         source_id: @report.id

      f1 = FactoryBot.create :shared_filter, name: 'list', settings: filters[0]
      FactoryBot.create :filter_ownership, filterable: @report, shared_filter: f1, var_name: 'list'
      FactoryBot.create :filter_ownership, filterable: @dashboard, shared_filter: f1, var_name: 'list'
    end

    def test_updating_items(inputStr, outputStr, message = nil, dashboard = false)
      wait_for_element_load '.ci-list-filter-open'
      safe_click('div.ci-list-filter-open')
      wait_for_element_load('textarea.list-input-textarea')
      if not inputStr.nil?
        page.first('textarea.list-input-textarea').set inputStr
      else
        safe_click('button.ci-clear-all')
      end
      safe_click('button.ci-modal-submit')

      if message.present?
        wait_for_element_load('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')
        expect(page.first('[data-ci="ci-toasts-top"] [data-ci="ci-toast"]')).to have_content message
        safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')
        sleep 1
      end
      if dashboard
        safe_click('.ci-modal-cancel')
        wait_for_element_load '.filter-header'
        safe_click('.filter-header')
      end
      sleep 1
      expect(page.first('.ci-list-filter-open').text).to eq outputStr
    end

    context 'in report page' do
      it 'should allow user to submit items in limitation' do
        visit query_report_path @report
        test_updating_items("1\n2", "Current items: 2")
        test_updating_items(nil, "No items")
      end
      it 'should prevent user from submitting items exceeding limit' do
        visit query_report_path @report
        test_updating_items("1\n2\n3", "No items", "exceeds limit #{max_items}")
      end
    end

    context 'in dashboard page' do
      it 'should allow user to submit items in limitation' do
        visit query_report_path @report
        test_updating_items("1\n2", "Current items: 2")
        test_updating_items(nil, "No items")
      end
      it 'should prevent user from submitting items exceeding limit' do
        visit dashboard_path @dashboard
        wait_for_element_load '.filter-header'
        safe_click('.filter-header')
        test_updating_items("1\n2\n3", "No items", "exceeds limit #{max_items}", true)
      end
    end
  end
end
