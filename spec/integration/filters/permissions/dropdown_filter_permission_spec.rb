# typed: false
require 'rails_helper'

describe 'Dropdown filter permision', js: true, stable: true do
  let (:permission_yml) {
    "permissions:\n
        - in_group: Singapore\n
          then_sql:\n
            ds_id: #{ds.id}\n
            query: select * from (values ('sgd'), ('usd')) R\n
        - if_group: Vietnam\n
          sql: values ('vnd'), ('usd')\n
        - in_group: Thailand\n
          value: thb\nexpression: Thailand union (Vietnam intersect Singapore)"
  }
  let (:filter_sql) {
    "values ('vnd','Viet Nam Dong'),
            ('usd','US Dollar'),
            ('thb','Baht'),
            ('sgd','Singapore Dollar')"
  }
  let(:filter) {
    FactoryBot.create :dropdown_shared_filter,
                       name: 'currency',
                       settings: {
                         :type => "dropdown",
                         :hide_all => false,
                         :permissions => permission_yml,
                         :data_source_id => ds.id,
                         :date_max_ds_id => ds.id,
                         :date_min_ds_id => ds.id,
                         :dropdown_source => "sql",
                         :override_default => true,
                         :parent_filter_id => nil,
                         :dropdown_multiselect => false,
                         :dropdown_sql => filter_sql
                       }
  }
  let (:report) {
    FactoryBot.create :query_report,
                       query: "select * from ( #{filter_sql} ) R
                               where [[column1 IN ({{#{filter.name}}})]] ",
                       data_source: ds
  }
  let (:admin) {get_test_admin}
  let (:current_user) {get_test_admin}
  let (:ds) {get_test_ds}

  shared_examples 'filter permissions' do
    it 'should filter out correct options based on permissions' do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
      groups.each {|g| FactoryBot.create :group_membership, group: g, user: current_user}
      qlogin(current_user, query_report_path(report))

      wait_for_report_load
      wait_for_element_load '.filter-editable'
      expect(page.first('.select2-selection__placeholder').text).to eq expected_placeholder
      page.first('.filter-editable').click
      expect(page.all('.select2-results li').map {|u| u.text}).to eq(expected_options)
      expect(page.all('.ci-table-report-data .ag-row .ag-cell:first-child').map {|u| u.text}).to eq(expected_codes)
    end
  end

  describe 'user belongs to no restricted group' do
    let(:expected_placeholder) {'All'}
    let(:expected_options) {['Viet Nam Dong', 'US Dollar', 'Baht', 'Singapore Dollar']}
    let(:expected_codes) {['vnd', 'usd', 'thb', 'sgd']}
    it_behaves_like "filter permissions"
  end

  describe 'user belongs to one restricted group' do
    let(:groups) {
      [(FactoryBot.create :group, name: 'Singapore')]
    }
    let(:expected_placeholder) {'All (Restricted)'}
    let(:expected_options) {['US Dollar', 'Singapore Dollar']}
    let(:expected_codes) {['usd', 'sgd']}
    it_behaves_like "filter permissions"
  end

  describe 'user belongs to many restricted groups => effected by expression' do
    let(:expected_placeholder) {'All (Restricted)'}
    let(:expected_options) {['US Dollar', 'Baht']}
    let(:expected_codes) {['usd', 'thb']}
    let(:groups) {
      [
        (FactoryBot.create :group, name: 'Singapore'),
        (FactoryBot.create :group, name: 'Vietnam'),
        (FactoryBot.create :group, name: 'Thailand')
      ]
    }
    it_behaves_like "filter permissions"
  end

  context 'subtitution $user.email' do

    let(:perm_sql) {
      <<-SQL.strip_heredoc
        with manager_stores as (
          values ('vnd','#{admin.email}'), ('thb','invalid_string'), ('usd','#{admin.email}'))
        select column1 from manager_stores where column2 = {{ $user.email }}
      SQL
    }

    let (:permission_yml_email) {
      {permissions: [{if_group: 'Manager', then_sql: {ds_id: ds.id, query: perm_sql}}]}
    }

    let(:filter) {
      FactoryBot.create :dropdown_shared_filter,
                         name: 'currency',
                         settings: {
                           :type => 'dropdown',
                           :hide_all => false,
                           :permissions => permission_yml_email.to_yaml,
                           :data_source_id => ds.id,
                           :date_max_ds_id => ds.id,
                           :date_min_ds_id => ds.id,
                           :dropdown_source => 'sql',
                           :override_default => true,
                           :parent_filter_id => nil,
                           :dropdown_multiselect => false,
                           :dropdown_sql => filter_sql
                         }
    }
    let(:expected_placeholder) {'All (Restricted)'}
    let(:groups) {[(create :group, name: 'Manager')]}

    describe 'should return correct permissions' do
      let(:expected_options) {['Viet Nam Dong', 'US Dollar']}
      let(:expected_codes) {%w(vnd usd)}
      it_behaves_like 'filter permissions'
    end

    describe 'should return empty permissions with wrong user' do
      let(:current_user) {User.where(name: 'admin2').first}
      let(:expected_options) {['No results found']}
      let(:expected_codes) {%w(vnd usd thb sgd)} # no filter => 1 == 1
      it_behaves_like 'filter permissions'
    end

  end

  describe 'there is only one available option' do
    let(:groups) {
      [(FactoryBot.create :group, name: 'Thailand')]
    }
    let(:expected_codes) {['thb']}
    it 'should display the options as text' do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
      groups.each {|g| FactoryBot.create :group_membership, group: g, user: current_user}

      qlogin(current_user, query_report_path(report))
      wait_for_report_load
      wait_expect('Baht') {page.first('.ci-input-readonly').text}
      expect(page.all('.ci-table-report-data .ag-row .ag-cell:first-child').map {|u| u.text}).to eq(expected_codes)
    end
  end

  describe 'permission string incorrect' do
    let (:permission_yml) {''}
    it 'unable to save the filter' do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
      qlogin(current_user, '/shared_filters')

      wait_for_element_load('a.ci-filter-name')
      page.first('a.ci-filter-name').click
      wait_for_element_load('.modal-dialog')
      sleep 1
      safe_click('.ci-permission-toggle')
      wait_for_element_load('#ci-ace-permissions')
      page.execute_script 'window.ace.edit("ci-ace-permissions").setValue("some text")'
      page.find('.ci-permissions-test').click

      expect(page.find('.permission-error-details').text).to eq 'Permissions must starts with line "permissions:".'
    end
  end
end
