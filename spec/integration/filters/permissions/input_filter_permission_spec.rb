# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Input filter permission', js: true, stable: true do
  let(:permission_yml) do
    "permissions:\n
        - in_group: Vietnam\n
          then_sql:\n
            ds_id: #{ds.id}\n
            query: select 'VND'
        - if_group: Singapore\n
          then_value: SGD"
  end
  let(:filter) do
    FactoryBot.create :shared_filter,
                       name: 'Input filter',
                       settings: {
                         type: 'input',
                         default: default_text,
                         permissions: permission_yml,
                         data_source_id: ds.id,
                         date_max_ds_id: ds.id,
                         date_min_ds_id: ds.id,
                         override_default: true
                       }
  end
  let(:report) do
    FactoryBot.create :query_report,
                       query: 'select 1',
                       data_source: ds
  end
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }
  let(:default_text) { 'default_text' }
  let(:expected_readonly_text) { nil }
  let(:expected_editable_text) { default_text }

  # capybara 3: raise exception instead return nil
  def first_or_nil(selector)
    page.first(selector) if page.has_css?(selector)
  end

  shared_examples 'filter permissions' do
    before do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
      groups.each { |g| FactoryBot.create :group_membership, group: g, user: admin }
    end

    it 'should return correct text based on permissions' do
      path = query_report_path report
      safe_login(admin, path)

      wait_for_report_load

      readonly_text = first_or_nil('.ci-input-readonly')&.text
      expect(readonly_text).to eq expected_readonly_text

      editable_text = first_or_nil('.ci-input-editable .vinput')&.value
      expect(editable_text).to eq expected_editable_text
    end
  end

  describe 'user matches email condition' do
    let (:permission_yml) do
      "permissions:\n
        - in_group: Vietnam\n
          value: VND\n
        - if_user: #{admin.email}\n
          value: SGP\n
        - if_group: Singapore\n
          then_value: SGD"
    end
    let(:expected_readonly_text) { 'SGP' }
    let(:expected_editable_text) { nil }
    it_behaves_like 'filter permissions'
  end

  describe 'user belongs to no restricted group' do
    it_behaves_like 'filter permissions'
  end

  describe 'user belongs to one restricted group' do
    let(:groups) do
      [(FactoryBot.create :group, name: 'Singapore')]
    end
    let(:expected_readonly_text) { 'SGD' }
    let(:expected_editable_text) { nil }

    it_behaves_like 'filter permissions'
  end

  describe 'user belongs to many restricted groups' do
    let(:groups) do
      [
        (FactoryBot.create :group, name: 'Singapore'),
        (FactoryBot.create :group, name: 'Vietnam'),
      ]
    end

    describe 'should follow the rule of the first group matched in permission_yml' do
      let(:expected_readonly_text) { 'VND' }
      let(:expected_editable_text) { nil }
      it_behaves_like 'filter permissions'
    end
  end

  describe 'permission string incorrect' do
    let(:permission_yml) { '' }

    it 'unable to save the filter' do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
      safe_login(admin, '/shared_filters')
      safe_click('a.ci-filter-name')
      safe_click('.ci-permission-toggle')
      wait_for_element_load('#ci-ace-permissions')
      page.execute_script 'window.ace.edit("ci-ace-permissions").setValue("some text")'
      safe_click('.ci-permissions-test')

      expect(page.find('.permission-error-details').text).to eq 'Permissions must starts with line "permissions:".'
    end
  end
end
