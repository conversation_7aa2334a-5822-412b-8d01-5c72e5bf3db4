# typed: false

require 'rails_helper'

describe 'Date filter permission', js: true, stable: true do
  let(:permission_yml) do
    "permissions:\n
        - in_group: Vietnam\n
          date_min: '2016-06-11'\n
        - if_group: Thailand\n
          date_max: '2016-06-22'\n
        - in_group: Singapore\n
          then_date_min: '2016-06-11'\n
          then_date_max: '2016-06-16'\n"
  end
  let(:filter) do
    FactoryBot.create :date_sf,
                      name: 'Date filter',
                      settings: {
                        type: 'date',
                        permissions: permission_yml,
                        data_source_id: ds.id,
                        date_max_ds_id: ds.id,
                        date_min_ds_id: ds.id,
                        date_max_toggle: true,
                        date_max_type: 'human',
                        date_max_human: 'yesterday',
                        override_default: true,
                      }
  end
  let(:report) do
    FactoryBot.create :query_report,
                      query: 'select 1',
                      data_source: ds
  end
  let(:admin) { get_test_admin }
  let(:ds) { get_test_ds }
  let(:current_date) { DateTime.parse('2016-06-25 08:00:00') }
  let(:expected_year) { '2016' }
  let(:expected_month) { 'Jun' }
  let(:expected_disabled_days) { ['25'] }

  let(:date_picker_class) { '.mx-calendar' }
  let(:year_text_class) { '.mx-btn-current-year' }
  let(:month_text_class) { '.mx-btn-current-month' }
  let(:disabled_cell_class) { '.cell.disabled:not(.not-current-month)' }

  shared_examples 'filter permissions' do
    it 'limits correct date range based on permissions' do
      Timecop.travel(current_date) do
        FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
        groups.each { |g| FactoryBot.create :group_membership, group: g, user: admin }

        qlogin :admin, query_report_path(report)

        wait_for_report_load
        page.first('.date-filter-wrapper input').click

        wait_for_element_load(date_picker_class)
        picker = page.first(date_picker_class)
        year = picker.first(year_text_class).text
        month = picker.first(month_text_class).text
        disabled_days = picker.all(disabled_cell_class).map { |d| d.text }
        expect(year).to eq expected_year
        expect(month).to eq expected_month
        expect(disabled_days & expected_disabled_days).to eq expected_disabled_days
      end
    end
  end

  describe 'user belongs to no restricted group' do
    let(:expected_year) { '2016' }
    let(:expected_month) { 'Jun' }
    let(:expected_disabled_days) { %w[25 26 27 28 29 30] }

    it_behaves_like 'filter permissions'
  end

  describe 'user belong to one restricted group' do
    describe 'restricted rule is date min' do
      let(:expected_month) { 'Jun' }

      let(:groups) { [(FactoryBot.create :group, name: 'Vietnam')] }
      let(:expected_disabled_days) { %w[1 2 3 4 5 6 7 8 9 10 25 26 27 28 29 30] }

      it_behaves_like 'filter permissions'
    end

    describe 'restricted rule is date max' do
      let(:groups) { [(FactoryBot.create :group, name: 'Thailand')] }
      let(:expected_disabled_days) { %w[23 24 25 26 27 28 29 30] }

      it_behaves_like 'filter permissions'
    end

    describe 'restrict rule contains both date_min and date_max' do
      let(:expected_month) { 'Jun' }

      let(:groups) { [(FactoryBot.create :group, name: 'Singapore')] }
      let(:expected_disabled_days) { %w[1 2 3 4 5 6 7 8 9 10 17 18 19 20 21 22 23 24 25 26 27 28 29 30] }

      it_behaves_like 'filter permissions'
    end

    describe 'restrict rule contains both date_min and date_max' do
      let(:expected_month) { 'Jun' }

      let(:permission_yml) do
        "permissions:\n
        - if_user: #{admin.email}\n
          date_min: '2016-06-11'\n
          date_max: '2016-06-16'\n"
      end
      let(:expected_disabled_days) { %w[1 2 3 4 5 6 7 8 9 10 17 18 19 20 21 22 23 24 25 26 27 28 29 30] }

      it_behaves_like 'filter permissions'
    end
  end

  describe 'user belongs to many restricted groups' do
    let(:groups) do
      [
        (FactoryBot.create :group, name: 'Singapore'),
        (FactoryBot.create :group, name: 'Thailand'),
      ]
    end

    describe 'should follow the rule of the first group matched in permission_yml' do
      let(:expected_disabled_days) { %w[23 24 25 26 27 28 29 30] }

      it_behaves_like 'filter permissions'
    end
  end

  describe 'permission string incorrect' do
    let(:permission_yml) { '' }

    before do
      FactoryBot.create :filter_ownership, filterable: report, shared_filter: filter, var_name: filter.name
    end

    it 'unable to save the filter' do
      qlogin admin, '/shared_filters'

      wait_for_element_load('.ci-sf-index')
      safe_click('a.ci-filter-name')
      safe_click('.ci-permission-toggle')

      wait_for_element_load('.ci-sample-permissions')
      safe_click('.ci-sample-permissions')

      page.execute_script 'window.ace.edit("ci-ace-permissions").setValue("some text")'
      safe_click('.ci-permissions-test')

      wait_expect('Permissions must starts with line "permissions:".') { page.find('.permission-error-details').text }
    end
  end
end
