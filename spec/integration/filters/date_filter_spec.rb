# typed: false
require 'rails_helper'

describe 'Date filter', js: true, legacy: true do
  let (:admin) {get_test_admin}
  let (:ds) {get_test_ds}

  before do
    qlogin(:admin, "/shared_filters")
  end

  it 'it should be create success date filter' do
    page.find('.ci-create-sf').click

    wait_for_element_load('.ci-modal-edit-filter')
    sleep 1
    select_h_select_option('.ci-select-item', value: 'date')

    page.find('.ci-sf-label-name').set('Just for test')
    page.find('.ci-sf-preview').click

    wait_for_element_load '.ci-datepicker-input'
    expect(page.find('.ci-datepicker-input').value).not_to be nil
  end
end
