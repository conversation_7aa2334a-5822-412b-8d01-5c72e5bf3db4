# typed: false
require 'rails_helper'

describe 'Filter Actions', js: true, legacy: true do
  let (:admin) {get_test_admin}
  let (:ds) {get_test_ds}
  let (:filters) {
    {
      single_dropdown: (create :country_sf, name: 'Single dropdown'),
      lazy_dropdown: (create :lazy_sf, name: 'Lazy dropdown'),
      multi_dropdown: (create :multi_sf, name: 'Multiple dropdown'),
      input: (create :shared_filter, name: 'Input'),
      list: (create :list_sf, name: 'List Input'),
      date: (create :date_sf, name: 'Date'),
      date_range: (create :date_range_shared_filter, name: 'Date range'),
      separator: (create :separator_shared_filter, name: 'Separator')
    }
  }
  let (:settings) {
    {
      input: {default: 'hello'},
      list: {max_items: 15},
      date: {
        default: '2017-05-16',
        date_min_type: 'human',
        date_max_type: 'human',
        date_min_toggle: true,
        date_max_toggle: true,
        date_min_human: '2017-05-11',
        date_max_human: '2017-05-27',
      },
      date_range: {
        date_min: '2017-05-11',
        date_max: '2017-05-27',
        default_start: '2017-05-16',
        default_end: '2017-05-25'
      },
      separator: {},
      multi_dropdown: {default: 'sg,th'},
      single_dropdown: {default: 'sg'},
      lazy_dropdown: {default: 'sg'}
    }
  }
  let (:dashboard) {
    dashboard = create :dashboard
    FactoryBot.create :dashboard_widget, dashboard: dashboard, source: report
    filters.each do |key, filter|
      create :filter_ownership, filterable: dashboard, shared_filter: filter, var_name: key, label: key
    end
    dashboard
  }
  let (:report) {
    query = 'values({{input}},{{date}},{{date_range_start}},{{date_range_end}},{{multi_dropdown}},{{single_dropdown}},{{lazy_dropdown}})'
    report = create :query_report, query: query
    filters.each do |key, filter|
      create :filter_ownership, filterable: report, shared_filter: filter, var_name: key, label: key
    end
    report
  }

  before do
    FeatureToggle.toggle_tenant('shareable_link:ui_enabled', admin.tenant.id, true)
    FeatureToggle.toggle_tenant('public_users:cache_params_without_token', admin.tenant.id, true)
    GlobalConfig.set('tenant:use_new_color_palette_date', '01-01-2000')
    create :tenant_subscription, tenant: admin.tenant, status: 'active', embed_workers: 3
    filters.each do |key, filter|
      filter.settings.merge!(settings[key])
      filter.save
    end
  end

  describe 'Report' do
    before do
      path = query_report_path report
      qlogin(admin, path)
      wait_for_element_load '.ci-table-report-data'
    end

    it 'correctly render default value' do
      test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
    end

    it 'correctly render filters in shareable link' do
      test_sharable_link
    end

    it 'correctly render filters in embedded link with no filter' do
      safe_click '.ci-toggle'
      url = test_embed_link_with_no_filter
      disable_report_filter_default_value
      visit "#{url}&input=hehehe"
      test_filter_values ['report_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
    end

    it 'correctly render filters in embedded link with single filter' do
      safe_click '.ci-toggle'
      url = test_embed_link_with_single_filter
      disable_report_filter_default_value
      visit "#{url}&input=hehehe"
      sleep 2 # wait for loading
      test_filter_values ['report_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    end

    it 'correctly render filters in embedded link with multiple filters' do
      safe_click '.ci-toggle'
      url = test_embed_link_with_multiple_filter
      disable_report_filter_default_value
      visit "#{url}&input=hehehe"
      sleep 2 # wait for loading
      test_filter_values ['report_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'vn']
    end

    it 'correct render filters in legacy embed link' do
      init_legacy_embed_link(report)
      safe_click '.ci-toggle'
      url = test_legacy_embed
      disable_report_filter_default_value
      visit "#{url}&input=hehehe"
      test_filter_values ['report_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    end
  end

  describe 'Dashboard' do
    before do
      path = dashboard_path dashboard
      safe_login(admin, path)
      wait_for_element_load '.ci-table-report-data'
    end

    it 'correctly render default value' do
      test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
    end

    it 'correctly render filters in shareable link' do
      test_sharable_link
    end

    it 'correctly render filters in embedded link with no filter' do
      sleep 2
      safe_click '.ci-dashboard-settings'
      url = test_embed_link_with_no_filter
      disable_dashboard_filter_default_value
      visit "#{url}&input=hehehe"
      test_filter_values ['dashboard_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
    end

    it 'correctly render filters in embedded link with single filter' do
      sleep 2
      safe_click '.ci-dashboard-settings'
      url = test_embed_link_with_single_filter
      disable_dashboard_filter_default_value
      visit "#{url}&input=hehehe"
      test_filter_values ['dashboard_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    end

    it 'correctly render filters in embedded link with multiple filters' do
      sleep 2
      safe_click '.ci-dashboard-settings'
      url = test_embed_link_with_multiple_filter
      disable_dashboard_filter_default_value
      visit "#{url}&input=hehehe"
      test_filter_values ['dashboard_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'vn']
    end

    it 'correct render filters in legacy embed link' do
      init_legacy_embed_link(dashboard)
      sleep 2
      safe_click '.ci-dashboard-settings'
      url = test_legacy_embed
      disable_dashboard_filter_default_value
      visit "#{url}&input=hehehe"
      test_filter_values ['dashboard_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    end

    context 'embed identifier variable value does not exist in dropdown filter options' do
      context 'accept any identifier value' do
        before do
          FeatureToggle.toggle_global(EmbedLink::FT_ACCEPT_ANY_IDENTIFIER_VAR_VALUE, true)
        end
        it 'keeps the embed identifier value' do
          wait_for_element_load '.ci-dashboard-settings'
          safe_click '.ci-dashboard-settings'
          url = test_embed_link_with_non_existing_embed_identifier_value
          disable_dashboard_filter_default_value
          visit "#{url}&input=hehehe"
          test_filter_values ['dashboard_default', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'wakanda', 'sg']
        end
      end

      it 'shows error' do
        wait_for_element_load '.ci-dashboard-settings'
        safe_click '.ci-dashboard-settings'
        test_embed_link_with_non_existing_embed_identifier_value(reject: true)
      end
    end
  end

  context 'filter with long values' do
    let(:dashboard) { create :dashboard }
    let(:qr) { create :query_report, query: "select {{ text }}, 'asd'", is_adhoc: true }
    let!(:dw) { create :dashboard_widget, source: qr, dashboard: dashboard }
    let(:filter) { create :shared_filter, name: 'text'}
    let(:fo) {
      create :filter_ownership, filterable: dashboard, shared_filter: filter, var_name: 'text', label: 'text'
    }
    let(:el) {
      el = create :embed_link, source: dashboard, filter_ownerships: []
      create :filter_value, filter_valuable: el, filter_ownership: fo, value: {selected_value: 'a' * 500}, settings: {override: true, read_only: false}
      el.update_filters
      el.set_public_user
      el.share_source
      el
    }
    let(:url) {
      sk = el.secret_key
      token = jwt_encode(sk, {}, Time.now.to_i + 90000)
      "/embed/#{el.hash_code}?_token=#{token}"
    }
    it 'can display and be interacted correctly' do
      visit url
      test_filter_values ['a' * 500, 'asd']

      page.first('.ci-filter-header').click
      sleep 1

      page.first('.ci-input-editable .ci-text').set('b' * 501)
      safe_click '.ci-submit-filters-btn'
      sleep 3
      test_filter_values ['b' * 501, 'asd']

      safe_click('.ci-filter-header') if page.has_css?('.ci-filter-header')
      sleep 1

      page.first('.ci-input-editable .ci-text').set('zxc')
      safe_click '.ci-submit-filters-btn'
      sleep 3
      test_filter_values ['zxc', 'asd']

      safe_click('.ci-filter-header') if page.has_css?('.ci-filter-header')
      sleep 1

      page.first('.ci-input-editable .ci-text').set('c' * 600)
      safe_click '.ci-submit-filters-btn'
      sleep 3
      test_filter_values ['c' * 600, 'asd']
    end
  end

  def get_filter_settings(is_embed: false)
    if is_embed
      safe_click '.ci-edit-embed-link-modal' # need to do this so that clicking advanced-filters works after h_select_dropdown, don't know why... :nhkdo:
      wait_for_element_load '.ci-advanced-filters'
      safe_click('.ci-advanced-filters')
    end
    page.all('.filter-value-wrapper').map do |filter|
      key = filter.first('.title').text
      element = filter.first('.filter-settings')
      element.find('.ci-read-only').click
      {"#{key}": element}
    end.reduce(Hash.new, &:merge)
  end

  def test_filter_values values
    wait_for_element_load '.ci-table-report-data'
    wait_expect(values) do
      page.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)
    end
  end

  def test_sharable_link
    safe_click '.share-dropdown.ci-share-dropdown'
    safe_click '.ci-shareable-links'
    safe_click '.ci-new-shareable-link'
    safe_click '.ci-title'
    fill_text('.ci-title', 'JustTest')
    settings = get_filter_settings

    # set filter values
    settings[:lazy_dropdown].first('.ci-read-only').click
    settings[:multi_dropdown].first('.ci-read-only').click
    settings[:list].first('.ci-read-only').click
    settings[:date_range].first('.ci-read-only').click
    settings[:date].first('.ci-override-default').click

    # logout
    url = page.first('.ci-shareable-url').text
    safe_click '.ci-save-links'
    page.reset!
    visit url

    # test values
    test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
    safe_click('.filter-header') if page.has_css?('.filter-header')
    wait_for_element_load '.filter-editable'
    expect(page.all('.filter-editable').count).to eq 3
    expect(page.all('.filter-readonly').count).to eq 4

    # submit new values
    page.first('.ci-input-editable .ci-text').set('world')
    safe_click '.ci-submit-filters-btn'
    sleep 3
    test_filter_values ['world', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']

    params = {
      input: 'hi', # change
      date: '2017-05-20', # change
      date_range_start: '2017-05-20', # => 2017-05-16
      date_range_end: '2017-05-28', # => 2017-05-25
      single_dropdown: 'th', # change
      lazy_dropdown: 'th', # => sg
    }
    param_string = params.map {|key, val| "&#{key}=#{val}"}.reduce('', :+)
    visit "#{url}&multi_dropdown[]=th&multi_dropdown[]=vn#{param_string}"
    test_filter_values ['hi', 'May 20 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'th', 'sg']

    # change default value
    change_filter_default_value
    visit url
    test_filter_values ['test', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
  end

  def test_embed_link_with_single_filter
    init_new_embed_link 'JustTest'

    select_h_select_option('.ci-select-static-filter', label: 'single_dropdown')

    # set filter values
    settings = get_filter_settings(is_embed: true)
    settings[:lazy_dropdown].first('.ci-read-only').click
    settings[:multi_dropdown].first('.ci-read-only').click
    settings[:list].first('.ci-read-only').click
    settings[:date_range].first('.ci-read-only').click
    settings[:date].first('.ci-override-default').click

    safe_click '.ci-save-embed-link'
    url = get_iframe_url({single_dropdown: 'vn'})
    # logout
    page.reset!

    # test values
    visit url
    test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    expect_filter_count editable: 2, readonly: 4

    # submit new values
    page.first('.ci-input-editable .ci-text').set('world')
    safe_click '.ci-submit-filters-btn'
    sleep 3
    test_filter_values ['world', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']

    # change default value
    change_filter_default_value
    visit url
    test_filter_values ['test', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    url
  end

  def test_embed_link_with_no_filter
    init_new_embed_link 'JustTest'

    # set filter values
    settings = get_filter_settings(is_embed: true)
    settings[:single_dropdown].first('.ci-read-only').click
    settings[:lazy_dropdown].first('.ci-read-only').click
    settings[:multi_dropdown].first('.ci-read-only').click
    settings[:list].first('.ci-read-only').click
    settings[:date_range].first('.ci-read-only').click
    settings[:date].first('.ci-override-default').click

    safe_click '.ci-save-embed-link'
    url = get_iframe_url
    # logout
    page.reset!

    # test values
    visit url
    test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']

    # test settings
    expect_filter_count editable: 2, readonly: 5

    # submit new values
    page.first('.ci-input-editable .ci-text').set('world')
    safe_click '.ci-submit-filters-btn'
    sleep 3
    test_filter_values ['world', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']

    # change default value
    change_filter_default_value
    visit url
    test_filter_values ['test', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'sg', 'sg']
    url
  end

  def test_embed_link_with_multiple_filter
    init_new_embed_link 'JustTest'

    # add static filters
    select_h_select_option('.ci-select-static-filter', label: 'single_dropdown')
    select_h_select_option('.ci-select-static-filter', label: 'lazy_dropdown')

    # set filter values
    settings = get_filter_settings(is_embed: true)
    settings[:multi_dropdown].first('.ci-read-only').click
    settings[:list].first('.ci-read-only').click
    settings[:date_range].first('.ci-read-only').click
    settings[:date].first('.ci-override-default').click

    safe_click '.ci-save-embed-link'
    url = get_iframe_url({single_dropdown: 'vn', lazy_dropdown: 'vn'})

    # logout
    page.reset!

    # test values
    visit url
    test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'vn']

    # test settings
    expect_filter_count editable: 2, readonly: 3

    # submit new values
    page.first('.ci-input-editable .ci-text').set('world')
    safe_click '.ci-submit-filters-btn'
    sleep 3
    test_filter_values ['world', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'vn']

    # change default value
    change_filter_default_value
    visit url
    test_filter_values ['test', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'vn']
    url
  end

  def test_embed_link_with_non_existing_embed_identifier_value(reject: false)
    init_new_embed_link 'JustTest'

    select_h_select_option('.ci-select-static-filter', label: 'single_dropdown')

    # set filter values
    settings = get_filter_settings(is_embed: true)
    settings[:lazy_dropdown].first('.ci-read-only').click
    settings[:multi_dropdown].first('.ci-read-only').click
    settings[:list].first('.ci-read-only').click
    settings[:date_range].first('.ci-read-only').click
    settings[:date].first('.ci-override-default').click

    safe_click '.ci-save-embed-link'
    url = get_iframe_url({single_dropdown: 'wakanda'}) # this value does not exist in the filter options

    # logout
    page.reset!

    # test values
    visit url

    if reject
      expect_notifier_content /unexpected filter values/
      return url
    end

    test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'wakanda', 'sg']
    expect_filter_count editable: 2, readonly: 4

    # submit new values
    page.first('.ci-input-editable .ci-text').set('world')
    safe_click '.ci-submit-filters-btn'
    sleep 3
    test_filter_values ['world', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'wakanda', 'sg']

    # change default value
    change_filter_default_value
    visit url
    test_filter_values ['test', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'wakanda', 'sg']
    url
  end

  def test_legacy_embed
    safe_click '.ci-embed-links'
    safe_click '.ci-edit-embed-link'

    url = get_iframe_url({single_dropdown: 'vn'})

    # logout
    page.reset!

    # test values
    visit url
    test_filter_values ['hello', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']

    # test settings
    expect_filter_count editable: 2, readonly: 4

    # submit new values
    page.first('.ci-input-editable .ci-text').set('world')
    safe_click '.ci-submit-filters-btn'
    sleep 3
    test_filter_values ['world', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']

    # change default value
    change_filter_default_value
    visit url
    test_filter_values ['test', 'May 16 2017', 'May 16 2017', 'May 25 2017', 'sg', 'th', 'vn', 'sg']
    url
  end

  def init_new_embed_link (title)
    safe_click '.ci-embed-links'
    safe_click '.ci-new-embed-link'
    safe_click '.ci-title'
    fill_text('.ci-title', title)
    sleep 2
  end

  def change_filter_default_value
    filters[:input][:settings][:default] = 'test'
    filters[:date][:settings][:default] = '2017-05-21'
    filters[:input].save
    filters[:date].save
  end

  def disable_report_filter_default_value
    # If embed link does not have default filter value, it should use report default filter value
    report_input_fo = FilterOwnership.find_by(filterable_id: report.id, filterable_type: 'QueryReport', shared_filter_id: filters[:input].id)
    report_input_fo.update(default: 'report_default', override_default: true)
    embed_link = EmbedLink.last
    # Make embed link have no default value for 'input' filter
    input_fv = FilterValue.find_by(filter_valuable_id: embed_link.id, filter_valuable_type: 'EmbedLink', filter_ownership_id: report_input_fo.id)
    input_fv.settings[:override] = false
    input_fv.settings[:read_only] = true
    input_fv.save
  end

  def disable_dashboard_filter_default_value
    # If embed link does not have default filter value, it should use dashboard default filter value
    dashboard_input_fo = FilterOwnership.find_by(filterable_id: dashboard.id, filterable_type: 'Dashboard', shared_filter_id: filters[:input].id)
    dashboard_input_fo.update(default: 'dashboard_default', override_default: true)
    report_input_fo = FilterOwnership.find_by(filterable_id: report.id, filterable_type: 'QueryReport', shared_filter_id: filters[:input].id)
    report_input_fo.update(default: 'report_default', override_default: true)
    embed_link = EmbedLink.last
    # Make embed link have no default value for 'input' filter
    input_fv = FilterValue.find_by(filter_valuable_id: embed_link.id, filter_valuable_type: 'EmbedLink', filter_ownership_id: dashboard_input_fo.id)
    input_fv.settings[:override] = false
    input_fv.settings[:read_only] = true
    input_fv.save
  end

  def get_iframe_url (params = {})
    wait_for_element_load '.ci-embed-code'
    id = page.first('.ci-embed-code').text
    key = page.first('.ci-secret-key').text
    token = jwt_encode(key, params, Time.now.to_i + 24*60*60)
    "/embed/#{id}?_token=#{token}"
  end

  def expect_filter_count(filter_count)
    safe_click('.filter-header') if page.has_css?('.filter-header')
    wait_for_element_load '.filter-editable'
    expect(page.all('.filter-editable').count).to eq filter_count[:editable]
    expect(page.all('.filter-readonly').count).to eq filter_count[:readonly]
  end

  def init_legacy_embed_link (source)
    embed_fo = source.filter_ownerships.select{ |filter| filter.var_name == 'single_dropdown' }
    embed_link = create :embed_link, source: source, customer_filter_ownership_id: embed_fo.first.id
    embed_link.add_many(:filter_ownership, :embed_link_identifier_variable, embed_fo)
    embed_link.embed_link_identifier_variables.first.update legacy_customer_filter: true
    embed_link.set_public_user
    embed_link.share_source
    # add all source filters to embed link filter values
    source.filter_ownerships.each{ |fo|
      create :filter_value, filter_ownership: fo, filter_valuable: embed_link, settings: { override: false, read_only: true }
    }
    # simulate previous input filter value
    input_fo = FilterOwnership.find_by(filterable_id: source.id, filterable_type: source.class.name, shared_filter_id: filters[:input].id)
    input_fv = FilterValue.find_by(filter_valuable_id: embed_link.id, filter_valuable_type: 'EmbedLink', filter_ownership_id: input_fo.id)
    input_fv.settings[:read_only] = false
    input_fv.save
    # simulate previous date filter value
    date_fo = FilterOwnership.find_by(filterable_id: source.id, filterable_type: source.class.name, shared_filter_id: filters[:date].id)
    date_fv = FilterValue.find_by(filter_valuable_id: embed_link.id, filter_valuable_type: 'EmbedLink', filter_ownership_id: date_fo.id)
    date_fv.settings[:read_only] = false
    date_fv.settings[:override] = true
    date_fv.value[:selected_value] = '2017-05-16'
    date_fv.save
    embed_link
  end
end
