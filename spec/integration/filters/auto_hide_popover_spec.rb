# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'filter popover auto hide', js: true, legacy: true do
  let(:admin) { users(:admin) }

  def add_a_date_filter
    select_h_select_option '.ci-viz-filter-select', value: "#{orders_model.id}$!created_at"
    wait_for_element_load('.ci-filter-form')
    page.find('.ci-date-picker-input').click
    wait_for_element_load('.ci-date-range-picker-calendar')
    a_date_cell = page.first('td.cell:not(.not-current-month)')
    a_date_cell.click # select start date
    a_date_cell.click # select end date
    page.first('.viz-section-header').click
  end

  def make_sure_all_popover_open
    page.has_css?('.ci-filter-form', wait: 0)
    page.has_css?('.ci-date-range-picker-calendar', wait: 0)
  end

  context 'edit a condition' do
    include_context 'format is defined in data modeling'

    before do
      safe_login(admin, "/datasets/#{data_set.id}")
      wait_for_element_load('.ci-viz-filter-select')
      add_a_date_filter
    end

    before(:each) do
      page.find('.ci-dm-filter').click
      wait_for_element_load('.ci-filter-form')
      page.find('.ci-date-picker-input').click
    end

    it 'date filter won\'t close when click on any btn on date picker' do
      # date filter won't close when click next/prev on date picker
      page.first('.mx-btn.mx-btn-text.mx-btn-icon-left').click
      make_sure_all_popover_open

      page.first('.mx-btn.mx-btn-text.mx-btn-icon-right').click
      make_sure_all_popover_open

      # date filter won't close when click month, year on date picker
      page.first('.mx-btn.mx-btn-text.mx-btn-current-month').click
      make_sure_all_popover_open

      page.first('.mx-btn.mx-btn-text.mx-btn-current-year').click
      make_sure_all_popover_open
    end
  end
end
