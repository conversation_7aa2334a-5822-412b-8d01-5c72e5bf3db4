# frozen_string_literal: true
# typed: false

require 'rails_helper'

describe 'Batch Move', js: true, stable: true do
  let(:admin) { get_test_admin }

  let!(:cat_1) { FactoryBot.create(:report_category, name: 'Marketing', parent_id: 0) }
  let!(:cat_2) { FactoryBot.create(:report_category, name: 'IT', parent_id: 0) }

  let!(:report_1) { FactoryBot.create(:query_report, title: 'A', category_id: 0) }
  let!(:report_2) { FactoryBot.create(:query_report, title: 'B', category_id: 0) }
  let!(:report_3) { FactoryBot.create(:query_report, title: 'C', category_id: 0) }

  let!(:personal_cat) { FactoryBot.create(:personal_category, owner: admin) }
  let!(:personal_report) do
    FactoryBot.create(:personal_item, item: report_3, owner: admin, tenant: admin.tenant, category_id: -1)
  end

  before do
    FeatureToggle.toggle_global('bulk_actions', true)
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
    FeatureToggle.toggle_global('new_navigation_node', true)
  end

  it 'select and move batch items' do
    safe_login(admin, '/browse')

    wait_for_element_load('.ci-table-row')

    hover('.ci-table-row-2')
    click('.ci-table-row-2 .h-checkbox')

    hover('.ci-table-row-3')
    click('.ci-table-row-3 .h-checkbox')

    wait_for_element_load('.ci-batch-action-move')
    click('.ci-batch-action-move')

    wait_for_element_load('.ci-batch-move-modal')

    # The click here may not trigger the dropdown, so try to click multiple times
    wait_for do
      click('.ci-batch-move-modal .ci-tree-select-control')
      wait_for_element_load('.ci-tree-select-option-list')
    end

    safe_click('.ci-tree-select-option-list div[data-index="0"] .ci-toggle-arrow')

    sleep 1
    click('.ci-tree-select-option-list div[data-index="1"]')

    sleep 1
    click('.ci-modal-submit')

    wait_for { page.all('.ci-batch-move-modal').count == 0 }

    click('.ci-table-row-0 .ci-node-link')
    sleep 1

    nodes = page.all('.ci-table-row .ci-node-link').map(&:text)
    expect(nodes).to eql([report_1.title, report_2.title])
  end
end
