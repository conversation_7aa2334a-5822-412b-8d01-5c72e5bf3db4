# typed: false
require 'rails_helper'

describe 'Sticky header behavior', js: true, stable: true do
  let(:admin) { get_test_admin }

  let!(:cat_1) { FactoryBot.create(:report_category, name: 'Marketing', parent_id: 0) }
  let!(:cat_2) { FactoryBot.create(:report_category, name: 'IT', parent_id: 0) }

  let!(:report_1) { FactoryBot.create(:query_report, title: 'A', category_id: 0) }
  let!(:report_2) { FactoryBot.create(:query_report, title: 'B', category_id: 0) }
  let!(:report_3) { FactoryBot.create(:query_report, title: 'C', category_id: 0) }

  let!(:personal_cat) { FactoryBot.create(:personal_category, owner: admin) }
  let!(:personal_report) do
    FactoryBot.create(:personal_item, item: report_3, owner: admin, tenant: admin.tenant, category_id: -1)
  end

  before do
    FeatureToggle.toggle_global('bulk_actions', true)
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
    FeatureToggle.toggle_global('new_navigation_node', true)

    # Try to overflow the container to check if the sticky header still displays correctly
    20.times do
      FactoryBot.create(:query_report, title: 'B', category_id: 0)
    end
  end

  it 'Report browse: stick correctly' do
    safe_login(admin, '/browse')

    wait_for_element_load('.ci-table-row')
    wait_for_all_ajax_requests
    wait_for_all_holistics_loadings

    reliable_hover('.ci-table-row-2', '.ci-table-row-2 .h-checkbox')
    safe_click('.ci-table-row-2 .h-checkbox')
    reliable_hover('.ci-table-row-3', '.ci-table-row-3 .h-checkbox')
    safe_click('.ci-table-row-3 .h-checkbox')

    expect(page).to have_content('2 items selected')
    scroll_to_js('.ci-table-row-24')

    # Test if the sticky header shows up in current viewport
    expect(element_in_viewport?('.ci-select-all')).to be_truthy
  end
end
