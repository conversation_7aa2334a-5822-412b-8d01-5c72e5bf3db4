# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'new report', js: true do
  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let(:current_tenant) { get_test_tenant }
  let(:report) { FactoryBot.create :query_report_new }
  let(:adhoc_filter) { FactoryBot.create :date_sf, is_adhoc: true }
  let(:shared_filter) { FactoryBot.create :text_sf }
  let!(:fo_adhoc) do
    FactoryBot.create :filter_ownership,
                       filterable: report,
                       shared_filter: adhoc_filter,
                       var_name: 'adhoc_1',
                       order: 0
  end
  let!(:sf_adhoc) do
    FactoryBot.create :filter_ownership,
                       filterable: report,
                       shared_filter: shared_filter,
                       var_name: 'shared_1',
                       order: 1
  end

  before do
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
  end

  after do
    clear_page_unload_events
  end

  def wait_for_all_filter_loaded
    wait_for_element_load('.ci-filter-list')
    wait_for { page.all('.ci-filter-loading').count == 0 }
  end

  def expand_filters_panel
    if !page.has_css?('.ci-filter-list')
      safe_click('.ci-expand-filter')
    end
  end

  context 'New sql gen flow enabled' do
    before do
      FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
      FeatureToggle.toggle_global('viz:table_v2', true)
    end

    it 'should create sql report successfully' do
      safe_login :admin, '/queries/new?mode=sql'
      wait_for_element_load '.ci-editor-main'
      fill_in_ace '.ci-editor-main', 'select 1'
      safe_click '.ci-btn-run'
      wait_for_element_load('.ci-table-report-data')
      expect(page.find('.ci-table-report-data').text).to match(/\?column\?\s+1/)
      safe_click '.ci-save-report'
      page.find(".saving-form .new-report-container input[name='Title']").set('title')
      safe_click '.ci-save-report-from-explore-modal'
      expect_notifier_content /success/
    end
  end

  context 'New flow creating report >' do
    describe 'Disabled dataset >' do
      before do
        FeatureToggle.toggle_global('data_sets:enabled', false)
      end
      it 'should not show mode select' do
        safe_login :admin, '/queries/new'
        expect(page).not_to have_selector('.ci-mode-select')
      end
    end

    describe 'Enabled dataset >' do
      include_context 'data_modeling_schema_with_data'

      let(:model) { create_data_modeling_model_from_table(ds, 'products') }
      let(:dataset) do
        dataset = DataSet.create_from_models(
          data_models: [model],
          owner_id: admin.id,
          title: 'New dataset',
          category_id: 0,
          data_source_id: model.data_source_id,
          tenant_id: admin.tenant_id,
        )
        dataset.save!
        dataset
      end

      before do
        FeatureToggle.toggle_global('data_sets:enabled', true)
        FeatureToggle.toggle_global('sql_report:creation', true)
        DataSourceVersions::SchemaSynchronizationService.new(ds).execute
        dataset # preload dataset
      end

      def select_field
        select_h_select_option('.viz-section:nth-child(1) .ci-viz-field-select', value: "#{model.id}$!created_at")
      end

      it 'should show mode select' do
        safe_login(admin, '/queries/new')
        wait_expect(true) { page.has_css?('.ci-mode-select') }
      end

      it 'should show dataset list and successfully create report' do
        FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, false)

        safe_login(admin, '/queries/new')
        page.find('.ci-dataset-mode').click
        wait_for_element_load('.ci-data-set-select')
        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        expect(page.find('.ci-limit-select-toggle').text).to eq 'No limit'
        select_field

        page.find('.ci-explorer-control-get-results').click
        page.find('.ci-save-ds-based-report').click
        fill_text('.ci-title .h-input', 'new report')

        select_h_select_option('.ci-folder', label: current_tenant.name)

        page.find('.ci-save-report-from-explore-modal').click
        wait_expect(2) { QueryReport.count }
      end

      it 'should show viz type info' do
        safe_login(admin, '/queries/new')
        page.find('.ci-dataset-mode').click
        wait_for_element_load('.ci-data-set-select')
        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        wait_for_element_load('.viz-type-select')

        page.find('.ci-viz-type-data_table').hover
        expect(page).to have_content 'To quickly view your raw data or to compare several variables.'

        page.find('.ci-viz-type-area_chart').hover
        expect(page).to have_content 'X-axis *: dimension to display your data along'
      end

      it 'has 5k row limit by default when FT is enabled' do
        FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, true)

        safe_login(admin, '/queries/new')
        page.find('.ci-dataset-mode').click
        wait_for_element_load('.ci-data-set-select')
        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        expect(page.find('.ci-limit-select-toggle').text).to eq 'Limit 5000'
      end

      it 'has tenant-configed row limit by default when FT is enabled' do
        FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, true)
        admin.tenant.update(settings: { viz: { default_records_limit: 20000 } })

        safe_login(admin, '/queries/new')
        page.find('.ci-dataset-mode').click
        wait_for_element_load('.ci-data-set-select')
        select_h_select_option('.ci-data-set-select', value: "#{dataset.id}ds")
        expect(page.find('.ci-limit-select-toggle').text).to eq 'Limit 20000'
      end
    end
  end

  context 'In edit report >' do
    describe 'Edit filter >' do
      it_behaves_like 'adhoc filter new report' do
        let(:new_date_value) { '1990-12-30' }
        let(:user) { :admin }
        let(:path) { "/queries/#{report.id}/edit" }
        let(:filter_index) { 0 }
        let(:filter_mode) { 'edit' }
      end

      it_behaves_like 'shared filter new report' do
        let(:new_text_value) { 'ahihihihi' }
        let(:user) { :admin }
        let(:path) { "/queries/#{report.id}/edit" }
        let(:filter_index) { 1 }
        let(:filter_mode) { 'edit' }
      end
    end

    describe 'Discard confirmation' do
      let(:report_url) { query_report_path(report) }

      def click_discard_button
        wait_for_element_load('.ci-discard')
        page.find('.ci-discard').click
      end

      def confirm_discard
        wait_for_element_load('.confirm-modal')
        page.find('.confirm-modal .ci-confirm').click
      end

      def fill_in_editor(_value)
        wait_for_editor
        fill_in_ace('.editor', 'select 1')
      end

      def expect_goto_report_page
        sleep 2 # wait for loading page
        wait_for_element_load('.ci-edit-report-link')
        expect(page).to have_selector('.ci-edit-report-link')
      end

      before(:each) do
        safe_login(admin, report_url)
        wait_for_element_load('.ci-filter-list')
        safe_click('.ci-edit-report-link')
        wait_for_all_ajax_requests
      end

      it 'should confirm discard when report is dirty' do
        fill_in_editor('select 1')
        click_discard_button
        confirm_discard
        expect_goto_report_page
      end

      it 'should save normally when report is dirty' do
        fill_in_editor('select 1')
        find('.ci-save-report').click
        expect_goto_report_page
      end

      it 'should close immediately if report stay the same' do
        click_discard_button
        expect_goto_report_page
      end
    end

    describe 'New filter >' do
      it 'should show error duplicated variable' do
        safe_login(admin, "/queries/#{report.id}/edit")
        wait_for_editor

        expand_filters_panel
        wait_for_all_filter_loaded

        # select adhoc filter
        page.find('.add-filter-button').click
        page.find('.hui-dropdown-floating .ci-filter-type-0').click
        wait_expect(1) { page.all('.ci-modal-edit-filter').count }

        select_h_select_option('.ci-select-item', value: 'input')
        # case missing variable
        wait_expect('Please enter variable name') { page.first('.h-form-invalid-feedback', visible: true).text }

        # case duplicated variable
        page.find(:xpath, "//label[contains(text(), 'Variable Name')]/../div/input").set('adhoc_1')
        wait_expect('Filter variable name "adhoc_1" is already used') { page.find('.h-form-invalid-feedback').text }

        # ok
        find(:xpath, "//label[contains(text(), 'Variable Name')]/../div/input").set('adhoc_2')
        page.find('.ci-shared-filter-save').click
      end

      it 'shared filters should not generate duplicate variables' do
        safe_login(admin, "/queries/#{report.id}/edit")
        wait_for_editor
        clear_page_unload_events

        expand_filters_panel
        wait_for_all_filter_loaded

        # Add 2 shared filters
        page.first('.add-filter-button').click
        page.find('.hui-dropdown-floating .ci-filter-templates').hover
        page.first('.items-list div').click

        sleep 1
        page.first('.add-filter-button').click
        page.find('.hui-dropdown-floating .ci-filter-templates').hover
        page.first('.items-list div').click

        # validate to make sure there is no duplicated variable
        variables = page.all('.ci-edit-filter-link').map(&:text)
        expect(variables.uniq.length).to eq(variables.length)
      end

      it 'should able to choose parent dropdown' do
        safe_login(admin, "/queries/#{report.id}/edit")
        wait_for_editor

        expand_filters_panel
        wait_for_all_filter_loaded

        # Add parent dropdown
        parent_variable = 'dd_parent'
        page.find('.add-filter-button').click
        page.find('.hui-dropdown-floating .ci-filter-type-2').click
        wait_for_element_load('.modal-dialog')
        wait_expect(1) { page.all('.ci-modal-edit-filter').count }

        # set parent variable name
        wait_expect(parent_variable) do
          # keep filling until filled https://stackoverflow.com/a/45332510/9093051
          find(:xpath, "//label[contains(text(), 'Variable Name')]/../div/input").set(parent_variable)
          find(:xpath, "//label[contains(text(), 'Variable Name')]/../div/input").value
        end
        page.find('.ci-shared-filter-save').click

        # Add child dropdown
        safe_click('.add-filter-button')
        page.find('.hui-dropdown-floating .ci-filter-type-2').click
        wait_for_element_load('.modal-dialog')
        wait_expect(1) { page.all('.ci-modal-edit-filter').count }

        # set parent variable name
        child_variable = 'dropdown_child'
        wait_expect(child_variable) do
          # keep filling until filled https://stackoverflow.com/a/45332510/9093051
          find(:xpath, "//label[contains(text(), 'Variable Name')]/../div/input").set(child_variable)
          find(:xpath, "//label[contains(text(), 'Variable Name')]/../div/input").value
        end

        # check child filter checkbox
        h_checkbox_check(label: 'This is a child filter')
        # parent dropdown should be shown in list
        wait_for_element_load('.ci-select-parent')
        wait_expect(true) do
          dropdown_parent_list = find('.ci-select-parent').all('option').collect(&:text).map(&:strip)
          puts dropdown_parent_list
          dropdown_parent_list.include?(parent_variable)
        end

        # select parent
        find('.ci-select-parent').first('option').click

        # update options
        page.find('.table-records').double_click
        page.find('.ci-dropdown-manual-textarea').set("opt_1,Child Option 1,opt_1\nopt_2,Child Option 2,opt_2\nopt_3,Child Option 3,opt_3")
        page.find('.ci-shared-filter-save').click

        expand_filters_panel

        # validate select parent option will trigger child options
        wait_for_element_load do
          page.all(:xpath, "//a[contains(text(), '#{parent_variable}')]/../../div[contains(@class,'ci-report-filter-input')]/span").present?
        end
        safe_click_element(
          page.find(:xpath, "//a[contains(text(), '#{parent_variable}')]/../../div[contains(@class,'ci-report-filter-input')]/span")
        )

        # select first option
        page.first('.select2-container ul li').click
        wait_for_element_load do
          page.all(:xpath, "//a[contains(text(), '#{child_variable}')]/../../div[contains(@class,'ci-report-filter-input')]/span").present?
        end
        page.find(:xpath, "//a[contains(text(), '#{child_variable}')]/../../div[contains(@class,'ci-report-filter-input')]/span").click
        options = page.all('.select2-container ul li').collect(&:text)

        expect(options.length).to eq(1)
        expect(options[0]).to eq('Child Option 1')

        # Save report
        page.find('.ci-save-report').click
        sleep(3) # wait for loading page
        wait_for_all_filter_loaded

        # validate select parent option will trigger child options
        page.find('.ci-single-dropdown-filter', match: :first).click

        # select first option
        page.find('.select2-results__option', match: :first).click

        # Validate child filter's options
        page.all('.ci-single-dropdown-filter').last.click
        options = page.all('.select2-results__option').collect(&:text)

        expect(options.length).to eq(1)
        expect(options[0]).to eq('Child Option 1')
      end
    end

    describe 'viz setting' do
      it 'keep viz setting fields even when query is changed' do
        report.update(query: 'select 1 a, 2 b, 3 c')
        safe_login(admin, "/queries/#{report.id}/edit")
        clear_page_unload_events

        wait_for_editor

        sleep 1
        page.first('.ci-btn-run').click
        wait_for_viz_load
        adhoc_query_model = DataModel.where(backend_type: 'PgcacheModel').first
        wait_for_element_load('.ci-table-report-data')

        page.first('.ci-viz-type-select').click
        page.first('.ci-viz-type-line_chart').click
        page.first('.ci-viz-setting-tab-settings').click
        wait_for_element_load('.ci-x-axis-row')

        page.all('.ci-y-axis-row .h-icon[data-icon="cancel"]').last.click

        sleep 1
        select_h_select_option('.ci-add-y-axis-row', value: "1$!b")

        sleep 1
        page.first('.top-header').click # just clicking somewhere to hide the select panel
        page.first('.ci-add-y_axis').click

        sleep 5
        page.first('.ci-btn-run').click
        wait_for_element_load('.ci-highcharts')
        expect(page.all('.ci-add-y-axis-row').count).to eq 2
        page.all('.viz-setting-form .ci-viz-field-select').each do |field|
          expect(field.text).not_to include('Invalid field')
        end
      end
    end

    describe 'rendering big numbers' do
      include_context 'query_model_dataset_report_with_bignumbers'
      it 'can render big numbers properly' do
        safe_login admin, query_report_path(query_model_dataset_based_report)
        wait_for_element_load('.ci-table-report-data')
        wait_expect(['m', '2', '23845621292002029.69']) do
          page.all('.ag-row:nth-child(1) .ag-cell')[1..].map(&:text)
        end
        wait_expect(['m', '9', '23845621290950143']) do
          page.all('.ag-row:nth-child(2) .ag-cell')[1..].map(&:text)
        end
        wait_expect(['f', '10', '23845621290950143']) do
          page.all('.ag-row:nth-child(3) .ag-cell')[1..].map(&:text)
        end
        wait_expect(['m', '23845621290950143.23845621290950143', '1771846']) do
          page.all('.ag-row:nth-child(6) .ag-cell')[1..].map(&:text)
        end
        wait_expect(['f', '7', '803275.23845621290950143']) do
          page.all('.ag-row:nth-child(7) .ag-cell')[1..].map(&:text)
        end
      end
    end
  end
end
