# typed: false
require 'rails_helper'

describe 'query editor autocomplete', js: true, stable: true do
  let(:admin) { get_test_admin }

  before do
    FeatureToggle.toggle_global('autocomplete', true)
  end

  after do
    page.evaluate_script 'window.onbeforeunload = undefined'
    page.evaluate_script 'window.onpopstate = undefined'
  end

  def test_autocomplete(text)
    safe_click('.ace_content')
    fill_in_ace('', text)
    wait_expect(true) { page.has_css?('.ace_autocomplete') }
  end

  describe 'report editor' do
    it 'suggests autocomplete correctly' do
      safe_login(admin, '/queries/new')
      test_autocomplete('se')
    end
  end

  describe 'metrics editor' do
    it 'suggests autocomplete correctly' do
      safe_login(admin, '/metrics/manage')
      safe_click('.ci-add-metric')
      test_autocomplete("\nse")
    end
  end

  describe 'adhoc editor' do
    it 'suggests autocomplete correctly' do
      safe_login(admin, '/adhoc/query_editor')
      test_autocomplete('se')
    end
  end
end
