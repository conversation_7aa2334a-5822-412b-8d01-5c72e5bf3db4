# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'pin items do', :js, stable: true do
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global(Dashboard::FT_PIN_DASHBOARD, true)
  end

  let(:ds) { get_test_ds }
  let(:admin) { get_test_admin }
  let!(:category) { create(:report_category, name: 'aihhi') }
  let!(:dashboard) { create(:dashboard, version: 3, title: 'pinned dashboard', category: category) }

  def expect_no_pinned_items
    # no pinned item on homepage
    expect(page).not_to have_content('Pinned')
  end

  def toggle_pin_dashboard
    visit dashboard_path(dashboard)
    wait_for_element_load('.ci-preferences-toggle')
    safe_click('.ci-preferences-toggle')
    safe_click('.ci-toggle-pin-dashboard')
    sleep 0.5
  end

  def expect_have_pinned_item
    visit '/home'
    wait_for_element_load('[data-ci="ci-pinned-items-section"]')

    expect(page.find('[data-ci="ci-pinned-items-section"]')).to have_content(dashboard.title)
  end

  it 'successfully pin and unpin dashboard' do
    safe_login(admin, '/home')
    expect_no_pinned_items

    # pin dashboard
    toggle_pin_dashboard

    # auto open suggest edit user access modal
    wait_for_element_load("[data-ci='ci-suggest-edit-user-access-modal']")
    safe_click("[data-ci='ci-skip-edit-user-access-btn']")
    safe_click('[data-ci="ci-toasts-top"] [data-ci="ci-toast"] button:has(.h-icon[data-icon="cancel"])')

    # page.find("[data-ci='ci-icon-pinned-dashboard']").hover
    reliable_hover("[data-ci='ci-icon-pinned-dashboard']", '.ci-dashboard-pinned-info')
    wait_for_element_load('.ci-dashboard-pinned-info')
    expect(page.find('.ci-dashboard-pinned-info')).to have_content('This Dashboard is pinned to the Org Homepage')

    expect_have_pinned_item

    # unpin dashboard
    toggle_pin_dashboard
    visit '/home'
    expect_no_pinned_items
  end

  it 'opens edit user access modal if admin want to share to more users after pinned dashboard' do
    safe_login(admin, '/home')
    toggle_pin_dashboard

    wait_for_element_load("[data-ci='ci-suggest-edit-user-access-modal']")
    safe_click("[data-ci='ci-edit-user-access-btn']")
    wait_for_element_load '.ci-share-select'

    # share to all users
    select_h_select_option('.ci-share-select', label: 'All users')
    safe_click('.ci-share-btn')
    page.first('.ci-modal-close.close-btn').click

    # unpin dashboard
    safe_click('.ci-preferences-toggle')
    safe_click('.ci-toggle-pin-dashboard')

    # and pin again
    safe_click('.ci-preferences-toggle')
    safe_click('.ci-toggle-pin-dashboard')

    # does not show suggest edit user access modal when all users adready been shared
    expect(page.all("[data-ci='ci-suggest-edit-user-access-modal']").count).to eq(0)
  end

  it 'successfully removed from pinned items section when item is deleted' do
    safe_login(admin, '/home')
    expect_no_pinned_items

    toggle_pin_dashboard
    expect_have_pinned_item

    # delete folder containing dashboard
    visit '/home'
    wait_for_element_load('.report-container')
    page.find('.ci-public-workspace').click
    wait_for_element_load('.node-browse')
    page.first('.ci-actions').click
    page.first('.ci-delete').click
    page.first('.ci-confirm-delete').click
    wait_for_element_load('.modal-delete-category')
    page.first('.ci-delete-mode').click
    fill_text('.ci-confirm-field', 'DELETE')
    page.find('.ci-submit').click

    visit '/home'
    expect_no_pinned_items
    expect(PinnedItem.count).to eq(0)
  end
end
