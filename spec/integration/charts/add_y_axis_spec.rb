# typed: false
require 'rails_helper'

describe 'Add y-axis test', js: true, stable: true do
  let(:report) do
    sql = <<-SQL.strip_heredoc
        values(1, 30, 50), (2, 34, 55), (3, 45, 76), (4, 80, 145)
    SQL
    FactoryBot.create :query_report, title: 'Test Duplicated add y-axis',
                      query: sql,
                      viz: {
                        "has_viz" => true,
                        "viz_type" => "chart",
                        "chart_dsl" =>
                          { "yAxes" => [{ "align" => "left", "title" => "ali", "scale_type" => "linear" }],
                            "series" =>
                              [{ "type" => "auto", "color" => "auto", "field_index" => 0, "yaxis_index" => "0" },
                               { "type" => "auto", "color" => "auto", "field_index" => 1, "yaxis_index" => "0" },
                               { "type" => "auto", "color" => "auto", "field_index" => 2, "yaxis_index" => "0" }] },
                        "chart_type" => "line",
                        "chart_options" => { "x_axis_columns" => [0], "y_axis_columns" => [0, 1, 2] }
                      }
  end
  let(:axis) do
    { title: 'toyota' }
  end

  it 'add y-axis not duplicated' do
    safe_login(:admin, edit_query_report_path(report))

    wait_for_element_load('.ci-report-preview:not([disabled])')
    safe_click('.h-tab.active .ci-report-preview')

    safe_click('.ci-viz-tab')
    wait_for_all_holistics_loadings

    # click axis tab in viz editor
    safe_click('.ci-viz-axis')
    safe_click('.ci-add-axis')
    safe_click('.ci-add-axis')
    page.all('.axis-selectors:nth-child(2) tbody input.ci-axis-title')[1].set(axis[:title])
    title1 = page.all('.axis-selectors:nth-child(2) tbody input.ci-axis-title')[1].value()
    title2 = page.all('.axis-selectors:nth-child(2) tbody input.ci-axis-title')[2].value()

    expect(title1).to_not eq title2
  end
  context 'explore dataset' do
    include_context 'data_set'

    before do
      FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
      FeatureToggle.toggle_global('data_sets:custom_expression', true)
      FeatureToggle.toggle_global('data_models:custom_field', true)
    end
    let!(:viz_setting) do
      create(
        :viz_setting,
        tenant: admin.tenant,
        viz_type: 'pivot_table',
        adhoc_fields: [
          {
            aggregation_type: 'custom',
            description: '',
            fieldType: 'measure',
            is_custom: true,
            is_custom_measure: true,
            is_external: false,
            is_hidden: false,
            label: 'count(data_modeling_products.name)',
            model: {},
            name: 'f_7984e57',
            sql: 'count(data_modeling_products.name)',
            syntax: 'aml',
            type: 'number',
          },
        ],
        fields: {
          pivot_data: {
            columns: [],
            rows: [],
            values: [
              {
                aggregation: 'custom',
                format: {
                  format: {
                    pattern: 'inherited',
                  },
                  type: 'number',
                },
                path_hash: {
                  field_name: 'f_7984e57',
                },
                type: 'number',
              },
            ],
          },
        }
      )
    end
    it 'should keep biz cal label after changing viz type' do
      safe_login(admin, data_set_path(data_set))
      page.find('.ci-viz-type-pivot_table').click
      page.find('.pivot-section-values .ci-empty-field').click
      page.find('.ci-add-business-calc').click

      fill_text('.ci-label-input', 'count(data_modeling_products.name)')
      fill_in_monaco('#ci-definition-input', 'count(data_modeling_products.name)')

      safe_click('.ci-submit')
      wait_for_all_ajax_requests

      safe_click('.ci-explorer-control-get-results')
      wait_for_all_ajax_requests
      expect(page.find('.ci-viz-result').text).to include('count(data_modeling_products.name)')

      page.find('.ci-viz-type-line_chart').click

      safe_click('.ci-explorer-control-get-results')
      wait_for_all_ajax_requests
      expect(page.find('.ci-viz-result').text).to include('count(data_modeling_products.name)')
    end
  end
end
