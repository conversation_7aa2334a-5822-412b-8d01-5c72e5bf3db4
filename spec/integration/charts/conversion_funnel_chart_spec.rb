# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'New Conversion Funnel chart in dashboard widget', js: true, stable: true do
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('data_models:manager', true)
    FeatureToggle.toggle_global('data_models:explore_controls', true)
  end
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }
  let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
  let(:report) { FactoryBot.create :new_conversion_funnel_report }
  let!(:dashboard_widget) do
    FactoryBot.create :dashboard_widget, source: report, dashboard: dashboard
  end
  let(:url) { dashboard_path(dashboard) }

  it 'should not have old Conversion Funnel icon and should have new Settings' do
    safe_login(admin, url)
    open_data_exploration
    expect(page).not_to have_css('.ci-viz-type-conversion_funnel')
    expect(page).to have_css('.viz-section-header', exact_text: 'Break down')
    expect(page).to have_css('.viz-section-header', exact_text: 'Values')
    expect(page).not_to have_css('.viz-section-header', exact_text: 'X Axis')
    expect(page).not_to have_css('.viz-section-header', exact_text: 'Legend')
  end

  context 're-ordering Y-Axis columns' do
    it 'should reflect changes to columns and percentage table of chart' do
      safe_login(admin, url)
      open_data_exploration
      expect(chart_x_axis_labels[0]).to eq 'Sum of sold'
      expect(chart_x_axis_labels[1]).to eq 'Sum of active'
      expect(summary_row).to eq ['All 389132 (100%) 205838 (52.9%) 198332 (96.35%)']

      first_y = page.all('.ci-y-axis-row').first
      second_y = page.all('.ci-y-axis-row')[1]
      first_y.drag_to(second_y, delay: 0.25)
      first_y.click
      wait_for_element_load('.ci-data-exploration-modal .ci-viz-result')
      sleep 0.5
      expect(chart_x_axis_labels[0]).to eq 'Sum of active'
      expect(chart_x_axis_labels[1]).to eq 'Sum of sold'
      expect(summary_row).to eq ['All 205838 (100%) 389132 (189.05%) 198332 (50.97%)']
    end
  end

  context 'sorting by Y-Axis' do
    it 'should reflect order of columns for selected row correctly to chart and data table' do
      safe_login(admin, url)
      sleep 5
      open_data_exploration
      expect(chart_x_axis_labels[0]).to eq 'Sum of sold'
      expect(chart_x_axis_labels[1]).to eq 'Sum of active'
      expect(chart_x_axis_labels[2]).to eq 'Sum of closed'
      expect(data_table_headers).to eq ['month', 'Sum of sold', 'Sum of active', 'Sum of closed']
      expect(summary_row).to eq ['All 389132 (100%) 205838 (52.9%) 198332 (96.35%)']
      expect(april_row).to eq ['Month 4 106648 (100%) 43454 (40.75%) 50077 (115.24%)']

      safe_click('.hc-action-menu')
      page.find('.ci-chart-sort').hover
      safe_click('.ci-chart-asc')
      expect(chart_x_axis_labels[0]).to eq 'Sum of closed'
      expect(chart_x_axis_labels[1]).to eq 'Sum of active'
      expect(chart_x_axis_labels[2]).to eq 'Sum of sold'
      expect(data_table_headers).to eq ['month', 'Sum of closed', 'Sum of active', 'Sum of sold']
      expect(summary_row).to eq ['All 198332 (100%) 205838 (103.78%) 389132 (189.05%)']
      expect(april_row).to eq ['Month 4 50077 (100%) 43454 (86.77%) 106648 (245.43%)']

      page.find('.ci-data-exploration-modal .ci-viz-result table td', text: 'Month 4')
          .click
      expect(chart_x_axis_labels[0]).to eq 'Sum of closed'
      expect(chart_x_axis_labels[1]).to eq 'Sum of active'
      expect(chart_x_axis_labels[2]).to eq 'Sum of sold'

      safe_click('.hc-action-menu')
      page.find('.ci-chart-sort').hover
      safe_click('.ci-chart-asc')
      expect(chart_x_axis_labels[0]).to eq 'Sum of active'
      expect(chart_x_axis_labels[1]).to eq 'Sum of closed'
      expect(chart_x_axis_labels[2]).to eq 'Sum of sold'
      expect(data_table_headers).to eq ['month', 'Sum of active', 'Sum of closed', 'Sum of sold']
      expect(summary_row).to eq ['All 205838 (100%) 198332 (96.35%) 389132 (196.2%)']
      expect(april_row).to eq ['Month 4 43454 (100%) 50077 (115.24%) 106648 (212.97%)']
    end
  end
end

describe 'Old Conversion Funnel chart in dashboard widget', js: true do
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('data_models:manager', true)
    FeatureToggle.toggle_global('data_models:explore_controls', true)
  end
  let(:tenant) { get_test_tenant }
  let(:admin) { get_test_admin }
  let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
  let(:report) { FactoryBot.create :conversion_funnel_report }
  let!(:dashboard_widget) do
    FactoryBot.create :dashboard_widget, source: report, dashboard: dashboard
  end
  let(:url) { dashboard_path(dashboard) }

  it 'should not have new Conversion Funnel icon and should have old Settings' do
    safe_login(admin, url)
    open_data_exploration
    expect(page).not_to have_css('.ci-viz-type-new_conversion_funnel')
    expect(page).not_to have_css('.viz-section-header', exact_text: 'Break down')
    expect(page).not_to have_css('.viz-section-header', exact_text: 'Values')
    expect(page).to have_css('.viz-section-header', exact_text: 'X Axis')
    expect(page).to have_css('.viz-section-header', exact_text: 'Legend')
  end
end

def open_data_exploration
  wait_for_element_load('.conversion-funnel, .new-conversion-funnel')
  page.find('.ci-report-widget').hover
  safe_click('.hui-btn [data-icon="explore"]')
end

def summary_row
  page.all('.ci-data-exploration-modal .ci-viz-result table tbody tr', text: 'All')
      .map(&:text)
end

def april_row
  page.all('.ci-data-exploration-modal .ci-viz-result table tbody tr', text: 'Month 4')
      .map(&:text)
end

def chart_x_axis_labels
  page.all('.ci-data-exploration-modal .highcharts-xaxis-labels text')
      .map(&:text)
end

def data_table_headers
  page.all('.ci-data-exploration-modal .ci-viz-result table thead tr th')
      .map(&:text)
end
