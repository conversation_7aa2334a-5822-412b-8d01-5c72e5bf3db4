# typed: false
require 'rails_helper'

describe 'Chart', js: true, stable: true do
  def visit_report(rp)
    qlogin(:admin, query_report_path(rp))
    sleep 0.5
  end

  context 'when report has date x-axis column with null values' do
    let (:report) {
      sql = <<~SQL
        -- please see https://app.asana.com/0/161816130717323/227248002688227 for more details
        select *
        from (
          select * from (
            values
            ('2016-10-01'::date, 18334),
            ('2016-09-01'::date, 30685),
            ('2016-08-01'::date, 24658),
            ('2016-07-01'::date, 8842),
            ('2016-04-01'::date, 8709),
            ('2013-06-01'::date, 9971),
            ('2013-05-01'::date, 9476),
            ('2013-04-01'::date, 0),
            ('2013-03-01'::date, 0),
            ('2013-02-01'::date, 0),
            ('2013-01-01'::date, 0)
          ) t
          union
          select date(null), 3603
        ) t
        order by 1
      SQL

      FactoryBot.create :query_report, query: sql,
         settings: {
           "column_types"=>["date", "auto"],
           "column_options"=>
             [{"name"=>"column1",
               "date_format"=>"mmm dd yyyy",
               "number_format"=>"auto",
               "string_format"=>"string"},
              {"name"=>"column2",
               "date_format"=>"mmm dd yyyy",
               "number_format"=>"auto",
               "string_format"=>"string"}]
         },
         viz: {
           has_viz: true, viz_type: 'chart',
           "chart_dsl"=>
             {"yAxes"=>[{"align"=>"left"}],
              "series"=>
                [{"type"=>"auto",
                  "color"=>"auto",
                  "field_index"=>1,
                  "yaxis_index"=>"0"}],
              "settings"=>{"data_label_on_chart"=>true}},
           "chart_type"=>"area",
           "chart_options"=>{"x_axis_columns"=>[0], "y_axis_columns"=>[1]}
         }
    }

    it 'should display chart error about date x-axis values' do
      visit_report(report)
      wait_for_element_load('.chart')
      err_message = page.find('.chart').text
      expect(err_message).to include 'Chart Error: Date x-axis has invalid values (e.g. null values)'
    end
  end

  context 'for bubble chart' do
     let (:report) {
       FactoryBot.create :query_report, query: "values (1, 2, 3), (4, 5, 6)",
        viz: {
          "has_viz"=>true,
          "viz_type"=>"chart",
          "chart_dsl"=>
            {"yAxes"=>[{"align"=>"left"}],
             "series"=>
               [{"type"=>"auto",
                 "color"=>"auto",
                 "field_index"=>1,
                 "yaxis_index"=>"0"}]},
          "chart_type"=>"bubble",
          "chart_options"=>{"x_axis_columns"=>[0], "y_axis_columns"=>[1, 2]}
        },
        settings: {
          "column_types"=>["auto", "auto", "auto"],
          "column_options"=>
            [{ "name"=>"column1", "number_format"=>"auto" },
             { "name"=>"column2", "number_format"=>"auto" },
             {"name"=>"column3", "number_format"=>"auto" }]
        }
     }

     it 'should display bubble circles' do
       visit_report(report)
       wait_for_element_load('.highcharts-bubble-series .highcharts-color-0')
       expect(page.all('.highcharts-bubble-series .highcharts-color-0').count).to eq 2
     end
  end

  # https://app.asana.com/0/76997687380943/241876808065694
  context 'when report use display percentage option' do
    let (:report) {
      sql = <<~SQL
        select column1 as week_date, column2 as app_version, column3 as total
        from (
          values
          ('Dec 26 2016', '1.9.5', 5),
          ('Dec 26 2016', '1.9.3', 1),
          ('Jan 02 2017', '1.9.5', 34),
          ('Jan 02 2017', '1.9.2', 9),
          ('Jan 02 2017', '1.9.7', 7),
          ('Jan 02 2017', '1.9.3', 6),
          ('Dec 26 2016', '1.9.7', 1),
          ('Jan 09 2017', '1.9.2', 2),
          ('Dec 26 2016', '1.9.8', 436),
          ('Jan 02 2017', '1.9.8', 2044),
          ('Jan 09 2017', '1.9.8', 279),
          ('Jan 09 2017', '1.9.5', 5),
          ('Dec 26 2016', '1.9.2', 3),
          ('Jan 09 2017', '1.9.7', 1)
         ) as f
      SQL

      FactoryBot.create :query_report, query: sql,
                         settings: {
                           'quick_pivot' => true,
                           "column_types"=>["auto", "auto", "auto"],
                           "column_options"=>
                             [{"name"=>"week_date",
                               "date_format"=>"mmm dd yyyy",
                               "number_format"=>"auto",
                               "string_format"=>"string"},
                              {"name"=>"app_version",
                               "date_format"=>"mmm dd yyyy",
                               "number_format"=>"auto",
                               "string_format"=>"string"},
                              {"name"=>"total",
                               "date_format"=>"mmm dd yyyy",
                               "number_format"=>"auto",
                               "string_format"=>"string"}]
                         },
                         viz: {
                           "has_viz"=>true,
                           "viz_type"=>"chart",
                           "chart_dsl"=>
                             {"yAxes"=>[{"align"=>"left"}],
                              "series"=>
                                [{"type"=>"auto", "color"=>"auto", "field_index"=>1, "yaxis_index"=>"0"},
                                 {"type"=>"auto", "color"=>"auto", "field_index"=>2, "yaxis_index"=>"0"},
                                 {"type"=>"auto", "color"=>"auto", "field_index"=>3, "yaxis_index"=>"0"},
                                 {"type"=>"auto", "color"=>"auto", "field_index"=>4, "yaxis_index"=>"0"},
                                 {"type"=>"auto", "color"=>"auto", "field_index"=>5, "yaxis_index"=>"0"}],
                              "settings"=>
                                {"stacking"=>{"column"=>true},
                                 "data_label_on_chart"=>true,
                                 "display_percentages"=>true}},
                           "chart_type"=>"column",
                           "chart_options"=>{"x_axis_columns"=>[0], "y_axis_columns"=>[1, 2]}
                         }
    }

    it 'should display tooltip and data label correctly' do
      visit_report(report)

      wait_for_element_load('.highcharts-container .highcharts-data-labels.highcharts-series-4')
      fifth_series_data_labels = page.all('.highcharts-container .highcharts-data-labels.highcharts-series-4 tspan.highcharts-text-outline')
                                   .map { |node| node.text }

      expect(fifth_series_data_labels).to eq %w(97.76% 97.33% 97.21%)

      # double hover to render tooltip DOM of first col
      page.first('.highcharts-container .highcharts-series.highcharts-series-0.highcharts-tracker path').hover
      page.first('.highcharts-container .highcharts-series.highcharts-series-4.highcharts-tracker path').hover

      first_col_tooltip_content = page.find('.highcharts-tooltip-container .highcharts-tooltip .tooltip-container').text(normalize_ws: true)
      expect(first_col_tooltip_content).to eq 'Dec 26 2016 ● 1.9.2: 0.67% ● 1.9.3: 0.22% ● 1.9.5: 1.12% ● 1.9.7: 0.22% ● 1.9.8: 97.76%'
    end
  end

  it 'world clould chart report should render correctly' do
    world_cloud_report = FactoryBot.create :world_cloud_report

    visit_report(world_cloud_report)

    wait_for_element_load('.highchart')
    expect(page).to have_selector('.highcharts-container')
  end

  it 'conversion funnel chart should render correctly' do
    conversion_funnel_report = FactoryBot.create :conversion_funnel_report

    visit_report(conversion_funnel_report)

    wait_for_element_load('.highchart')
    expect(page).to have_selector('.highcharts-container')
    expect(page).to have_selector('.highcharts-conversion-data .conversion-funnel-arrow')
  end
end
