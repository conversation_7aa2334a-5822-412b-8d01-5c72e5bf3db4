# frozen_string_literal: true

# typed: false
require 'rails_helper'

describe 'report preferences', js: true, legacy: true do
  let!(:current_user) { get_test_admin }
  let(:report) { FactoryBot.create :query_report_with_cache_info, owner: current_user }

  describe 'Preference loaded tab' do
    it 'should load general tab' do
      open_general_tab
      wait_for_element_load('.tab-link-selected')
      expect(page.first('.tab-link-selected > a').text).to eq('General')
    end

    it 'should load cache setting tab' do
      open_caching_settings_tab
      wait_for_element_load('.tab-link-selected')
      expect(page.first('.tab-link-selected > a').text).to eq('Cache Settings')
    end
  end

  describe 'General tab' do
    it 'update metadata' do
      new_title = 'new_title'
      new_desc = 'new_desc'
      open_general_tab
      # Button save should be disable
      save_changes_btn = page.find('.btn-save-general-settings')
      wait_for { save_changes_btn.disabled? }
      # Update title & description
      page.find('.setting-input > input.ci-title').set(new_title)
      page.find('.ci-desc').set(new_desc)
      # Button save should be enable (data modified)
      wait_for { !save_changes_btn.disabled? }
      save_changes_btn.click
      # wait for success response
      wait_for { save_changes_btn.disabled? }
      # check report status
      report.reload
      expect(report.title).to eq(new_title)
      expect(report.description).to eq(new_desc)
    end
  end

  describe 'Cache Settings tab' do
    before(:each) do
      open_caching_settings_tab
    end

    it 'Change caching duration' do
      # Select duration (6 hours)
      select_h_select_option('.ci-cache-duration', label: '6 hours')
      wait_expect(360) do
        report.reload
        report.cache_setting.duration
      end
    end

    it 'Switch on/off auto preload' do
      auto_preload_section = page.all('.cache-setting-input')[1]
      toggle_auto_preload = auto_preload_section.first('.ci-auto-preload')
      # Disable
      toggle_auto_preload.click
      wait_expect(true) do
        report.reload
        report.scheduled_caches[0].schedule.paused
      end
      # Enable
      toggle_auto_preload.click
      wait_expect(false) do
        report.reload
        report.scheduled_caches[0].schedule.paused
      end
    end

    it 'Change auto preload time' do
      auto_preload_section = page.all('.cache-setting-input')[1]
      page.find('.h-modal').click
      page.find('.preload-time-selector').click
      wait_for_element_load('.ci-ss-repeatby')
      expect(page.find('.ci-ss-repeatby').text) == 'Every'
      select_h_select_option('.ci-ss-repeatby', value: 'Hourly')
      page.find('.ci-schedule-done').click
      wait_expect('0 * * * *') do
        report.reload
        report.scheduled_caches[0].schedule.repeat
      end
    end
  end

  def open_general_tab
    safe_login(current_user, "/queries/#{report.id}")
    wait_for_element_load('.ci-toggle')
    page.find('.ci-toggle').click
    # Select first item 'Preference' in option lists
    page.find('.v-popper__inner li:first-child').click
    wait_for_element_load('.btn-save-general-settings')
  end

  def open_caching_settings_tab
    safe_login(current_user, "/queries/#{report.id}")
    wait_for_element_load('.cache-status')
    page.find('.cache-status').hover
    # Click cache status
    wait_for_element_load('.cache-status__popover')
    page.find('.cache-status__popover').click_button('Cache Settings')
    wait_for_element_load('.cache-setting-input')
  end
end
