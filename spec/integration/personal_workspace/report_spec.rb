# typed: false
require 'rails_helper'

describe 'report in personal workspace', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:admin1) { get_test_admin1 }
  let(:admin3) { get_test_admin3 }
  let(:analyst) { get_test_analyst }
  let(:biz_user) { get_test_user }
  let(:source_table) { FQName.new schema_name: 'data_modeling', table_name: 'test_data_modeling_table' }

  let(:group) { FactoryBot.create :group, tenant_id: admin.tenant_id }
  let!(:group_membership) { FactoryBot.create :group_membership, user: admin1, group: group, tenant_id: admin.tenant_id }
  let!(:group_membership1) { FactoryBot.create :group_membership, user: biz_user, group: group, tenant_id: admin.tenant_id }

  include_context 'data_modeling_schema'
  let(:sql) { 'select generate_series(1, 25) as num' }

  before do
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global('data_source:enable_schema_info', true)
    FeatureToggle.toggle_global('schema_synchronization', true)
    FeatureToggle.toggle_global('data_models:manager', true)
    create_test_data_modeling_table(source_table)
    DataSourceVersions::SchemaSynchronizationService.new(get_test_ds).execute
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
  end

  def select_field(name)
    all('.ci-viz-field-select.empty').first.click
    sleep 0.1
    field_input = all('.ci-viz-field-select.empty .vue-treeselect__input').first
    name.each_char do |char|
      field_input.native.send_keys(char)
    end
    sleep 0.5
    field_input.native.send_keys(:return)
  end

  def create_report_using_modal
    page.first('.ci-title input').set('report 1')
    folder_select = page.first('.ci-folder .vue-treeselect__input')

    'private'.each_char do |char|
      folder_select.native.send_keys(char)
    end
    sleep 1
    folder_select.native.send_keys(:return)
    sleep 1

    first('.ci-confirm').click
    sleep 1

    visit first('a.ci-report-link')['href'].to_s
    sleep 1
    wait_expect('report 1') { page.find('.ci-report-title').text }
  end

  describe 'create', js: true do
    describe 'from data set' do
      let!(:dataset) do
        create(
          :data_set,
          root_model_id: create_real_data_model_from_table(get_test_ds, source_table.table_name).id
        )
      end

      xit 'should be success' do
        qlogin(admin, "/datasets/#{dataset.id}")
        wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')
        select_field('id')
        select_field('body')
        wait_for_element_load('#ci-save-result-as')
        find('#ci-save-result-as').click
        sleep 1
        create_report_using_modal
        expect(PersonalItem.count).to eq(1)
      end
    end

    describe 'from adhoc sql editor' do
      xit 'should be success' do
        qlogin(:admin, '/adhoc/query_editor')
        wait_for_element_load('#adhoc-editor')
        fill_in_ace('#adhoc-editor', 'select 1,2,3;')
        page.find('.ci-run-query').click
        safe_click('.ci-save-query-result')
        create_report_using_modal
        expect(PersonalItem.count).to eq(1)
      end
    end

    describe 'from Create new report' do
      xit 'should be success' do
        qlogin(:admin, '/queries/new?category=0&categoryType=PersonalCategory')
        wait_for_element_load('#awesome-query-editor')
        fill_in_ace('#awesome-query-editor', 'select 1,2,3;')
        first('.ci-report-name').click
        first('.ci-title-input').set('report 1')
        page.find('.ci-save-report').click
        sleep 2
        page.driver.browser.navigate.refresh
        wait_for_element_load('.ci-report-title')
        expect(find('.ci-report-title').text).to eq('report 1')
        expect(PersonalItem.count).to eq(1)
      end
    end

    describe 'from explore result' do
      let(:report) do
        cat = create(:personal_category, name: 'Taxi', owner_id: admin.id)
        settings = VizSetting.new(viz_type: 'data_table')
        model = get_test_query_model('numbers', sql)
        report = create(
          :query_report, title: 'Report To Edit', root_model_id: model.id,
          viz_setting: settings, category_id: -1
        )
        create(
          :personal_item, item_id: report.id, item_type: 'QueryReport',
          owner_id: admin.id, category_id: cat.id, tenant_id: admin.tenant_id
        )
        report
      end

      xit 'should be success' do
        qlogin(:admin, "/queries/#{report.to_param}")
        wait_for_element_load('.ci-table-report-data')
        wait_for_element_load('.ci-data-explore')
        first('.ci-data-explore').click
        wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')

        select_field('num')
        first('.ci-save-explore').click
        sleep 1
        create_report_using_modal
        sleep 2
        expect(PersonalItem.count).to eq(2)
      end
    end
  end

  describe 'report editing test', js: true do
    let(:report) do
      cat = create(:personal_category, name: 'Taxi', owner_id: admin.id)
      settings = {paging_page_size: 10}
      report = create(
        :query_report, title: 'Report To Edit',
        query: sql, settings: settings, category_id: -1
      )
      create(
        :personal_item, item_id: report.id, item_type: 'QueryReport',
        owner_id: admin.id, category_id: cat.id, tenant_id: admin.tenant_id
      )
      report
    end

    it 'edits the report successfully' do
      qlogin(:admin, "/queries/#{report.to_param}/edit")

      wait_for_element_load('#editor')
      # Change title
      safe_click('.ci-title')
      first('.ci-title-input').set('Number Series')

      safe_click('.ci-save')
      # Wait for request success
      wait_for_all_ajax_requests

      report.reload
      wait_expect('Number Series') { report.title }
    end
  end

  context 'with a shared report', js: true do
    let (:current_user) { get_test_analyst }
    let(:cat) do
      cat = create(:personal_category, name: 'Taxi', owner_id: admin.id)
      cat
    end
    let(:report) do
      settings = VizSetting.new(viz_type: 'data_table')
      model = get_test_query_model('numbers', sql)
      report = create(
        :query_report, title: 'report 1', root_model_id: model.id,
        viz_setting: settings, category_id: -1, owner_id: admin.id
      )
      personal_item = create(
        :personal_item, item_id: report.id, item_type: 'QueryReport',
        owner_id: admin.id, category_id: cat.id, tenant_id: admin.tenant_id
      )

      admin.share(current_user, :read, personal_item)
      admin.share(current_user, :read, model)
      admin.share(current_user, :read, model.data_source)
      personal_item
    end

    xit 'view the report successfully' do
      qlogin(current_user, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('report 1') { page.find('.ci-report-title').text }
    end

    xit 'view report successfully for user in group' do
      admin.share(group, :read, report)
      qlogin(admin1, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('report 1') { page.find('.ci-report-title').text }

      qlogin(biz_user, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('report 1') { page.find('.ci-report-title').text }

      qlogin(admin3, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('You do not have permission to access this resource') { page.find('.ci-report-msg').text }
    end

    xit 'view the folder and report successfully for users in group' do
      admin.share(group, :read, cat)
      qlogin(admin1, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('report 1') { page.find('.ci-report-title').text }

      qlogin(biz_user, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('report 1') { page.find('.ci-report-title').text }

      qlogin(admin3, "/queries/#{report.to_param}")
      sleep 1
      wait_expect('You do not have permission to access this resource') { page.find('.ci-report-msg').text }
    end

    xit 'explore and save to another report successfully' do
      qlogin(current_user, "/queries/#{report.to_param}")
      wait_expect('report 1') { page.find('.ci-report-title').text }
      wait_for_element_load('.ci-data-explore')
      first('.ci-data-explore').click
      wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')

      select_field('num')
      first('.ci-save-explore').click
      sleep 1
      create_report_using_modal
      sleep 2
      expect(PersonalItem.count).to eq(2)
    end
  end

  describe 'clone report to Private Workspace', js: true do
    xit 'should be able to clone owned report from Public to Private Workspace' do
      public_report = create :query_report, owner: admin, category_id: 0
      qlogin :admin, '/browse'

      page.all('.ci-navigation-node-action', visible: false)[0].click
      sleep 1
      page.find('.ci-personal-workspace-clone').click

      sleep 2
      expect(PersonalItem.count).to eq(1)
    end

    xit 'should be able to clone public shared report to Private Workspace' do
      public_report = create :query_report, owner: admin, category_id: 0
      admin.share(analyst, :read, public_report)
      qlogin :analyst, '/browse'

      page.all('.ci-navigation-node-action', visible: false)[0].click
      sleep 1
      page.find('.ci-personal-workspace-clone').click

      sleep 2
      expect(PersonalItem.count).to eq(1)
    end
  end
end
