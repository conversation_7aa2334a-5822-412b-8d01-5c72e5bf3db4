# typed: false
require 'rails_helper'

describe 'folder in personal workspace', js: true, stable: true do
  let(:admin) { get_test_admin }

  before do
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
  end

  describe 'create' do
    it 'should be success' do
      qlogin(:admin, '/browse')
      safe_click('.ci-create')
      wait_for_element_load('.ci-create-new-folder')
      safe_click('.ci-create-new-folder')

      wait_for_element_load('.ci-category-name')
      page.find('.ci-category-name').set('test folder')
      safe_click('.ci-submit-btn')
      wait_for_element_load('.ci-node-refresh')
      safe_click('.ci-node-refresh')
      wait_expect('test folder') { page.find('[data-ci="ci-node-title"]').text }
    end
  end
end
