# typed: false
require 'rails_helper'

describe 'dashboard in personal workspace', js: true, stable: true do
  let(:admin) { get_test_admin }
  let(:analyst) { get_test_analyst }

  before do
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
    FeatureToggle.toggle_global('dashboards_v3:creation', true)
  end

  describe 'create a blank dashboard' do
    xit 'should be success' do
      qlogin(:admin, '/browse')
      safe_click('.ci-create')
      safe_click('.ci-create-new-dashboard')

      wait_for_element_load('.ci-dashboard-name')
      first('.ci-dashboard-name').set('test dashboard')
      safe_click('.ci-version-1')
      safe_click('.ci-submit-btn')
      expect(first('.ci-widget-title-element').text).to eq('test dashboard')
    end
  end

  describe 'share permission' do
    def check_share_permission(user:, can_shared_btn_clicked:, has_user_access_setting:)
      qlogin(user, "/dashboards/v3/#{dashboard.to_param}")
      wait_for_element_load('[data-ci="ci-share-dropdown"]')
      shared_button = page.find('[data-ci="ci-share-dropdown"]')
      expect(shared_button[:class].include?('hui-btn-disabled')).to be !can_shared_btn_clicked
      safe_click('.ci-preferences-toggle')
      safe_click('.ci-preference')
      expect(page.all('.ci-user-access').empty?).not_to be has_user_access_setting
    end

    context 'when in case explorer' do
      before do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
      end
      let(:explorer) { get_test_explorer }

      let(:dashboard) do
        private_dashboard = create(
          :dashboard, owner_id: explorer.id, tenant_id: explorer.tenant_id,
          category_id: -1, version: 3,
        )
        create(
          :personal_item, item_id: private_dashboard.id, item_type: 'Dashboard',
          owner_id: explorer.id, tenant_id: explorer.tenant_id,
        )
        private_dashboard
      end

      it 'can share the private dashboard' do
        FeatureToggle.toggle_global(User::FT_ALLOW_EXPLORER_BIZ_USER_SHARE_PRIVATE_ITEM, true)
        check_share_permission(user: explorer, can_shared_btn_clicked: true, has_user_access_setting: true)
      end

      it 'can not share the private dashboard' do
        FeatureToggle.toggle_global(User::FT_ALLOW_EXPLORER_BIZ_USER_SHARE_PRIVATE_ITEM, false)
        check_share_permission(user: explorer, can_shared_btn_clicked: false, has_user_access_setting: false)
      end
    end

    context 'when in case biz user' do
      before do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, false)
      end
      let(:biz_user) { get_test_user }

      let(:dashboard) do
        private_dashboard = create(
          :dashboard, owner_id: biz_user.id, tenant_id: biz_user.tenant_id,
          category_id: -1, version: 3,
        )
        create(
          :personal_item, item_id: private_dashboard.id, item_type: 'Dashboard',
          owner_id: biz_user.id, tenant_id: biz_user.tenant_id,
        )
        private_dashboard
      end

      it 'can share the private dashboard' do
        FeatureToggle.toggle_global(User::FT_ALLOW_EXPLORER_BIZ_USER_SHARE_PRIVATE_ITEM, true)
        check_share_permission(user: biz_user, can_shared_btn_clicked: true, has_user_access_setting: true)
      end

      it 'can not share the private dashboard' do
        FeatureToggle.toggle_global(User::FT_ALLOW_EXPLORER_BIZ_USER_SHARE_PRIVATE_ITEM, false)
        check_share_permission(user: biz_user, can_shared_btn_clicked: false, has_user_access_setting: false)
      end
    end
  end

  context 'with an existing dashboard' do
    let(:current_user) { admin }

    let(:dashboard) do
      dashboard = create(
        :dashboard, owner_id: current_user.id, tenant_id: current_user.tenant_id,
        category_id: -1,
      )
      create(
        :personal_item, item_id: dashboard.id, item_type: 'Dashboard',
        owner_id: current_user.id, tenant_id: current_user.tenant_id,
      )
      dashboard
    end

    describe 'add an adhoc widget' do
      def add_adhoc_widget
        qlogin(current_user, "/dashboards/#{dashboard.id}")
        wait_for_element_load('.ci-toggle-add-widget')
        first('.ci-toggle-add-widget').click
        wait_for_element_load('.ci-adhoc-report-widget')
        first('.ci-adhoc-report-widget').click
        wait_for_element_load('#awesome-query-editor')
        fill_in_ace('#awesome-query-editor', 'select 1,2,3;')
        first('.ci-report-name').click
        first('.ci-title-input').set('widget 1')
        first('.ci-save-report').click
        wait_expect('widget 1') { first('.ci-widget-title-element').text }
        expect(DashboardWidget.count).to eq(1)
      end

      it 'should be success' do
        add_adhoc_widget
      end

      context 'user is an analyst' do
        let(:current_user) { get_test_analyst }
        xit 'should be success' do
          add_adhoc_widget
        end
      end
    end

    describe 'add an existing report to dashboard' do
      let!(:report) {create(:query_report, title: 'report 1', query: 'select 1,2,3')}
      include_context 'version_2.7'
      it 'should be success' do
        qlogin(current_user, "/dashboards/#{dashboard.id}")
        wait_for_element_load('.ci-toggle-add-widget')
        first('.ci-toggle-add-widget').click
        wait_for_element_load('.ci-report-widget')
        first('.ci-report-widget').click

        # Select report
        wait_for_element_load('.ci-select-report')
        select_h_select_option('.ci-select-report', label: 'report 1')

        wait_for_element_load('.ci-widget-title')
        first('.ci-widget-title').set('widget 1')

        safe_click('.ci-submit-btn')

        wait_for { page.all('.v-loading-container', wait: false).count == 0 }

        wait_for_element_load('.ci-widget-title-element')
        expect(first('.ci-widget-title-element').text).to eq('widget 1')

        expect(DashboardWidget.count).to eq(1)
      end
      it 'only allow query report, cannot select Dataset Report' do
        qlogin(current_user, "/dashboards/#{dashboard.id}")
        wait_for_element_load('.ci-toggle-add-widget')
        first('.ci-toggle-add-widget').click
        wait_for_element_load('.ci-report-widget')
        first('.ci-report-widget').click

        # Select report
        wait_for_element_load('.ci-select-report .resource-treeselect')
        node_name = 'dsreport'
        search_h_select('.ci-select-report .resource-treeselect', text: node_name)
        expect(page.find('.hui-select-floating').text).to eq 'No options...'
        node_name = 'report 1'
        select_h_select_option('.ci-select-report .resource-treeselect', label: node_name)
        wait_for_element_load('.ci-widget-title')
        first('.ci-widget-title').set('widget 1')
        safe_click('.ci-submit-btn')
        wait_for { page.all('.v-loading-container', wait: false).count == 0 }
        wait_for_element_load('.ci-widget-title-element')
        expect(first('.ci-widget-title-element').text).to eq('widget 1')

        expect(DashboardWidget.count).to eq(1)
      end
    end

    describe 'remove widget from dashboard' do
      let!(:report) {create(:query_report, title: 'report 1', query: 'select 1,2,3')}
      let!(:widget) do
        widget = create(
          :dashboard_widget, dashboard: dashboard, source_type: 'QueryReport',
          source_id: report.id, tenant_id: current_user.tenant_id
        )
        create(
          :personal_item, item: report, category_id: -1, owner_id: current_user.id,
          tenant_id: current_user.tenant_id
        )
        widget
      end
      xit 'should be success' do
        qlogin(current_user, "/dashboards/#{dashboard.id}")
        wait_for_element_load('.report-widget')
        page.find('.report-widget').hover
        first('.ci-widget-controls').click
        wait_for_element_load('.ci-widget-remove')
        first('.ci-widget-remove').click
        safe_click('.ci-confirm-delete')
        wait_for_element_load('.widget-placeholder')
        expect(DashboardWidget.count).to eq(0)
      end
    end
  end

  context 'with a shared dashboard', js: true do
    let (:current_user) { get_test_analyst }
    let(:dashboard) do
      dashboard = create(
        :dashboard, owner_id: admin.id, tenant_id: current_user.tenant_id,
        name: 'Dashboard 1', category_id: -1,
      )
      personal_item = create(
        :personal_item, item_id: dashboard.id, item_type: 'Dashboard',
        owner_id: admin.id, tenant_id: current_user.tenant_id,
      )
      admin.share(current_user, :read, personal_item)
      dashboard
    end

    it 'view the dashboard successfully' do
      qlogin(current_user, "/dashboards/#{dashboard.to_param}")
      sleep 1
      wait_for_element_load('.ci-dashboard-name')
      wait_expect('Dashboard 1') { page.find('.ci-dashboard-name').text }
    end
  end

  describe 'clone dashboard to Private Workspace', js: true do
    xit 'should be able to clone dashboard from Public to Private Workspace' do
      public_dashboard = create :dashboard, owner: admin, category_id: 0
      qlogin :admin, '/browse'

      page.all('.ci-navigation-node-action', visible: false)[0].click
      sleep 1
      page.find('.ci-personal-workspace-clone').click

      sleep 2
      expect(PersonalItem.count).to eq(1)
    end

    xit 'should be able to clone public shared dashboard to Private Workspace' do
      public_dashboard = create :dashboard, owner: admin, category_id: 0
      admin.share(analyst, :read, public_dashboard)
      qlogin :analyst, '/browse'

      page.all('.ci-navigation-node-action', visible: false)[0].click
      sleep 1
      page.find('.ci-personal-workspace-clone').click

      sleep 2
      expect(PersonalItem.count).to eq(1)
    end
  end
end
