# typed: false
require 'rails_helper'

describe 'go-up', js: true, driver: :iphone, stable: true do
  let (:admin) { get_test_admin }

  context 'report category' do
    before do
      parent = FactoryBot.create :report_category, name: 'parent'
      FactoryBot.create :report_category, name: 'child', parent_id: parent.id
    end

    it 'go to report category' do
      qlogin(admin, "/browse")
      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 1 #includes 'parent' only
      expect(current_path).to eq ('/browse')
    end

    it 'go in parent and go back' do
      qlogin(admin, "/browse")
      wait_for_element_load '.browse-item'
      sleep 1
      page.first('.browse-item').click

      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 2 #includes '.....' and 'child'
      expect(current_path).to eq ('/browse/1-parent')

      page.first('.browse-item').click
      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 1
      expect(current_path).to eq ('/browse')
    end

    it 'go in child and go back' do
      qlogin(admin, "/browse/1-parent")
      wait_for_element_load '.browse-item'
      sleep 1
      page.all('.browse-item')[1].click

      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 2 #includes '.....' and 'there is nothing here :('
      expect(current_path).to eq ('/browse/2-child')

      page.first('.browse-item').click
      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 2
      expect(current_path).to eq ('/browse/1-parent')
    end
  end

  context 'personal category' do
    before do
      FeatureToggle.toggle_global('personal_workspace:enabled', true)
      parent = FactoryBot.create :personal_category, name: 'parent', owner_id: admin.id
      FactoryBot.create :personal_category, name: 'child', parent_id: parent.id, owner_id: admin.id
    end

    it 'go to report category' do
      qlogin(admin, "/personal-folders")
      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 1
      expect(current_path).to eq ('/personal-folders')
    end

    it 'go in parent and go back' do
      qlogin(admin, "/personal-folders")
      wait_for_element_load '.browse-item'
      sleep 1
      page.first('.browse-item').click

      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 2
      expect(current_path).to be_in(['/personal-folders/1', '/personal-folders/1-parent'])

      page.first('.browse-item').click
      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 1
      expect(current_path).to eq ('/personal-folders')
    end

    it 'go in child and go back' do
      qlogin(admin, "/personal-folders/1")
      wait_for_element_load '.browse-item'
      sleep 1
      page.all('.browse-item')[1].click

      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 2
      expect(current_path).to be_in(['/personal-folders/2', '/personal-folders/2-child'])

      page.first('.browse-item').click
      wait_for_element_load '.browse-item'
      sleep 1
      displayed_elements = page.all('.browse-item')

      expect(displayed_elements.count).to eq 2
      expect(current_path).to be_in(['/personal-folders/1', '/personal-folders/1-parent'])
    end
  end
end
