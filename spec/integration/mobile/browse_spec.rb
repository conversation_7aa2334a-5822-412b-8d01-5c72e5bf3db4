# typed: false
require 'rails_helper'

describe 'Homepage on mobile', js: true, driver: :iphone, stable: true do
  let (:admin) { get_test_admin }
  let (:tenant) { get_test_tenant }

  before do
    FeatureToggle.toggle_global('search:folder', true)

    5.times.each { FactoryBot.create :dashboard, owner: admin, tenant: tenant }
    FactoryBot.create :favourite, source: Dashboard.first, user: admin
    5.times.each { FactoryBot.create :query_report, owner: admin, tenant: tenant }
    FactoryBot.create :favourite, source: QueryReport.first, user: admin
    FactoryBot.create :favourite, source: QueryReport.last, user: admin

    FactoryBot.create :report_category, name: 'folder search', tenant: tenant, owner: admin
    FactoryBot.create :query_report, owner: admin, tenant: tenant, title: 'report search'
    FactoryBot.create :query_report, owner: admin, tenant: tenant, title: 'child report search', category_id: 1
    FactoryBot.create :dashboard, owner: admin, tenant: tenant, title: 'dashboard search'
  end

  it 'allows signin from login page, then signout from sidebar' do
    sign_in admin

    wait_expect(current_path) { browse_path }

    wait_and_click '.ci-m-sidebar'
    wait_and_click '.ci-m-logout'
    wait_and_click '.ci-confirm'
    sleep 1

    wait_expect(current_path) { new_user_session_path }
  end

  it 'displays list of dashboards, reports and favorites' do
    qlogin admin, browse_path

    wait_for_element_load '.browse-item'
    wait_expect(13) { page.all('.browse-item').count } # 1 inside folder

    wait_for_element_load '.ci-fav'
    wait_expect(Favourite.count) { page.all('.ci-fav.fav-on').count }
  end

  it 'searches items correctly' do
    qlogin admin, browse_path

    wait_for_element_load '.browse-item'
    fill_text('.ci-search-browse', 'search')
    wait_expect(4) { page.all('.browse-item').count }
  end
end
