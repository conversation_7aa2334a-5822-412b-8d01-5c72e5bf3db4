# typed: false
require 'rails_helper'

describe 'Homepage on mobile', js: true, driver: :iphone, stable: true do
  let (:admin) { get_test_admin }
  let (:tenant) { get_test_tenant }
  let (:user) { get_test_analyst }

  before do
    FeatureToggle.toggle_global('reporting_nav:enabled', true)
    FeatureToggle.toggle_global('personal_workspace:enabled', true)
    5.times.each { FactoryBot.create :dashboard, owner: admin, tenant: tenant }
    FactoryBot.create :favourite, source: Dashboard.first, user: admin
  end

  let!(:report2) {
    query = <<-SQL.strip_heredoc
      VALUES ('2013-02-01', 100, 200),
              ('2013-02-02', 200, 400),
              ('2013-02-03', 300, 600)
    SQL

    _report = create :query_report, title: 'Test 2', query: query
    _report.category_id = -1
    _report.personal_item = create :personal_item, item: _report, tenant_id: _report.tenant_id, owner: _report.owner
    _report.save
    admin.share(user, :read, _report.personal_item)
    _report
  }
  let!(:report3) {
    query = <<-SQL.strip_heredoc
      VALUES ('2013-02-01', 100, 200),
              ('2013-02-02', 200, 400),
              ('2013-02-03', 300, 600)
    SQL

    _report = create :query_report, title: 'Test 2', query: query
    _report.category_id = -1
    _report.personal_item = create :personal_item, item: _report, tenant_id: _report.tenant_id, owner: user
    _report.save
    user.share(admin, :read, _report.personal_item)
    _report
  }
  let!(:private_folder) { create :personal_category, name: 'Private', owner_id: admin.id, tenant_id: admin.tenant_id }
  let!(:dashboard2) {
    _dashboard = create :dashboard, title: 'Private Dash'
    _dashboard.category_id = -1
    _dashboard.personal_item = create :personal_item, item: _dashboard, tenant_id: _dashboard.tenant_id, owner_id: admin.id
    _dashboard.save
    admin.share(user, :read, _dashboard.personal_item)
    _dashboard
  }
  let!(:dashboard_widget) {
    create :dashboard_widget, dashboard: dashboard2, source: report2, tenant_id: admin.tenant_id
  }

  context 'Sidebar Navigation' do

    # NOTE: This spec cannot run on Firefox 47.0.1 but working fine in latest Firefox and Chrome
    it 'working sidebar' do
      sign_in admin
      expect(current_path).to eq '/home'

      wait_and_click '.ci-m-sidebar .h-icon'
      sleep 0.5
      wait_and_click '.ci-m-workspace'
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item', visible: true)
      expect(displayed_elements.count).to eq 5
      expect(current_path).to eq browse_path

      wait_and_click '.ci-m-sidebar .h-icon'
      sleep 0.5
      wait_and_click '.ci-m-personal'
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item', visible: true)
      expect(displayed_elements.count).to eq 3
      expect(current_path).to eq('/personal-folders')

      wait_and_click '.ci-m-sidebar .h-icon'
      sleep 0.5
      wait_and_click '.ci-m-shared'
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item', visible: true)
      expect(displayed_elements.count).to eq 1
      expect(current_path).to eq('/shared-with-me')

      wait_and_click '.ci-m-sidebar .h-icon'
      sleep 0.5
      wait_and_click '.ci-m-fav'
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item', visible: true)
      expect(displayed_elements.count).to eq 1
      expect(current_path).to eq browse_path
    end
  end

  context 'Private Workspace' do
    it 'Personal Page' do
      qlogin(admin, "/personal-folders")
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item',visible: true)
      expect(displayed_elements.count).to eq 3
      sleep 1
    end

    it 'Access Report from direct link and go back to Private' do
      qlogin(admin, "/queries/#{report2.to_param}")
      sleep 1
      page.first('.m-navbar .nav-back').click
      sleep 1
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item',visible: true)
      expect(displayed_elements.count).to eq 3
      expect(current_path).to eq '/personal-folders'
      sleep 1
    end

    it 'Access Dashboard from direct link and go back to Private' do
      qlogin(admin, "/dashboards/#{dashboard2.id}")
      sleep 1
      page.first('.m-navbar .nav-back').click
      sleep 1
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item',visible: true)
      expect(displayed_elements.count).to eq 3
      expect(current_path).to eq '/personal-folders'
      sleep 1
    end

    it 'Shared with Me Page' do
      qlogin(user, "/shared-with-me")
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item',visible: true)
      expect(displayed_elements.count).to eq 2
      sleep 1
    end

    it 'Access Report from direct link and go back to Shared With Me' do
      qlogin(user, "/queries/#{report2.to_param}")
      sleep 1
      page.first('.m-navbar .nav-back').click
      sleep 1
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item',visible: true)
      expect(displayed_elements.count).to eq 2
      expect(current_path).to eq '/shared-with-me'
      sleep 1
    end

    it 'Access Dashboard from direct link and go back to Shared With Me' do
      qlogin(user, "/dashboards/#{dashboard2.id}")
      sleep 1
      page.first('.m-navbar .nav-back').click
      sleep 1
      wait_for_element_load '.browse-item'
      displayed_elements = page.all('.browse-item',visible: true)
      expect(displayed_elements.count).to eq 2
      expect(current_path).to eq '/shared-with-me'
      sleep 1
    end
  end

  context 'User with expired trial & toggle is on' do
    before do
      FeatureToggle.toggle_global('billing:freemium_expired_modal', true)
      user.tenant.build_tenant_subscription(expired_at: Date.yesterday, status: 'trial.expired').save
    end
    it 'Pops up modal' do
      qlogin(user, "/browse")
      expect(current_path).to eq '/browse'
      wait_for_element_load '.trial-expired-mobile'
      expect(current_path).to eq browse_path
    end
    it 'Pops up modal, user can log out, sign in and still pops up modal' do
      qlogin(user, "/browse")
      expect(current_path).to eq '/browse'
      wait_for_element_load '.trial-expired-mobile'
      expect(current_path).to eq browse_path
      page.first('.ci-m-logout').click
      wait_for_element_load '.sign-in'
      qlogin(user, "/browse")
      wait_for_element_load '.trial-expired-mobile'
      expect(current_path).to eq browse_path
    end
  end
end
