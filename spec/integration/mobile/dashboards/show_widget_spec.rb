# typed: false
require 'rails_helper'
describe 'Dashboard on mobile', js: true, driver: :iphone, stable: true do
  include_context 'avenger_objects'

  let(:data_source) {
    avengers_source_with_dbconfig
  }
  let(:report) {
    query = <<-SQL.strip_heredoc
      VALUES ('2013-01-01', 100, 200),
              ('2013-01-02', 200, 400),
              ('2013-01-03', 300, 600)
    SQL

    create :query_report, title: 'Test', query: query, tenant_id: avengers.id, data_source_id: data_source.id
  }
  let(:report2) {
    query = <<-SQL.strip_heredoc
      VALUES ('2013-02-01', 100, 200),
              ('2013-02-02', 200, 400),
              ('2013-02-03', 300, 600)
    SQL

    create :query_report, title: 'Test 2', query: query, tenant_id: avengers.id, data_source_id: data_source.id
  }
  let(:report3) {
    query = <<~SQL
      VALUES ({{input}}, {{input}})
    SQL

    create :query_report, title: 'Test 3', query: query, is_adhoc: true, tenant_id: avengers.id, data_source_id: data_source.id
  }
  let (:dashboard) {
    dashboard = create :dashboard, tenant_id: avengers.id, category_id: 0, id: 10
    dashboard
  }
  let! (:dashboard_widget) {
    create :dashboard_widget, dashboard: dashboard, source: report, tenant_id: avengers.id
  }
  let (:dashboard_widget2) {
    create :dashboard_widget, dashboard: dashboard, source: report2, tenant_id: avengers.id
  }
  let (:dashboard_widget3) {
    create :dashboard_widget, dashboard: dashboard, source: report3, tenant_id: avengers.id
  }

  def expanded_widget_via_direct_link(user, widget)
    qlogin(user, "/dashboards/#{dashboard.id}?_e=#{widget.id}")
    wait_for_element_load '.m-expanded-widget .ci-table-report-data'
    wait_for_report_data
  end

  def test_widget_data values
    wait_for_report_data
    sleep 1
    wait_expect(values) do
      page.all('.ci-table-report-data .ag-row .ag-cell').map(&:text)
    end
  end

  describe 'Signed in users' do
    context 'Expanded widget' do
      before do
        dashboard_widget2
        dashboard_widget3
      end

      it 'User can go back after access via direct link' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)
        page.first('.m-navbar .nav-back').click
        wait_for_element_load('.m-widget')
        widgets = page.all('.m-widget')
        expect(widgets.count).to eq 3
        test_widget_data ["Jan 01 2013", "100", "200", "Jan 02 2013", "200", "400", "Jan 03 2013", "300", "600", "Feb 01 2013", "100", "200", "Feb 02 2013", "200", "400", "Feb 03 2013", "300", "600"]
      end

      it 'User go back from expanded widget and can expand another correctly' do
        expanded_widget_via_direct_link(capt_america, dashboard_widget)
        page.find('.m-navbar .nav-back', match: :first).click

        wait_for_element_load('.dashboard-widgets .m-widget:nth-child(2) .ci-table-report-data')
        safe_click('.widget-title', index: 1)

        test_widget_data ['Feb 01 2013', '100', '200', 'Feb 02 2013', '200', '400', 'Feb 03 2013', '300', '600']
      end
    end
  end
end
