# typed: false
require 'rails_helper'

describe 'Dashboard on mobile', js: true, driver: :iphone, stable: true do
  let (:dashboard) { FactoryBot.create :dashboard }

  before do
    r = FactoryBot.create :query_report, query: 'values (1, 2)'
    m = FactoryBot.create :query_metric

    DashboardWidget.create source_type: 'Text', title: '# Wow', dashboard: dashboard, tenant: dashboard.tenant
    DashboardWidget.create source: r, dashboard: dashboard, tenant: dashboard.tenant, title: 'Wow Report'
    DashboardWidget.create source: m, dashboard: dashboard, tenant: dashboard.tenant, title: 'Wow metric'

    dashboard.reload
  end

  it 'displays list of widgets in column' do
    qlogin :admin, dashboard_path(dashboard)

    wait_for_element_load '.m-widget'
    widgets = page.all('.m-widget')

    expect(widgets.count).to eq dashboard.dashboard_widgets.count
  end

  context 'when expanding report widget' do
    it 'shows full report' do
      qlogin :admin, dashboard_path(dashboard)

      wait_and_click '.m-widget.report-widget .ci-expand-widget'

      wait_for_element_load '.ci-table-report-data .ag-row'
      sleep 1 # handsometable re-renders once
      records = page.all('.ag-row')
      columns = page.all('.ag-row .ag-cell')

      expect(records.count).to eq 1
      expect(columns.count).to eq 2
    end
  end

end
