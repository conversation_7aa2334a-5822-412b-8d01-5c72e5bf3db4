# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Data exploration specs', js: true do
  include_context 'data_exploration'

  context 'can explore everywhere' do
    describe 'dashboard v1' do
      before do
        FeatureToggle.toggle_global('data_models:manager', false)
        add_report_to_dashboard(report, dashboard)
        dashboard_navigation(admin, dashboard)
      end

      it 'not show context menu on left click' do
        click_element('.highchart')
        expect(page).not_to have_selector('.h-context-menu-content .ci-data-explore')
      end

      # FIXME: Skip because it fails intermittenly on CircleCI
      it 'show context menu on right click', skip: ENV['CIRCLECI'] do
        wait_for_element_load('.highchart')
        right_click_on_element('.highchart')
        wait_expect(true) { page.has_css?('.h-context-menu-content .ci-data-explore') }
      end

      it 'show explore button on widget controls' do
        page.find('.widget-wrapper').hover
        wait_and_click('.ci-widget-controls')
        wait_expect(true) { page.has_css?('.hui-dropdown-floating .ci-data-explore') }
      end
    end

    context 'dashboard v3' do
      let!(:widget) { add_report_to_dashboard(ds_report, dashboard) }
      let!(:widget2) { add_report_to_dashboard(ds_report, dashboard) }
      before do
        # these FTs affect how many viz results (among main viz, table data, chart data) are rendered in the result
        FeatureToggle.toggle_global(Viz::Constants::FT_TABULAR_DATA_LAZY_TABLE_DATA, true)
        FeatureToggle.toggle_global(Viz::Constants::FT_SORT_BEFORE_LIMIT, true)
        FeatureToggle.toggle_global(::Viz::Constants::FT_TABLE_V2, true)
        FeatureToggle.toggle_global(::Viz::Constants::FT_PIVOT_V2, true)

        vs = ds_report.viz_setting
        vs.fields[:x_axis] = {
          path_hash: {
            model_id: data_set.data_models.first.id,
            field_name: 'name',
          },
          format: {
            type: 'string',
            sub_type: 'string',
          },
        }
        vs.save!
        dashboard.update!(version: 3)
        dashboard_navigation(admin, dashboard)
      end

      it 'can sync explore state with url' do
        # initial load
        wait_for_viz_load
        wait_expect(6) do
          page.all('.highcharts-series .highcharts-point').size
        end
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 2, wait: true)

        # open explore modal
        page.first('.ci-report-widget').hover
        safe_click('.hui-btn [data-icon="explore"]')
        wait_for_element_load('.ci-data-exploration-modal')
        explore_regex = /_explore=(\d+)/
        hash_regex = /_ehash=([^\/]+)/
        wait_expect(widget.id.to_s) do
          current_url.match(explore_regex).try(:[], 1)
        end
        expect(current_url.match(hash_regex)).to eq(nil)
        expect(page.all('.ci-field-info .field-label').map(&:text)).not_to include(['Status'])
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3, wait: true)

        # update viz
        select_h_select_option '.viz-section:nth-child(2) .ci-empty-field', label: 'Status'
        wait_for do
          current_url.match(hash_regex).try(:[], 1)
        end
        explored_url = current_url

        page.go_back
        wait_expect(nil) do
          current_url.match(hash_regex).try(:[], 1)
        end
        # no new requests because this is the initial exploration, which is already being loaded on frontend
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3, wait: true)

        page.go_back
        wait_expect(nil) do
          current_url.match(explore_regex).try(:[], 1)
        end
        wait_expect(0) do
          page.all('.ci-data-exploration-modal', wait: false).size
        end
        wait_expect(6) do
          page.all('.highcharts-series .highcharts-point').size
        end
        # no new requests because the widgets are already loaded
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3, wait: true)

        page.go_forward
        wait_expect(widget.id.to_s) do
          current_url.match(explore_regex).try(:[], 1)
        end
        wait_for_element_load('.ci-data-exploration-modal')
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 4, wait: true)

        # revisit explored_url
        visit explored_url
        wait_for_element_load('.ci-data-exploration-modal')
        expect(page.all('.ci-field-info .field-label').map(&:text)).to include('Status')
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 2, wait: true) # 1 for widget + 1 for explore modal
        safe_click('.ci-explorer-control-get-results')
        expect(page.all('.ci-report-widget .highcharts-series .highcharts-point').size).to eq(3) # only the first widget is loaded
        wait_expect(4) do
          page.all('.ci-data-exploration-modal .highcharts-series .highcharts-point').size
        end
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 3, wait: true) # +1 Get Result

        safe_click('.ci-close-modal')
        # both widgets are loaded
        wait_expect(6) do
          page.all('.highcharts-series .highcharts-point').size
        end
        expect_ajax_requests({ url: /\/viz_data\/submit_generate\.json/ }, count: 4, wait: true) # +1 the other widget
      end
    end

    describe 'dashboard widget' do
      before do
        widget = add_report_to_dashboard(report, dashboard)
        dashboard_widget_navigation(admin, widget)
      end

      it 'show explore button in expanded widget' do
        wait_for_element_load('.dashboard-expanded-widget-meta')
        expect(page).to have_selector('.ci-data-explore')
      end
    end

    describe 'query report' do
      it 'should show explore button in query report while feature toogle is disabled' do
        report_navigation(admin, report)
        expand_tabular_data_tab
        right_click_on_element('.ci-table-report-data > div:first-child')
        safe_click('.ci-data-explore')
        expect(page).to have_selector('.data-exploration-modal')
      end
    end
  end

  # Enable left click behavior

  context 'enable explore everywhere with left click' do
    before do
      FeatureToggle.toggle_global('viz_result:left_click_enabled', true)
    end

    describe 'query report' do
      context 'viz_result:show_context_menu_on_data_table is disbled' do
        before do
          report_navigation(admin, report)
        end

        it 'should show context menu by left click' do
          click_element('.highchart')
          expect(page).to have_selector('.h-context-menu-content .ci-data-explore')
        end

        it 'should not show context menu on data table' do
          expand_tabular_data_tab
          wait_for_report_data
          click_element('.ci-table-report-data > div:first-child')
          expect(page).not_to have_selector('.h-context-menu-content .ci-data-explore')
        end
      end
    end
  end

  context 'context menu visibility' do
    before do
      FeatureToggle.toggle_global('data_models:manager', true)
      FeatureToggle.toggle_global('viz_result:left_click_enabled', true)
    end

    it 'should not show context on query editor' do
      safe_login admin, "/queries/#{report.id}"
      click_element('.ci-edit-report-link')
      click_element('.ci-btn-run')
      wait_for_element_load('.highchart')
      click_element('.highchart')
      expect(page).not_to have_selector('.viz-context-menu')
      right_click_on_element('.highchart')
      expect(page).not_to have_selector('.viz-context-menu')
      click_element('.ci-save-report')
    end

    it 'shoud not show context menu when exploring everywhere' do
      safe_login admin, "/queries/#{report.id}"
      wait_for_element_load('.highchart')
      right_click_on_element('.highchart')
      open_modal_by_click('.ci-data-explore')
      right_click_on_element('.ci-data-exploration-modal .highchart')
      expect(page).not_to have_selector('.ci-data-exploration-modal .viz-context-menu')
    end

    it 'should not show context on query editor' do
      safe_login admin, "/data_models?ds=#{ds.id}&model=#{model.id}"
      click_element('.ci-model-tabs .nav-left div:nth-child(2)')
      click_element('.ci-explorer-control-get-results')
      click_element('.ci-table-report-data')
      expect(page).not_to have_selector('.viz-context-menu')
    end
  end

  context 'generate report name' do
    it 'keep generated title if create new report' do
      report_navigation(admin, ds_report)
      wait_for_element_load('.ci-data-explore')
      safe_click('.ci-data-explore')

      wait_for_element_load('.ci-data-exploration-modal .highchart')
      safe_click('.ci-save-explore-results-btn')

      select_h_select_option('.save-report-modal .resource-treeselect', value: '0c')

      safe_click('.ci-save-report-from-explore-modal')

      wait_expect('Sum of Price by Id') { QueryReport.last.title }
    end
  end

  it 'should open notifications list popover' do
    report_navigation(admin, ds_report)
    wait_for_viz_load
    open_modal_by_click '.report-show-controls .ci-data-explore'

    safe_click('.ci-export-btn')
    safe_click('.ci-export-data-only')
    expect(page).to have_selector('.ci-notifications-list')
    expect(page).to have_selector('.popover-header-noti')
  end

  it 'should show viz sort in viz setting form' do
    report_navigation(admin, ds_report)
    wait_for_viz_load
    open_modal_by_click '.report-show-controls .ci-data-explore'

    # just showing the sort section is enough
    wait_for_element_load('.ci-highcharts-sort')
  end
end
