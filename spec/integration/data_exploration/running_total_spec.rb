# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Data exploration specs', js: true do
  include_context 'data_set'

  before do
    FeatureToggle.toggle_global(Viz::Constants::FT_RUNNING_TOTAL, true)
    FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
  end

  it 'works' do
    connector = Connectors.from_ds(get_test_ds)

    connector.exec_sql('truncate data_modeling.products')
    connector.exec_sql(
      <<~SQL
        INSERT INTO data_modeling.products(name, merchant_id, category_id, "Price", "Status", "Created At")
        ( VALUES
          ('bread', 1, 1, 2.25, 'available', '2019-08-10T00:00:00Z'),
          ('milk', 1, 1, 3, 'available', '2019-08-10T00:00:00Z'),
          ('egg', 1, 1, 5, 'available', '2019-08-10T00:00:00Z'),
          ('bread', 1, 1, 2.25, 'expired', '2019-08-10T00:00:00Z'),
          ('bread', 1, 1, 2.25, 'available', '2019-08-09T00:00:00Z'),
          ('milk', 1, 1, 3, 'available', '2019-08-09T00:00:00Z'),
          ('egg', 1, 1, 5, 'available', '2019-08-09T00:00:00Z'),
          ('bread', 1, 1, 2.25, 'expired', '2019-08-09T00:00:00Z')
        )
      SQL
    )
    safe_login admin, data_set_path(data_set)
    wait_for_element_load('.data-set-explorer')
    wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')

    page.find('#data_modeling_products-created_at .name').click(x: -30, y: 0) # second field in first model: created_at
    page.find('#data_modeling_products-category_id .name').click(x: -30, y: 0) # first field in first model: category_id
    page.find('#data_modeling_products-category_id .name').click(x: -30, y: 0) # first field in first model: category_id

    select_h_select_option('.ci-viz-field-select:nth-child(3) .ci-normal-field', value: 'sum')
    select_h_select_option('.ci-viz-field-select:nth-child(2) .ci-normal-field', group: 'Running', value: 'running sum')

    safe_click('.ci-explorer-control-get-results')

    wait_for_element_load('.ci-table-report-data')

    values = page.first('.ci-table-report-data').all('.ag-cell:not(.ag-column-first)').map(&:text)
    expect(values).to eq(["2019-08-10 00:00:00", "8", "4",
                          "2019-08-09 00:00:00", "4", "4"])

    page.find('#data_modeling_products-status .name').click(x: -30, y: 0) # 7th field in first model: status

    safe_click('.ci-explorer-control-get-results')

    wait_for_all_ajax_requests
    wait_for_element_load('.ci-table-report-data')

    values = page.first('.ci-table-report-data').all('.ag-cell:not(.ag-column-first)').map(&:text)
    expect(values).to eq(["2019-08-10 00:00:00", "6", "3", "available",
                          "2019-08-09 00:00:00", "3", "3", "available",
                          "2019-08-10 00:00:00", "2", "1", "expired",
                          "2019-08-09 00:00:00", "1", "1", "expired"])

    connector.exec_sql('truncate data_modeling.products')
  end
end
