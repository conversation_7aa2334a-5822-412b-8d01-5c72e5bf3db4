# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Data Explorer', :js do
  include_context 'data_set'
  let(:ds_report) { create(:products_modeling_report, owner: admin, data_set: data_set) }

  context 'empty condition' do
    it 'does not submit invalid condition' do
      safe_login admin, data_set_path(data_set)
      # select some fields
      page.find_by_id('category_id-label').click(x: -30, y: 0)
      page.find_by_id('created_at-label').click(x: -30, y: 0)
      page.find_by_id('name-label').click(x: -30, y: 0)

      # add condition and submit
      select_h_select_option('.ci-viz-filter-select', label: 'Name')
      h_select_dropdown('.ci-value-select', value: 'bread')
      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_all_ajax_requests
      # return only 1 row (filtered)
      expect(page.all('.ci-viz-result .ag-row').count).to eq 1

      # clear condition value and resubmit
      reset_ajax_requests
      safe_click('.ci-dm-filter')
      safe_click('.ci-value-select .ci-deselect')
      safe_click('[data-ci="ci-explorer-control-get-results"]')
      wait_for_all_ajax_requests
      # return all data (no filter)
      expect(page.all('.ci-viz-result .ag-row').count).to eq 3

      # do not submit filter
      submit_request = ajax_requests.find { |r| r['url'].include? 'submit_generate' }
      expect(submit_request.dig('data', 'viz_setting', 'filters')).to eq []
    end
  end
end
