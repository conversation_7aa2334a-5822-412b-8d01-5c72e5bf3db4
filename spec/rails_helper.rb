# typed: false
# frozen_string_literal: true

# This file is copied to spec/ when you run 'rails generate rspec:install'
ENV['RAILS_ENV'] ||= 'test'
ENV['CACHE_TYPE']  = 'postgres'

ENV['ENABLE_FT_DEPRECATION'] ||= '0'

# Check with `redis-cli monitor`
# https://github.com/grosser/parallel_tests/blob/master/Readme.md#add-to-configdatabaseyml
if ENV['TEST_ENV_NUMBER']
  redis_db  = ENV['TEST_ENV_NUMBER'].to_i
  redis_db -= 1 if redis_db > 0

  ENV['CACHE_DB']         = (redis_db * 2).to_s
  ENV['SIDEKIQ_REDIS_DB'] = ((redis_db * 2) + 1).to_s
end

require File.expand_path('../config/environment', __dir__)
require 'spec_helper'
require 'capybara/rspec'
require 'capybara/rails'
require 'rspec/rails'

# Check Rails migrations
# NOTE: may set RSPEC_SKIP_RAILS_MIGRATION_CHECK=1 to skip the check and avoid the processing overhead.
# Though in most case the overhead should be negligible.
unless ENV['RSPEC_SKIP_RAILS_MIGRATION_CHECK'].to_i == 1
  begin
    # Found this method from https://github.com/rails/rails/blob/v6.0.5.1/activerecord/lib/active_record/railtie.rb#L85
    ActiveRecord::Migration.check_pending!
  rescue ActiveRecord::PendingMigrationError
    raise 'Missing Rails migrations. Please make sure you have ran rails db:migrate and rails db:test:prepare'.red
  end
end

# update drivers
require 'webdrivers/geckodriver'

# register selenium driver
require 'register_driver'

# ensure db_support is always loaded first
require Rails.root.join('spec/support/db_support_methods.rb')
require Rails.root.join('spec/support/database_cleaner.rb')

# TODO: https://app.asana.com/0/1115112077819957/1205090267530201/f
Webdrivers::Chromedriver.required_version = '114.0.5735.90'

Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }
Dir[Rails.root.join('spec/shared/**/*.rb')].each { |f| require f }
require_relative 'deprecation_toolkit_helper'

TEST_MUTEX = 'tmp/test_mutex'

# Set up PGCACHE_ENV
rc = Rails.configuration.database_configuration['test']
ENV['POSTGRES_CACHE_HOST'] = rc['host']
ENV['POSTGRES_CACHE_PORT'] = rc['port'] || '5432'
ENV['POSTGRES_CACHE_DBNAME'] = rc['database']
ENV['POSTGRES_CACHE_USER'] = rc['username']
ENV['POSTGRES_CACHE_PASS'] = rc['password'].to_s
ENV['IS_RSPEC'] = 'true'

Capybara.configure do |config|
  num_threads = ENV['CAPYBARA_THREADS'].to_i
  num_threads = 4 if num_threads <= 0
  config.server = :puma, { Threads: "0:#{num_threads}" }

  config.default_max_wait_time = 5
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

RSpec.configure do |config|
  config.infer_base_class_for_anonymous_controllers = false
  config.fixture_path = "#{Rails.root}/spec/fixtures"
  config.use_transactional_fixtures = false
  config.global_fixtures = :all

  config.include Devise::Test::ControllerHelpers, type: :controller
  config.include FactoryBot::Syntax::Methods
  config.include Capybara::DSL
  config.include Committee::Test::Holistics::Methods, api: true

  config.include Rails.application.routes.url_helpers # url_for

  if defined?(ActiveRecord::Base) && ENV['ENABLE_ACTIVERECORD_LOGS'].present?
    ActiveRecord::Base.logger = Logger.new($stdout)
  end
  Rails.logger = Logger.new($stdout) if ENV['ENABLE_RAILS_LOGGER'].present?
  if ENV['RSPEC_CLEAN_BACKTRACE'].present?
    config.backtrace_exclusion_patterns = [
      # /spec\/spec_helper\.rb/,
      /lib\/rspec\/(core|expectations|matchers|mocks)/,
      /rspec-retry|bundle/,
      /opentelemetry|h_otel/,
      /sorbet-runtime/,
    ]
  end

  env = ENV.to_h

  config.before do |test|
    (sleep(0.2) while File.exist?(TEST_MUTEX)) if test.metadata[:non_parallel]

    # rubocop:disable Lint/EmptyBlock
    File.open(TEST_MUTEX, 'w') {}
    # rubocop:enable Lint/EmptyBlock

    ThreadContext.reset(:web)

    ActionMailer::Base.deliveries.clear
    Cache.flushdb
    ReportCache.flushall
    HistoryVersion.disable_version_tracking # To speed up tests

    FeatureToggle.toggle_global_multiple(
      true,
      [
        ShareableLink::FEATURE_TOGGLE_KEY,
        'shareable_link:ui_enabled',
        'data_source:enable_schema_info',
        'email_schedule:attachment_password',
        'use_new_viz_exporter_to_render_data_tables',
        'data_models:custom_field',
        'report_widget:dedup_job',
        SharedFilter::FEATURE_TOGGLE_STANDARDIZE_SETTINGS,
        QueryReports::Visualization::FT_EXPORT_LEGACY_VIZ_WITH_PPTR,
        QueryReport::FT_ALLOW_STANDALONE_DATASET,
        Permission::FT_COMPOUND_ACTIONS,
        BaseAbility::FT_INTERSECT_PARENT_ABILITIES,
        QueryReport::FT_ALLOW_SQL_CREATION,
        Dashboard::FT_V1_CREATION,
        Permissions::JobPermission::FT_CHECK_EMBED_PAYLOAD,
        'data_models:sql_generation_gem',
        DataModeling::SqlGenConverters::ConvertDataModel::USE_QUERY_CACHE_FROM_DB_MODEL,
        'data_models:use_viz_type_for_pgcache_model',
        # Connectors::FT_NEW_CONNECTORS,
        test.metadata[:disable_ability_cache] ? nil : BaseAbility::FT_CACHE,
        Job::FT_OPTIMIZED_ARGUMENTS,
        Viz::Constants::FT_DEDUP_EXPLORE_JOB,
        'jobs:display_revamped_job_statuses',
        CodingUtils::FQNames::FT_PARSE_USING_REG_EXP,
        ShareableLink::FT_USE_API_V2_IN_UI,
        Viz::Constants::FT_SKIP_FILTERS_ON_PIVOT_META_DATA,
        VizSetting::FT_SORT_PROCESSING_V2,
        AmlStudio::WorkingEnvironment::FT_STRICT_ENV_CHECK,
        DataModel::FT_SQL_GEN_GEM_IN_DB_MODELING,
        Users::OmniauthCallbacksController::FT_AUTO_REMEMBER_ME_ENABLED,
        ImageExporters::PuppeteerRunner::FT_APPLY_CSP,
        ImageExporters::PuppeteerRunner::FT_BLOCK_IFRAME,
        ImageExporters::PuppeteerRunner::FT_EXPERIMENTAL_VERSION,
        Job::FT_ROLLING_POLLING,
        JobLog::FT_SAVE_USING_RAW_SQL,
        PostgresCache::FT_USE_BULK_LOAD_V2,
        'viz:customizable_tooltips',
        Viz::Constants::FT_REFRESH_WITH_APPLIED_VIZ_SETTING,
        AdhocQuery::FT_AVOID_GUESS_COL_TYPE,
        Viz::Constants::FT_CACHE_KEY_V2,
        Viz::Constants::FT_FIELD_SUGGESTIONS_STRICT_ANALYST_PERMISSION,
        Viz::Constants::FT_AQL_SORT_BEFORE_LIMIT,
        AmlStudio::Project::FT_GIT_SYNC,
        Job::FT_SELECT_MINIMAL_COLUMNS,
        Viz::Constants::FT_NEW_JOB_EXPLORE_PAYLOAD,
        DataSets::AmlObjectsCacheService::FT_ARGUMENTS_SERIALIZER_CACHE,
        'viz_result:queue_update_v3',
        AmlStudio::Repositories::Compiler::FT_AML_SERVER_USE_GZIP,
        AmlCompiledCache::FT_USE_AML_COMPILED_CACHE,
        # temporary disable this FT in specs because there are a rare race condition that makes integration specs flaky
        # see https://github.com/holistics/holistics/pull/13255
        # Viz::Constants::FT_DASHBOARD_FETCH_DATASETS_ASYNC,
        AmlStudio::Project::FT_SOURCE_CONTROL_POSTIONAL_ARGS,
        'aml_editor:advanced_language_features',
        AmlStudio::Project::FT_CONFLICT_RESOLUTION_V2,
        AmlStudio::Project::FT_JSONRPC_CLIENT_USE_OJ_JSON_MODE,
        'viz_settings:explorer_control_v2',
        'viz_result:show_context_menu_on_data_table',
        'dataset_explorer:viz_caching_v2',
        Connectors::RedshiftConnector::FT_USE_LATE_BINDING_VIEW_COLS,
        Connectors::RedshiftConnector::FT_RETRIEVE_EXTERNAL_TABLES,
        ImageExporters::PuppeteerRunner::FT_ALL_VIZ_HAVE_RENDER_EVENT,
        AmlStudio::Project::FT_LOOKER_MIGRATOR,
        EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD,
        Canal::Constants::FT_CAST_RESULT_DATES,
        'out-of-sync:show-banner',
        'out-of-sync:block-table-interaction',
        'ag-grid:data-table',
        'ag-grid:pivot-table',
        'ag-grid:metric-sheet',
        'ag-grid:cohort-retention',
      ].compact,
    )

    unless (aml_env = test.metadata[:aml_env]) == :unset
      ThreadContext.set(:aml_env, aml_env || AmlStudio::WorkingEnvironment::Env::Live)
    end
  end

  config.before(:each, :js) do |_test|
    get_test_tenant
    get_holistics_tenant
  end

  config.before(:each, :prosopite) do
    ENV['PROSOPITE_ENABLED'] = '1'
  end

  config.after do
    FileUtils.rm_f(TEST_MUTEX)
    ENV.clear
    env.each { |k, v| ENV[k] = v }

    Timecop.return
  end

  config.before(:each, :skip_on_circleci) do
    skip_on_circleci!
  end

  config.before(:each, :allow_forgery_protection) do |_ex|
    ActionController::Base.allow_forgery_protection = true
  end

  config.after(:each, :allow_forgery_protection) do |_ex|
    ActionController::Base.allow_forgery_protection = false
  end

  # TODO: rename config to `h_otel`?
  config.before(:each, :otel) do |test|
    # put this in otel scope to avoid breaking other request specs. this will patch our sign_in method with Warden's
    # do not apply this for integration tests
    # @tag #internal_control/h_otel/controller_instrumentaton
    config.include Devise::Test::IntegrationHelpers, type: :request unless test.metadata[:js]
    FeatureToggle.toggle_global(HOtel::FT_OTEL_ENABLED, true)
    HOtel.warn_unless_enabled!
    otel_reset
  end
  config.around(:each, otel_propagation: false) do |ex|
    propagation = OpenTelemetry.propagation
    OpenTelemetry.propagation = OpenTelemetry::Context::Propagation::NoopTextMapPropagator.new
    ex.run
  ensure
    OpenTelemetry.propagation = propagation
  end
  config.around(:each, otel_sampler: false) do |ex|
    sampler = OpenTelemetry.tracer_provider.sampler
    OpenTelemetry.tracer_provider.sampler = OpenTelemetry::SDK::Trace::Samplers::ALWAYS_OFF
    ex.run
  ensure
    OpenTelemetry.tracer_provider.sampler = sampler
  end

  config.after(:each, :api) do |test|
    next if test.metadata[:skip_schema_conform]

    assert_schema_conform(response.try(:status) || 200)
  end
  config.after(:all) do
    File.write('tmp/openapi_coverage.json', Committee::Test::Holistics::OPENAPI_SCHEMA_COVERAGE.report.to_json)
  end

  config.after(:each, :js) do |example|
    if example.exception
      folder = File.join(
        [
          '/tmp/capybara/failures/',
          example.location_rerun_argument.gsub(/^\.\/spec/, '').gsub(/\.rb:.*$/, ''),
          example.metadata[:full_description].parameterize(separator: '_'),
        ],
      )
      file_name = StringUtils.now_random_string
      # rubocop:disable Lint/Debugger
      page.save_screenshot(File.join(folder, "#{file_name}.png"))
      # rubocop:enable Lint/Debugger
      File.write(
        File.join(folder, "#{file_name}.console.log"),
        all_browser_logs.map { |l| "[#{l.level}] #{l.message}" }.join("\n"),
      )
    end
  end

  config.around(:each, :capybara_ignore_server_errors) do |example|
    Capybara.raise_server_errors = false

    example.run
  ensure
    Capybara.raise_server_errors = true
  end

  config.infer_spec_type_from_file_location!
end

PostgresCache.flushall

def clear_repos
  `rm -rf #{AmlStudio::Project::Storage.repo_root_path}`
end
