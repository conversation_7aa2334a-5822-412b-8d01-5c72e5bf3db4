Model products {
  type: 'query'
  label: 'Products'
  description: ''
  data_source_name: 'pg'
  dimension id {
    label: 'Product Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.id }};;
  }
  dimension name {
    label: 'Product Name'
    type: 'text'
    hidden: false
    definition: @sql {{ #SOURCE.name }};;
  }
  dimension vendor_id {
    label: 'Vendor Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.vendor_id }};;
  }

  owner: '<EMAIL>'
  query: @sql
    with products(
      id,
      name,
      vendor_id,
      price
    ) as (
      values
        (1, 'Product A', 1, 100),
        (2, 'Product B', 1, 200),
        (3, 'Product C', 2, 300),
        (4, 'Product D', 2, 400)
    )
    select
      id,
      name,
      vendor_id,
      price
    from
      products
  ;;

  models: []
}
