Model countries {
  type: 'query'
  label: 'Countries'
  description: ''
  data_source_name: 'pg'
  dimension id {
    label: 'Country Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.id }};;
  }
  dimension name {
    label: 'Country Name'
    type: 'text'
    hidden: false
    definition: @sql {{ #SOURCE.name }};;
  }

  owner: '<EMAIL>'
  query: @sql
    with countries(
      id,
      name
    ) as (
      values
        (1, 'Vietnam'),
        (2, 'Singapore'),
        (3, 'Thailand'),
        (4, 'Malaysia')
    )
    select
      id,
      name
    from
      countries
  ;;

  models: []
}
