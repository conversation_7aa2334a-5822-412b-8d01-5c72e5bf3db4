Dataset ecommerce_embed_2 {
  data_source_name: 'pg'

  models: [
    users,
    countries,
    orders,
    products,
    vendors
  ]

  relationships: [
    relationship(users.country_id > countries.id, true),
    relationship(orders.user_id > users.id, true),
    relationship(orders.product_id > products.id, true),
    relationship(products.vendor_id > vendors.id, true)
  ]

  permission country_access {
    field: ref('countries', 'name')
    operator: 'matches_user_attribute'
    value: 'country_name'
  }
}
