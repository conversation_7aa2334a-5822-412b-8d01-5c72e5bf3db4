# typed: false
require 'rails_helper'

# Guide for debugging: https://www.notion.so/holistics/Visual-Image-Exportings-to-PDF-PNG-a6c8ba3e73ab4b19b0e18329f964dfbe#09096aab1b4d451a9d101a23adc1bcee

describe ImageExporters::QueryReport, require_puppeteer: true do
  let(:user) { get_test_admin }
  let(:job) { FactoryBot.create :job, user: user }
  let(:params) { {} }
  let(:qr) { FactoryBot.create :query_report }
  let(:format) { 'pdf' }
  let(:cache_key) do
    vals, err =
      if qr.dataset_based?
        [{ cache_key: 'wont_be_used' }, nil]
      else
        qr.run_and_process(user, params)
      end
    raise err if err.present?

    vals[:cache_key]
  end
  subject { described_class.new(qr, job: job, permission_rules: {}, query_processing_timezone: '') }

  def export
    subject.export(cache_key, format: format)
  rescue StandardError => e
    puts job.logs.map { |l| l.message }.join("\n")
    raise
  end

  # On some machines (Mac ones so far), the font rendering is different, causing the width to vary
  # Haven't figured out the actual root cause :((
  # WARNING: However, height should always be correct, because it affects multi-page rendering
  WIDTH_ACCEPTABLE_DIFF = ENV['CIRCLECI'] ? 0 : 10

  def test_page_sizes!(expected)
    # soft-test for cropping
    pattern = ImageExporters::Constants::PAGE_SIZE_LOG_PATTERN
    page_sizes = job.logs.map { |l| l.message.scan(pattern) }.flatten(1)
                    .map { |page_num, w, h| [page_num.to_i, [w.to_i, h.to_i]] }.to_h
    page_sizes.each do |page_num, page_size|
      expected_page_size = expected[page_num]
      expect(expected_page_size.present?).to be(true), "Page #{page_num} was not exported"
      width, height = page_size
      expect(expected_page_size[0] - width <= WIDTH_ACCEPTABLE_DIFF).to eq(true),
                                                                        "Page #{page_num} has mismatching width. Expected: #{expected_page_size[0]}. Got: #{width}"
      expect(expected_page_size[1]).to eq(height),
                                       "Page #{page_num} has mismatching height. Expected: #{expected_page_size[1]}. Got: #{height}"
    end
  end

  before do
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_tenant(ImageExporters::PuppeteerRunner::FT_PDF_EXPORT, user.tenant_id, true)
  end

  context 'single page image report' do
    include_context 'simple_image_report'

    it 'can export pdf successfully' do
      file_content = export

      test_success_pdf!(file_content, job)
      # expected_content = fixture_read_file('image_exporters/report_prawn.pdf')
      # expect(strip_date_meta_from_pdf(file_content)).to eq strip_date_meta_from_pdf(expected_content)
      test_page_sizes!({ 0 => [550, 198], 1 => [550, 198] })
    end

    context 'many rows' do
      include_context 'multi_pages_image_report'
      it 'still exports 1 page, because FT is disabled' do
        file_content = export

        test_success_pdf!(file_content, job)
        test_page_sizes!({ 0 => [550, 2970], 1 => [550, 2970], 2 => [550, 2970] })
      end
    end

    context 'chart viz' do
      include_context 'image_report_line'

      it 'can export pdf successfully' do
        file_content = export

        test_success_pdf!(file_content, job, total_page: 2)
        test_page_sizes!({
                           0 => [890, 540],
                           1 => [890, 540],
                           2 => [590, 425],
                         })
      end

      context 'many rows' do
        include_context 'multi_pages_image_report_line'

        it 'can export pdf successfully with no extra table pages' do
          file_content = export

          test_success_pdf!(file_content, job, total_page: 2)
          test_page_sizes!({
                             0 => [1060, 2759],
                             1 => [890, 540],
                             2 => [1060, 2759],
                           })
        end
      end
    end

    context 'pivot table' do
      include_context 'image_pivot_table'
      let(:num_rows) { 300 }
      let(:num_columns) { 10 }

      it 'can export pdf successfully with no extra table pages' do
        file_content = export

        test_success_pdf!(file_content, job)
        test_page_sizes!({
                           0 => [710, 898],
                           1 => [710, 898],
                         })
      end
    end

    xit 'can export fast' do
      10.times do |i|
        puts "No. #{i + 1}".yellow
        begin
          bm = Utils::BenchmarkContext.new
          time = bm.run 'exporting' do
            file_content = subject.export(cache_key, format: 'pdf')
            expect(file_content.present?).to be true
            expect(job.logs.any? { |l| l.message == 'Finish rendering' }).to be true
          end
        rescue StandardError => e
          puts job.logs.map { |l| l.message }.join("\n")
          raise
        end
        JobLog.destroy_all
      end
    end
  end

  describe 'multi pages image export' do
    before do
      FeatureToggle.toggle_tenant(ImageExporters::PuppeteerRunner::FT_PDF_EXPORT_ALL_ROWS, user.tenant_id, true)
      FeatureToggle.toggle_tenant('ag-grid:data-table', user.tenant_id, true)
      FeatureToggle.toggle_tenant('ag-grid:pivot-table', user.tenant_id, true)
    end
    context 'table viz' do
      include_context 'multi_pages_image_report'

      it 'can export multiple-page pdf successfully' do
        file_content = export

        test_success_pdf!(file_content, job, total_page: 3)
        test_page_sizes!({ 0 => [550, 2970], 1 => [550, 2970], 2 => [550, 2970], 3 => [550, 2970] })
      end

      context 'when hiding all fields' do
        let(:qr) { FactoryBot.create :report_with_table_fields_and_custom_labels }
        before do
          FeatureToggle.toggle_tenant('table:hide_fields', user.tenant_id, true)
        end

        it 'render all hidden fields banner' do
          qr.viz_setting.fields[:table_fields][0][:is_hidden] = true
          qr.viz_setting.fields[:table_fields][1][:is_hidden] = true
          file_content = export

          test_success_pdf!(file_content, job)
          test_page_sizes!({ 0 => [550, 150], 1 => [550, 150] })
        end
      end
    end

    context 'chart viz' do
      include_context 'multi_pages_image_report_line'

      it 'can export multiple-page pdf successfully' do
        file_content = export

        test_success_pdf!(file_content, job, total_page: 4)
        test_page_sizes!({
                           0 => [1060, 2970],
                           1 => [890, 540],
                           2 => [1060, 2970],
                           3 => [1060, 2970],
                           4 => [1060, 2970],
                         })
      end
    end

    context 'pivot table' do
      include_context 'image_pivot_table'
      let(:num_rows) { 300 }
      let(:num_columns) { 10 }

      it 'can export pdf successfully with all table pages' do
        file_content = export

        test_success_pdf!(file_content, job, total_page: 3)
        test_page_sizes!({
                           0 => [710, 2970],
                           1 => [710, 2970],
                           2 => [710, 2970],
                           3 => [710, 2970],
                         })
      end
    end
  end
end
