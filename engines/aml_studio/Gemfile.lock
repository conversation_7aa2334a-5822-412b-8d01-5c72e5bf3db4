PATH
  remote: ../../gems
  specs:
    source_control (0.9.0)
      activesupport
      coding_utils (~> 1.0)
      dotenv
      http
      json_rpc_client (~> 0.4)
      logstash-logger
      oj
      puma
      rugged (= 1.7.2)
      sinatra (~> 2.2)
      sorbet-runtime

PATH
  remote: .
  specs:
    aml_studio (0.2.0)
      active_model_serializers
      cancancan
      oj (~> 3.16)
      rails (~> 6.1.7.6)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (********)
      actionpack (= ********)
      activesupport (= ********)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      mail (>= 2.7.1)
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activesupport (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (********)
      actionpack (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      nokogiri (>= 1.8.5)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_model_serializers (0.10.14)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.6)
    activemodel (********)
      activesupport (= ********)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
    activestorage (********)
      actionpack (= ********)
      activejob (= ********)
      activerecord (= ********)
      activesupport (= ********)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (********)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    base64 (0.2.0)
    bigdecimal (3.1.9)
    builder (3.2.4)
    byebug (11.1.3)
    cancancan (3.5.0)
    case_transform (0.2)
      activesupport
    coderay (1.1.3)
    coding_utils (1.0.1)
      activesupport (~> 6.1.0)
      connection_pool (~> 2.2)
      hashdiff (~> 1.1)
      lz4-ruby (~> 0.3.3)
      msgpack_rails (~> 0.4.3)
      oj (~> 3.13)
      redis (~> 4.5.0)
      rspec (~> 3.0)
      sorbet-coerce (~> 0.7.0)
      sorbet-runtime (~> 0.5)
    concurrent-ruby (1.2.3)
    connection_pool (2.4.1)
    crass (1.0.6)
    date (3.3.4)
    diff-lcs (1.5.1)
    docile (1.4.0)
    domain_name (0.6.20240107)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    erubi (1.12.0)
    factory_bot (5.2.0)
      activesupport (>= 4.2.0)
    factory_bot_rails (5.2.0)
      factory_bot (~> 5.2.0)
      railties (>= 4.2.0)
    ffi (1.16.3)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.1.2)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    json (2.7.2)
    json_rpc_client (0.4.3)
      activesupport (~> 6.1)
      coding_utils (~> 1.0)
      http (~> 5.2.0)
      sorbet-runtime (~> 0.5)
    jsonapi-renderer (0.2.2)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logstash-event (1.2.02)
    logstash-logger (0.26.1)
      logstash-event (~> 1.2)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lz4-ruby (0.3.3)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    method_source (1.1.0)
    mini_mime (1.1.5)
    minitest (5.25.1)
    msgpack (1.7.2)
    msgpack_rails (0.4.3)
      activesupport (>= 3.0)
      msgpack
    mustermann (2.0.2)
      ruby2_keywords (~> 0.0.1)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.3)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    oj (3.16.9)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    ostruct (0.6.1)
    pg (1.5.6)
    polyfill (1.9.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    public_suffix (5.0.5)
    puma (6.5.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.16)
    rack-protection (2.2.4)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (********)
      actioncable (= ********)
      actionmailbox (= ********)
      actionmailer (= ********)
      actionpack (= ********)
      actiontext (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activestorage (= ********)
      activesupport (= ********)
      bundler (>= 1.15.0)
      railties (= ********)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rake (13.2.1)
    redis (4.5.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    ruby2_keywords (0.0.5)
    rugged (1.7.2)
    safe_type (1.1.1)
    simplecov (0.17.1)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    sinatra (2.2.4)
      mustermann (~> 2.0)
      rack (~> 2.2)
      rack-protection (= 2.2.4)
      tilt (~> 2.0)
    sorbet-coerce (0.7.0)
      polyfill (~> 1.8)
      safe_type (~> 1.1, >= 1.1.1)
      sorbet-runtime (>= 0.4.4704)
    sorbet-runtime (0.5.11338)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    thor (1.3.2)
    tilt (2.3.0)
    timeout (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.18)

PLATFORMS
  arm64-darwin-24
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-darwin-23
  x86_64-linux

DEPENDENCIES
  aml_studio!
  dotenv-rails
  factory_bot (~> 5.2)
  factory_bot_rails
  pg
  pry
  pry-byebug
  rspec
  rspec-rails
  rspec_junit_formatter
  simplecov (~> 0.17.1)
  source_control!

BUNDLED WITH
   2.4.22
