# frozen_string_literal: true
# typed: false

# USAGE:
# For empty database
#   rails db:seed
#
# For existing database
#  rails db:seed:replant
#
# Enable these environment keys to run this script
#  RAILS_ENV=production # To run this script on production
#  ENABLE_DBSEEDS_FOR_PRODUCTION=true # To run this script on production
#  DISABLE_DATABASE_ENVIRONMENT_CHECK=1 # To skip database environment check
#  DBSEED_INIT_STAFF=true # To create staff user

def fetch_env(key)
  env = ENV.fetch(key)
  raise "Missing ENV `#{key}`" if env.blank?

  env
end

# Init ID sequences
def initialize_id_sequences
  id_range_start = fetch_env('H_GUID_FIRST_RANGE_START').to_i
  id_range_end = fetch_env('H_GUID_FIRST_RANGE_END').to_i

  HGuid::AssignIdRange.new.call(range_start: id_range_start, range_end: id_range_end)

  jumper = HGuid::JumpSequence.new
  stats = HGuid::CollectStats.new.call
  stats.each do |s|
    jumper.call(
      sequence_name: s.sequence_fq_name,
      min_value: id_range_start,
      max_value: id_range_end,
    )
  end
end

initialize_id_sequences

# Init first tenant and user
tenant_name = 'Holistics'
tenant_uname = 'holistics'
admin_email = '<EMAIL>'
analyst_email = '<EMAIL>'
business_user_email = '<EMAIL>'

if ENV['ON_PREMISE'].is_true?
  tenant_name = ENV.fetch('ON_PREMISE_TENANT_NAME')
  tenant_uname = tenant_name.gsub(/[^0-9A-Za-z]/, '')
  admin_email = ENV.fetch('ON_PREMISE_SUPER_ADMIN_EMAIL', nil) || '<EMAIL>'
end

tenant_holistics = Tenant.create name: tenant_name, uname: tenant_uname
admin_user = User.find_or_create_by(email: admin_email) do |u|
  u.password = 'secretpassword'
  u.role = User::ROLE_ADMIN
  u.name = 'Admin'
  u.tenant = tenant_holistics
end
User.find_or_create_by(email: analyst_email) do |u|
  u.password = 'secretpassword'
  u.role = User::ROLE_ANALYST
  u.name = 'Analyst'
  u.tenant = tenant_holistics
end
User.find_or_create_by(email: business_user_email) do |u|
  u.password = 'secretpassword'
  u.role = User::ROLE_BUSINESS_USER
  u.name = 'Business User'
  u.tenant = tenant_holistics
end

# Updated Jun 1st, 2022
# Init second and thir tenant with same user's email address (multi-tenant setup)
tenant_name1 = 'Holistics 1'
tenant_uname1 = 'holistics1'
tenant_name2 = 'Holistics 2'
tenant_uname2 = 'holistics2'

admin_email1 = '<EMAIL>'

tenant_holistics1 = Tenant.create name: tenant_name1, uname: tenant_uname1
TenantDomain.create! tenant_id: tenant_holistics1.id, domain_name: 'holistics1.holistics.com'
User.create!(email: admin_email1, password: 'secretpassword1', role: 'admin', name: 'Admin', tenant: tenant_holistics1)

tenant_holistics2 = Tenant.create name: tenant_name2, uname: tenant_uname2
TenantDomain.create! tenant_id: tenant_holistics2.id, domain_name: 'holistics2.holistics.com'
User.create!(email: admin_email1, password: 'secretpassword2', role: 'admin', name: 'Admin', tenant: tenant_holistics2)

# Updated 26 Aug 2019
# Init set of Feature toggles

# This is enabled by default for all tenants
basic_fts = %w[
  chart_annotations
  data_manager
  google_sheet_source_with_sheet_api
  mailgun_webhook.notify_owner
  integrations:slack
  report:direct_export
  data_schedules:test_execution
  autocomplete
  email_schedule:cron
  slack_schedule:fresh_report_data
  email_schedule:fresh_report_data
  search:folder
  email_schedule:attachment_password
  integrations:sftp
  data_sources:google_analytics
  shareable_link:password
  fast_excel_export
  data_sources:automated_reverse_tunnel
  filterable:restrict_parent_child_filters
  conditional_formatting
  gsheet_schedule:fresh_report_data
  slack_schedules:dashboard
  report:version_history
  public_users:drilldowns
  jobs:pending_jobs_redis_cache
  new_ability
  presto:do_not_drop_previous_tables_of_view
  sample_data:stop_cloning_data_models
  exportings:pdf
  data_schedule:failure_notif_for_slack_and_gsheet
  activity_logs:index
  embed_link:reject_invalid_identifier_var_values
  data_models:explore_controls
  data_models:persistence
  billing:hard_restriction
  public_users:cache_params_without_token
  data_models:show_relationships
  email_schedule:include_holistics_logo_footer
  exportings:include_holistics_logo
  report_widget:dedup_job
  adhoc:use_gzip
  ui_ux:friendly_loading
  sql_report:creation
  aml_studio:enable
  data_models:aml_integration
  data_models:aml
  data_models:show_aml_tab
  data_source:enable_schema_info
  reporting_nav:enabled
  schema_synchronization
  shareable_link:enabled
  schema_explorer:enable_refresh
  billings:plans_v3
  data_source:enable_schema_info
  commenting
  use_new_viz_exporter_to_render_data_tables
  transform:drop_tmp_table_presto
  data_sets:custom_expression
  aml_studio:git_sync
  aml_studio:search_and_replace
]

# https://www.notion.so/holistics/Holistics-3-0-Docs-2019-WIP-a26abe0583744d9d9b3e7b0d62dd5981#4e4f7609b8304b56904ce6e3fe8cd143
holistics_3_fts = %w[
  data_models:custom_field
  data_models:data_model_panel
  data_models:manager
  data_sets:enabled
  personal_workspace:enabled
  report:select_data_model
  data_models:new_sql_generation
  dashboards_v3:date_drill
  pop:enabled
]

# For any development relating to Holistics 4.0
holistics_4_fts = %w[
  data_models:sql_generation_gem
  data_models:sql_generation_gem_on_single_model
  aml_studio:enable
]
aggrid_fts = %w[
  ag-grid:data-table
  ag-grid:pivot-table
  ag-grid:metric-sheet
  ag-grid:cohort-retention
]
table_interaction_fts = %w[
  table:reorder_columns
  table:freeze_columns
  table:hide_fields
  table:auto_size
  table:remove_columns
  table:rename_columns
  table:aggregate_columns
  table:add_new_columns
  table:add_edit_calculations
  viz:action
  table:manage_actions
]
pivot_interaction_fts = %w[
  pivot:freeze_columns
  pivot:hide_fields
  pivot:auto_size
  pivot:remove_columns
  pivot:rename_columns
  pivot:aggregate_columns
  pivot:add_edit_calculations
  pivot:transpose
]

table_fts = [
  *aggrid_fts,
  *table_interaction_fts,
  *pivot_interaction_fts,
]

basic_fts.each do |feature_key|
  ft = FeatureToggle.find_or_create_by(key: feature_key)
  ft.toggle_mode = 'enabled'
  ft.save!
end

holistics_3_fts.each do |feature_key|
  ft = FeatureToggle.find_or_create_by(key: feature_key)
  ft.toggle_mode = 'enabled'
  ft.save!
end

holistics_4_fts.each do |feature_key|
  ft = FeatureToggle.find_or_create_by(key: feature_key)
  ft.toggle_mode = 'enabled'
  ft.save!
end

table_fts.each do |feature_key|
  ft = FeatureToggle._create_new_ft_from(feature_key)
  ft.toggle_mode = 'enabled'
  ft.save!
end

# Create default Global Config
GlobalConfig.set_if_nil('user_export_jobs:cutoff_hours', 24)
GlobalConfig.set_if_nil('pending_jobs_polling_interval', 1000)
GlobalConfig.set_if_nil('postgres_cache:delete_expired_metadata_batch_size', 0)
GlobalConfig.set_if_nil(DataImport::REEXECUTION_LIMIT_GLOBAL_CONFIG, 3)
GlobalConfig.set_if_nil(DataTransform::REEXECUTION_LIMIT_GLOBAL_CONFIG, 3)

# Create default data for on-premise version
if Rails.env == 'production' && ENV['ON_PREMISE']
  TenantSubscription.create(tenant_id: tenant_holistics.id, status: 'active')
end

# Init for development
if Rails.env == 'development' || (Rails.env == 'production' && ENV['ENABLE_DBSEEDS_FOR_PRODUCTION'].is_true?)
  FeatureToggleGroups::UpsertVersions.new.call_all!

  TenantSubscription.create!(
    tenant_id: tenant_holistics.id,
    status: 'active',
  )

  rc = Rails.configuration.database_configuration[Rails.env].rsk

  dbconfig = {
    dbname: rc[:database],
    user: rc[:username],
    password: rc[:password],
    host: rc[:host],
    port: rc[:port] || 5432,
  }

  ds_1 = DataSource.create!(
    name: 'development_1',
    dbconfig: dbconfig,
    dbtype: 'postgresql',
    tenant_id: Tenant.first.id,
  )
  ds_1.synchronize_schema

  ds_2 = DataSource.create!(
    name: 'development_2',
    dbconfig: dbconfig,
    dbtype: 'postgresql',
    tenant_id: Tenant.first.id,
  )
  ds_2.synchronize_schema

  data_sources = [ds_1, ds_2]

  # Create Report Cat to store reports V2
  report_cat_v2 = ReportCategory.create!(
    name: 'Reports V2',
    tenant_id: tenant_holistics.id,
    parent_id: 0,
    is_workbook: false,
    hide_reports: false,
    owner_id: admin_user.id,
  )

  # Create Dasboard V3 to store reports V3
  dashboard_v3 = Dashboard.create!(
    title: 'Dashboard V3',
    tenant_id: tenant_holistics.id,
    items: {},
    owner_id: admin_user.id,
    category_id: 0,
    version: 3,
    description: '',
    cross_filtering_enabled: true,
    settings: {},
  )

  # Initial survey answers
  SurveyAnswer.create!(
    question_key: 'onboarding:welcome_screen',
    data: { passed: true },
    user_id: admin_user.id,
    tenant_id: tenant_holistics.id,
  )

  # This code loads each smaller seed file in db/seeds in a sorting order
  # In the future, if you want to add a new file inside db/seeds for more data:
  # 1. Create a file with this rule: xxx_seed_name (xxx is the order number of the seed)
  #
  # 2. Inside the seed file, write a function with necessary input. For example:
  # create_something(tenant_holistics, admin_user, data_sources, data_models)
  #
  # 3. Use ModelName.create! to create new records in your seed file.
  # You could try to create the objects in the Holistics app and use the rails console to
  # list the attributes for your creation function in the seed file.
  Dir[File.join(Rails.root, 'db', 'seeds', '*.rb')].sort.each { |seed| require seed }

  # Create Data Models
  data_models = create_data_models(tenant_holistics, admin_user, data_sources)

  # Create Data Sets
  data_sets = create_data_sets(tenant_holistics, admin_user, data_sources, data_models)

  # Create Email Schedules and Shareable Link
  create_email_scheulde_and_shareable_link(tenant_holistics, admin_user, data_sources)

  # Create Reports V2
  create_v2_charts(tenant_holistics, admin_user, data_sources, report_cat_v2)

  # Create Reports V3
  create_v3_charts(tenant_holistics, admin_user, data_sources, data_models, data_sets, dashboard_v3)

  # Create Date and Number Formats
  create_date_number_formats(tenant_holistics, admin_user, data_sources, data_models, data_sets)

  # Create dynamic filters
  create_dynamic_filters(tenant_holistics, admin_user, data_sources, data_models, data_sets)

  # Create report with relationship
  create_v3_report_with_relationship(tenant_holistics, admin_user, data_sources, data_models, data_sets)

  # Create report to test conditional formatting
  create_conditional_formatting(tenant_holistics, admin_user, data_sources, data_models, data_sets)

  # Create V2 filters
  create_v2_filters(tenant_holistics, admin_user, data_sources)

  # Init users for Holistics Staff on staging env.
  # The emails here is copy from the super admin list
  if ENV['DBSEED_INIT_STAFF'].is_true?
    staff_emails = User::SUPER_ADMIN_EMAILS
    staff_emails.each do |email|
      User.find_or_create_by(email: email) do |u|
        u.password = SecureRandom.hex(20)
        u.role = User::ROLE_ADMIN
        u.name = email[/^(.*)(@)/, 1]
        u.tenant = tenant_holistics
      end
    end
  end
end
