
# References:
# - https://circleci.com/docs/2.0/configuration-reference
# - https://circleci.com/docs/2.0/configuration-reference/#ending-a-job-from-within-a-step
# - https://github.com/holistics/holistics-circleci-image
---
version: 2.1
orbs:
  slack: circleci/slack@4.12.5
  jq: circleci/jq@2.2.0

shared_context: &shared_context
  - context:
      - slack-app-v4
      - 1password-connect-token

# Executors
executors:
  default: &default_executor
    working_directory: ~/holistics
    resource_class: small
    docker:
      - &holistics_image
        image: registry.gitlab.com/holistics/holistics-ci-image/holistics-ci-image:v7.1
        auth:
          username: $GITLAB_REGISTRY_USER
          password: $GITLAB_REGISTRY_PASSWORD
        environment:
          RAILS_ENV: test
          ENABLE_COV: "1"
      - &postgres_image
        image: cimg/postgres:15.4
        # https://rhaas.blogspot.com/2010/06/postgresql-as-in-memory-only-database_24.html
        command: ["postgres", "-c", "fsync=off", "-c", "full_page_writes=off"]
      - &redis_image
        image: cimg/redis:7.2.4

  rails:
    <<: *default_executor
    docker:
      - *holistics_image
      - *postgres_image
      - *redis_image
      - &canal_query_image
        image: ghcr.io/holistics/canal-query:2.0.1
        name: canal-query
        auth:
          username: $GITHUB_DEPLOY_USER
          password: $GITHUB_DEPLOY_TOKEN
        environment:
          GRPC_GO_LOG_VERBOSITY_LEVEL: 99
          HOTEL_USE_STDOUT: 1
          CANAL_LAKE_HOST: canal-lake
          CANAL_LAKE_PORT: 11320
      - &canal_lake_image
        image: ghcr.io/holistics/canal-lake:2.0.0
        name: canal-lake
        auth:
          username: $GITHUB_DEPLOY_USER
          password: $GITHUB_DEPLOY_TOKEN
        environment:
          GRPC_GO_LOG_VERBOSITY_LEVEL: 99
          HOTEL_USE_STDOUT: 1
      # TODO: split data source tests and run data source containers for those tests only
      - &mysql_image
        image: cimg/mysql:5.7
        environment:
          MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
          MYSQL_ROOT_HOST: "%"
          MYSQL_ROOT_PASSWORD: "password"
          MYSQL_USER: "ubuntu"
          MYSQL_PASSWORD: "test"
          MYSQL_DATABASE: "circle_test"
        command: [--character-set-server=utf8mb4,--collation-server=utf8mb4_unicode_ci]
      # TODO: fix x509 negative serial certificate
      # - &sqlserver_image
      #   image: mcr.microsoft.com/mssql/server:2019-latest
      #   environment:
      #     ACCEPT_EULA: 'Y'
      #     MSSQL_SA_PASSWORD: 'Password1!'
      - image: mongo:3.6.23
      - image: clickhouse/clickhouse-server:**********-alpine
        environment:
          CLICKHOUSE_GRPC_PORT: 9000
          CLICKHOUSE_HTTP_PORT: 8123
      - image: registry.gitlab.com/holistics/oracle-xe-11g:latest
        auth:
          username: $GITLAB_REGISTRY_USER
          password: $GITLAB_REGISTRY_PASSWORD
      - image: registry.gitlab.com/holistics/holistics-tunnel-ci-image:v0.1
        name: tunnel_server
        auth:
          username: $GITLAB_REGISTRY_USER
          password: $GITLAB_REGISTRY_PASSWORD
        environment:
          TUNNEL_USER: tunnel
          TUNNEL_SSH_PORT: 52323
          # spec/fixtures/ssh_keys/holistics.pub
          PUBLIC_KEY: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCgF7zXF316twhjyjQRdFDxvoW9wFhOkrBV2ZT68anlg+UU6EOy+j/YlWwamfe9EdoXuaATB/hG5XuHBeO4cNDliz97xacq/1M8VhBnL/uo1rXYgkFVB0Qa4PpsINmocroOaUmKqmPhVGDV2uefks2HCiB1DpwaZeSiWaYv7UC8hBvtrPPZzS6U1ytyAshwSFSJHKtGcYlB7ETS5DGWlIqkmKau2pSrOVa7U3KZSoKv6u0gEu3idEAEDXK/3+t59r8KttbdvKCY+qYvqIRDRck2NtjHMFydu3ChrfIj87fLRsB5taybQgrDULC9cgzAQU1jkbhG3EUL9RYzWYQJehOYcKdqRIgmYS+WvvX06WPvdIoc5w6CRwwULjBoRmKef/M5hOnsDLlpiznZUSHYJsfPEjURU+iwG797dFV2UIA2o2ZR+RbhcC3Qwylfm+K6pyalgd6eCeCi53R/hepsZIvJOv2kxP9iGh06t3lO5oQarfBcpFPiVVcqOJaP8pG3OEM= circle@ci"
  default_with_canal:
    <<: *default_executor
    docker:
      - *holistics_image
      - *postgres_image
      - *redis_image
      - *canal_query_image
      - *canal_lake_image
  minimal:
    <<: *default_executor
    docker:
      - *holistics_image
  assets_compiler: &assets_compiler
    <<: *default_executor
    docker:
      - *holistics_image
      - *postgres_image
  zeitwerk:
    <<: *assets_compiler

# Commands
commands:
  setup_npmrc_profile:
    steps:
      - run:
          name: Setup npmrc profile
          command: |
            echo "@holistics:registry=https://npm.pkg.github.com/" >> ~/holistics/.npmrc
            echo "//npm.pkg.github.com/:_authToken=$NPM_TOKEN"     >> ~/holistics/.npmrc

  yarn_install:
    steps:
      - run:
          name: Combine package.json files in workspaces
          command: |
            cat $(find {package.json,app/javascript,packages} -maxdepth 2 -name package.json -type f | sort) > combined-packages.txt
            node --version > .node-version
      - restore_cache:
          key: holistics-yarn-v4-{{ checksum "combined-packages.txt" }}-{{ checksum "yarn.lock" }}-{{ checksum ".node-version" }}
      - run:
          name: Unarchive combined-packages.tar
          command: if [[ -f combined-packages.tar ]]; then tar -xf combined-packages.tar ; fi
      - run:
          name: Yarn install
          command: |
            set -euo pipefail
            yarn policies set-version ${YARN_VERSION:-1.19.1}
            npm run ci:yarn:install

  yarn_cache:
    steps:
      - run:
          name: Archive combined-packages.tar
          command: |
            tar -cf combined-packages.tar \
              node_modules \
              app/javascript/node_modules \
              packages/*/node_modules
      - save_cache:
          key: holistics-yarn-v4-{{ checksum "combined-packages.txt" }}-{{ checksum "yarn.lock" }}-{{ checksum ".node-version" }}
          paths:
            - combined-packages.tar
            - .cache/puppeteer

  bundle_install:
    parameters:
      frozen:
        description: true run bundle install with --frozen option
        type: boolean
        default: true
    steps:
      - restore_cache:
          key: v3-holistics-{{ arch }}-{{ checksum ".ruby-version" }}-{{ checksum "Gemfile.lock" }}-<< parameters.frozen >>
      - run:
          name: Install bundler
          command: gem install bundler -v 2.4.22
      - run:
          name: Configure GitHub rubygems account
          command: bundle config --global rubygems.pkg.github.com $GITHUB_DEPLOY_USER:$GITHUB_DEPLOY_TOKEN
      - run:
          name: Bundle install
          command: |
            if << parameters.frozen >>; then
              bundle install --path vendor/bundle --frozen
            else
              bundle install --path vendor/bundle
            fi

  bundle_cache:
    parameters:
      frozen:
        description: true run bundle install with --frozen option
        type: boolean
        default: true
    steps:
      - save_cache:
          key: v3-holistics-{{ arch }}-{{ checksum ".ruby-version" }}-{{ checksum "Gemfile.lock" }}-<< parameters.frozen >>
          paths:
            - vendor/bundle

  vite_compile_save:
    steps:
      - restore_cache:
          name: Use build_dev cache of current branch or master
          keys:
            - &vite-compiled-cache vite-compiled-cache-{{ .Branch }}-{{ .Revision }}
            - vite-compiled-cache-{{ .Branch }}-
            - vite-compiled-cache-master-
      # used for run_pptr_test, run_integration_test
      - run:
          name: vite compile
          command: bin/vite build
          environment:
            NO_INITIALIZATION: true
            NODE_OPTIONS: --max-old-space-size=7000
      - persist_to_workspace:
          root: .
          paths:
            - public/vite-dev
            - tmp/cache/vite
      - save_cache:
          name: Save build_dev cache
          key: *vite-compiled-cache
          paths:
            - public/vite-dev
            - tmp/cache/vite

  vite_compile_pptr_save:
    parameters:
      use-pptr-cache:
        description: true to reuse build_puppeteer_related cache, false to trigger plain new build
        type: boolean
        default: true
    steps:
      - when:
          condition: << parameters.use-pptr-cache >>
          steps:
            - restore_cache:
                name: Use build_puppeteer_related cache of current branch or master branch
                keys:
                  - &vite-pptr-cache vite-pptr-cache-{{ .Branch }}-{{ .Revision }}
                  - vite-pptr-cache-{{ .Branch }}-
                  - vite-pptr-cache-master-
      # build exporter assets (packs/pptr_*)
      - run:
          name: Build pptr dashboard
          command: bin/vite build
          environment:
            NO_INITIALIZATION: true
            PACK: pptr_dashboard
            VITE_RUBY_PUBLIC_OUTPUT_DIR: vite/pptr_dashboard
            RAILS_ENV: production
            NODE_ENV: production
            NODE_OPTIONS: --max-old-space-size=5000
      - run:
          name: Build pptr report
          command: bin/vite build
          environment:
            NO_INITIALIZATION: true
            PACK: pptr_report
            VITE_RUBY_PUBLIC_OUTPUT_DIR: vite/pptr_report
            RAILS_ENV: production
            NODE_ENV: production
            NODE_OPTIONS: --max-old-space-size=5000
      - run:
          name: Build pptr legacy_viz
          command: bin/vite build
          environment:
            NO_INITIALIZATION: true
            PACK: pptr_legacy_viz
            VITE_RUBY_PUBLIC_OUTPUT_DIR: vite/pptr_legacy_viz
            RAILS_ENV: production
            NODE_ENV: production
            NODE_OPTIONS: --max-old-space-size=5000
      - run:
          name: Build pptr single_viz
          command: bin/vite build
          environment:
            NO_INITIALIZATION: true
            PACK: pptr_single_viz
            VITE_RUBY_PUBLIC_OUTPUT_DIR: vite/pptr_single_viz
            RAILS_ENV: production
            NODE_ENV: production
            NODE_OPTIONS: --max-old-space-size=5000
      - persist_to_workspace:
          root: .
          paths:
            - public/vite
      - save_cache:
          name: Save pptr cache
          key: *vite-pptr-cache
          paths:
            - public/vite
            - tmp/cache/vite

  vite_compile_restore:
    steps:
      - attach_workspace:
          at: .

  install_chrome:
    steps:
      - run:
          command: |
            node --version > .node-version
      - restore_cache:
          key: v1-holistics-{{ arch }}-{{ checksum ".node-version" }}-{{ checksum "yarn.lock" }}-{{ checksum ".chrome-version" }}
      - run:
          name: Install Chrome
          command: |
            INSTALL_PATH="$(pwd)/.cache/browsers" \
              ruby install_chrome.rb
      - save_cache:
          key: v1-holistics-{{ arch }}-{{ checksum ".node-version" }}-{{ checksum "yarn.lock" }}-{{ checksum ".chrome-version" }}
          paths:
            - .cache/browsers

  setup_env:
    steps:
      - run:
          name: Setup environment
          command: |
            cp config/database.yml.circleci config/database.yml
            cp .env.circleci .env

  setup_database:
    steps:
      - run:
          name: Setup database
          command: bundle exec rake db:create db:schema:load db:partition db:add_triggers

  run_aml_server:
    steps:
      - run:
          name: Run AML Server
          working_directory: ~/holistics/packages/aml-server
          command: yarn jsonrpc-server
          background: true
      - run:
          name: Wait for AML Server
          command: timeout 10 sh -c 'until nc -z $0 $1; do sleep 1; done' localhost 7004

  run_source_control_server:
    steps:
      - restore_cache:
          key: v1-source-control-{{ arch }}-{{ checksum ".ruby-version" }}-{{ checksum "gems/source_control/Gemfile.lock" }}
      - run:
          name: Install source control gems
          working_directory: ~/holistics/gems/source_control
          command: |
            gem install bundler -v 2.4.22
            bundle install --path vendor/bundle
      - run:
          name: Setup source control environment
          working_directory: ~/holistics/gems/source_control
          command: cp .env.circleci .env
      - save_cache:
          key: v1-source-control-{{ arch }}-{{ checksum ".ruby-version" }}-{{ checksum "gems/source_control/Gemfile.lock" }}
          paths:
            - gems/source_control/vendor/bundle
      - run:
          name: Run Source Control Server
          working_directory: ~/holistics/gems/source_control
          command: bundle exec puma -C config/puma_dev.rb
          background: true
      - run:
          name: Wait for Source Control Server
          command: timeout 10 sh -c 'until nc -z $0 $1; do sleep 1; done' localhost 7800

  run_node_utils_server:
    steps:
      - run:
          name: Run Node Utils Server
          working_directory: ~/holistics/packages/node_utils
          command: yarn server
          background: true
      - run:
          name: Wait for Node Utils Server
          command: timeout 10 sh -c 'until nc -z $0 $1; do sleep 1; done' localhost 3555

  upload_vite_compiled_cache:
    parameters:
      production-mode:
        description: true to upload to production s3 assets bucket, false to dev bucket
        type: boolean
        default: false
    steps:
      - run:
          name: Upload vite pre-built cache to S3
          command: |
            TAR_NAME="$(git rev-parse --short=10 HEAD).tar.gz"
            if ! <<parameters.production-mode>>; then TAR_NAME="staging_${TAR_NAME}" ; fi
            echo "tar name: ${TAR_NAME}"
            tar -czf ${TAR_NAME} tmp/cache/vite public/packs public/vite
            aws s3 cp ${TAR_NAME} ${S3_DEV_WEBPACK_BUCKET}/${TAR_NAME}

  setup_rails_app_env:
    steps:
      - run:
          # Task assets:precompile would need Rails app to be initialized
          name: Setup rails app environment for precompiling assets
          command: |
            cp config/database.yml.circleci config/database.yml
            cp .env.sample .env

  precompile_assets:
    parameters:
      production-mode:
        description: true to use production cdn host, false to use staging host
        type: boolean
        default: false
    steps:
      - run:
          name: Vite precompile assets
          command: |
            if <<parameters.production-mode>>;
            then
              CDN_HOST=assets.holistics.io
              echo "Precompile assets using production cdn host"
            else
              CDN_HOST=staging-assets.holistics.io
              echo "Precompile assets using staging cdn host"
            fi
            CDN_HOST=${CDN_HOST} bundle exec rake assets:precompile
          environment:
            RAILS_ENV: production
            NODE_ENV: production
            VITE_RUBY_SKIP_ASSETS_PRECOMPILE_EXTENSION: true

  upload_final_assets:
    parameters:
      production-mode:
        description: true to upload to production s3 assets bucket, false to dev bucket
        type: boolean
        default: false
    steps:
      - run:
          name: Upload final assets to CDN
          # version file is used to check whether final assets for latest version are pre-uploaded
          command: |
            if <<parameters.production-mode>>;
            then
              S3_ASSETS_BUCKET=$S3_PROD_ASSETS_BUCKET
              export AWS_ACCESS_KEY_ID=$PROD_AWS_ACCESS_KEY_ID
              export AWS_SECRET_ACCESS_KEY=$PROD_AWS_SECRET_ACCESS_KEY
              echo 'Using production s3 assets bucket'
            else
              S3_ASSETS_BUCKET=s3://holistics-dev-assets
              echo 'Using staging s3 assets bucket'
            fi
            CACHE_OPTIONS='--cache-control public,max-age=604800'
            VERSION="$(git rev-parse --short=10 HEAD)"
            touch ${VERSION}
            aws s3 cp public/assets ${S3_ASSETS_BUCKET}/assets --recursive ${CACHE_OPTIONS} --exclude ".sprockets-manifest-*.json"
            aws s3 cp public/packs/assets ${S3_ASSETS_BUCKET}/packs/assets --recursive ${CACHE_OPTIONS}
            aws s3 cp public/images ${S3_ASSETS_BUCKET}/images --recursive ${CACHE_OPTIONS}
            aws s3 cp ${VERSION} ${S3_ASSETS_BUCKET}/version/${VERSION}

  deploy_vite_assets:
    parameters:
      production-mode:
        description: true to upload to production s3 assets bucket, false to dev bucket
        type: boolean
        default: false
    steps:
      - setup_rails_app_env
      - run:
          name: Vite compile
          command: |
            if <<parameters.production-mode>>;
            then
              VITE_RUBY_ASSET_HOST=assets.holistics.io
              VITE_GLEAP_SDK_KEY=${PROD_VITE_GLEAP_SDK_KEY}
              VITE_GLEAP_SDK_KEY_FOR_TRIAL=${PROD_VITE_GLEAP_SDK_KEY_TRIAL}
              echo "Compile vite using production assets host"
            else
              VITE_RUBY_ASSET_HOST=staging-assets.holistics.io
              VITE_GLEAP_SDK_KEY=${STAGING_VITE_GLEAP_SDK_KEY}
              VITE_GLEAP_SDK_KEY_FOR_TRIAL=""
              echo "Compile vite using staging assets host"
            fi

            VITE_GLEAP_SDK_KEY_FOR_TRIAL=${VITE_GLEAP_SDK_KEY_FOR_TRIAL} \
            VITE_GLEAP_SDK_KEY=${VITE_GLEAP_SDK_KEY} \
            VITE_RUBY_ASSET_HOST=${VITE_RUBY_ASSET_HOST} \
            bundle exec rake vite:build
          no_output_timeout: 20m # https://support.circleci.com/hc/en-us/articles/360007188574-Build-has-hit-timeout-limit
          environment:
            NO_INITIALIZATION: true
            RAILS_ENV: production
            NODE_ENV: production
            NODE_OPTIONS: --max-old-space-size=12000
      - vite_compile_pptr_save:
          # the cache is guaranteed to work for non-FE changes only => deployment should always start plain new build
          use-pptr-cache: false
      - upload_vite_compiled_cache:
          production-mode: <<parameters.production-mode>>
      - precompile_assets:
          production-mode: <<parameters.production-mode>>
      - upload_final_assets:
          production-mode: <<parameters.production-mode>>

  setup_js_test:
    steps:
      - checkout
      - load_secrets
      - check_skippable_pr
      - set_variables
      - setup_npmrc_profile
      - yarn_install

  setup_rails_test:
    parameters:
      setup_env:
        type: boolean
        default: true
      setup_database:
        type: boolean
        default: true
      yarn_install:
        type: boolean
        default: true
      run_aml_server:
        type: boolean
        default: false
      run_node_utils_server:
        type: boolean
        default: false
      bundle_install_frozen:
        type: boolean
        default: true
    steps:
      - checkout
      - check_skippable_pr
      - load_secrets
      - set_variables
      - setup_npmrc_profile
      - bundle_install:
          frozen: << parameters.bundle_install_frozen >>
      - run_source_control_server
      - when:
          condition: << parameters.yarn_install >>
          steps:
            - yarn_install
      - vite_compile_restore
      - when:
          condition: << parameters.setup_env >>
          steps:
            - setup_env
      - when:
          condition: << parameters.setup_database >>
          steps:
            - setup_database
      - when:
          condition: << parameters.run_aml_server >>
          steps:
            - run_aml_server
      - when:
          condition: << parameters.run_node_utils_server >>
          steps:
            - run_node_utils_server

  setup_integration_test:
    parameters:
      install_chrome:
        type: boolean
        default: true
      bundle_install_frozen:
        type: boolean
        default: true
    steps:
      - checkout
      - load_secrets
      - check_skippable_pr
      - set_variables
      - bundle_install:
          frozen: << parameters.bundle_install_frozen >>
      - setup_npmrc_profile
      - yarn_install
      - vite_compile_restore
      - setup_env
      - when:
          condition: << parameters.install_chrome >>
          steps:
            - install_chrome
      - setup_database
      - run_aml_server
      - run_node_utils_server

  run_dangerci:
    steps:
      - run:
          name: Run DangerCI
          command: yarn danger ci

  # run_dangerci_jest_coverage is disabled for further fix
  # https://github.com/holistics/holistics/pull/8615
  # run_dangerci_jest_coverage:
  #   steps:
  #     - attach_workspace:
  #         at: .
  #     - run:
  #         name: Run DangerCI for Jest's coverage report
  #         command: yarn danger ci -d dangerfiles/jest_coverage.js

  run_dangerci_ruby:
    parameters:
      dangerfile:
        type: string
      danger_id:
        type: string
    steps:
      - run:
          name: Run Ruby DangerCI
          command: bundle exec danger --dangerfile=<< parameters.dangerfile >> --danger_id=<< parameters.danger_id >>

  run_brakeman:
    steps:
      - when:
          condition:
            or:
              - equal: [ master, << pipeline.git.branch >> ]
              - equal: [ release, << pipeline.git.branch >> ]
          steps:
          - run:
              name: Brakeman - Generate full report
              command: bundle exec brakeman --no-exit-on-warn -o /tmp/brakeman.html
      - run:
          name: Brakeman - Raise error on high-confidence warnings
          command:  |
            .circleci/run_linter_tool.sh brakeman
      - store_artifacts:
          path: /tmp/brakeman.html

  run_sorbet:
    steps:
      - run:
          name: Check sorbet
          command: |
            .circleci/run_linter_tool.sh sorbet

  run_rubocop:
    steps:
      - run:
          name: Check rubocop
          # TODO: separate warning and error offenses when we enforce rubocop
          command: |
            .circleci/run_linter_tool.sh rubocop

  run_eslint:
    steps:
      - run:
          name: Check eslint
          command: |
            .circleci/run_linter_tool.sh eslint

  # We only run this step on master or release branch
  run_size_limit_base:
    parameters:
      working_dir:
        description: define the directory stores the built files
        type: string
        default: "./public/packs"
    steps:
      - run:
          name: Create .size-limit.json for base branch
          command: ruby ./script/create_size_limit_config_base.rb <<parameters.working_dir>>
      - run:
          name: Calculate size-limit on base branch
          command: ./node_modules/.bin/size-limit --json > "size_limit_result_${CIRCLE_BRANCH}.json"
      - save_cache:
          key: size-limit-cache-{{ .Branch }}-{{ .Revision }}
          # Cannot mixing the branch name in the paths.
          paths:
            - size_limit_result_master.json
            - size_limit_result_release.json

  # restore both the keys. Let dangerfiles/size_limit.rb decides which file should be used as base results:
  # The script will select between size_limit_result_master.json and size_limit_result_release.json
  restore_size_limit_result:
    steps:
      - restore_cache:
          keys:
            - size-limit-cache-master
      - restore_cache:
          keys:
            - size-limit-cache-release

  run_danger_size_limit:
    steps:
      - attach_workspace:
          at: .
      - run_dangerci_ruby:
          dangerfile: "dangerfiles/size_limit.rb"
          danger_id: 'size_limit'

  # Get git commit message
  set_variables:
    steps:
      - run:
          name: Get commit message
          command: |
            echo 'export COMMIT_MESSAGE=$(git log -1 --pretty=format:"\`%h\` - %s")' >> $BASH_ENV
            source $BASH_ENV

  # Load secrets from 1password
  load_secrets:
    steps:
      - restore_cache:
          key: ci_env-{{ .Revision }}
      - run:
          name: export all 1password secrets to $BASH_ENV
          command: |
            # check if .circleci/ci_env.tpl exist and .circleci/ci_env.sh does not exist
            if [ -f .circleci/ci_env.tpl ] && [ ! -f .circleci/ci_env.sh ]; then
              echo "Loading secrets from 1password"
              op inject -i .circleci/ci_env.tpl -o .circleci/ci_env.sh
            fi

            # Check if $BASH_ENV contains the secrets
            if grep -q "#1pw-app-env injected" $BASH_ENV; then
              echo "1pw-app-env already loaded to BASH_ENV"
            else
              echo "#1pw-app-env injected" >> $BASH_ENV
              cat .circleci/ci_env.sh >> $BASH_ENV
            fi

            # load non-app secrets to a file .circleci/ci_non_app_env.sh if not exists
            if [ ! -f .circleci/ci_non_app_env.sh ]; then
              declare -A non_app_keys

              non_app_keys["TOWER_HOST"]="op://circleci-holistics-team/holistics-tower/TOWER_HOST"
              non_app_keys["TOWER_OAUTH_TOKEN"]="op://circleci-holistics-team/holistics-tower/TOWER_OAUTH_TOKEN"
              non_app_keys["PROD_TOWER_OAUTH_TOKEN"]="op://circleci-holistics-team/holistics-tower/PROD_TOWER_OAUTH_TOKEN"
              non_app_keys["SLACK_ACCESS_TOKEN"]="op://circleci-holistics-team/holistics-slack/SLACK_ACCESS_TOKEN"
              non_app_keys["SLACK_BOT_TOKEN"]="op://circleci-holistics-team/holistics-slack/SLACK_BOT_TOKEN"
              non_app_keys["SLACK_BOT_CHANNEL_ID"]="op://circleci-holistics-team/holistics-slack/SLACK_BOT_CHANNEL_ID"

              for key in "${!non_app_keys[@]}"; do
                echo "export $key=${non_app_keys[$key]}" >> .circleci/ci_non_app_env.tpl
              done

              echo "Loading non-app secrets from 1password"
              op inject -i .circleci/ci_non_app_env.tpl -o .circleci/ci_non_app_env.sh
            fi

            # Check if $BASH_ENV contains the secrets
            if grep -q "#1pw-non-app-env injected" $BASH_ENV; then
              echo "1pw-non-app-env already loaded to BASH_ENV"
            else
              echo "#1pw-non-app-env injected" >> $BASH_ENV
              cat .circleci/ci_non_app_env.sh >> $BASH_ENV
            fi

      - save_cache:
          key: ci_env-{{ .Revision }}
          paths:
            - .circleci/ci_env.sh
            - .circleci/ci_non_app_env.sh

  # Send job status to Slack
  # TODO known issue: https://github.com/CircleCI-Public/slack-orb/issues/73
  # The notification is not consolidated across the containers, so there could be multiple notifications from multiple containers
  slack_notify:
    steps:
      - slack/notify:
          channel: dev-github-bots
          event: fail
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":strawberry: Pull request <${CIRCLE_PULL_REQUEST}|${CIRCLE_BRANCH}> by *${CIRCLE_USERNAME}* failed in <https://github.com/${CIRCLE_PROJECT_USERNAME}/${CIRCLE_PROJECT_REPONAME}/commit/${CIRCLE_SHA1}|the latest commit:>\n${COMMIT_MESSAGE}.\n<${CIRCLE_BUILD_URL}|${CIRCLE_JOB}> job failed."
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://cdn.holistics.io/slack-icons/no.png",
                    "alt_text": "red cross for failing PR"
                  }
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "action_id": "basic_fail_view",
                      "text": {
                        "type": "plain_text",
                        "text": "View Job"
                      },
                      "url": "${CIRCLE_BUILD_URL}"
                    }
                  ]
                }
              ]
            }

  store_jest_artifacts:
    steps:
      - store_artifacts:
          path: /tmp/test-results/jest
      # - store_artifacts:
      #     path: coverage/lcov-report
      - store_test_results:
          path: /tmp/test-results/jest
      # - persist_to_workspace:
      #     root: .
      #     paths:
      #       - coverage

  store_rspec_artifacts:
    steps:
      - store_artifacts:
          path: /tmp/coverage
      - store_artifacts:
          path: /tmp/test-results/rspec/rspec.xml
      - store_test_results:
          path: /tmp/test-results/rspec

  run_jest_test:
    steps:
      - restore_cache:
          key: holistics-jest-test-{{ checksum "combined-packages.txt" }}-{{ checksum "yarn.lock" }}-{{ checksum ".node-version" }}
      - run:
          name: Run jest test
          command: |
            mkdir -p /tmp/test-results/jest
            JEST_TEST_FILES=$(circleci tests glob "**/*.test.{ts,js}" | circleci tests split --split-by=timings); \
            JEST_JUNIT_OUTPUT_DIR="/tmp/test-results/jest" \
            JEST_JUNIT_OUTPUT_NAME="jest.xml" \
            JEST_JUNIT_ADD_FILE_ATTRIBUTE="true" \
            yarn test:unit $JEST_TEST_FILES -w 1 --ci --reporters=default --reporters=jest-junit
      - save_cache:
          key: holistics-jest-test-{{ checksum "combined-packages.txt" }}-{{ checksum "yarn.lock" }}-{{ checksum ".node-version" }}
          paths:
            - /tmp/jest

  run_gems_test:
    steps:
      - run:
          name: Run gems tests
          command: script/gems_rspec.sh

  run_rails_test:
    steps:
      - run:
          name: Run rails tests
          command: |
            circleci tests glob "spec/{abilities,models,lib,controllers,mailers,services,serializers,queries,operations,parameters,workers,requests}/**/*_spec.rb" \
            "engines/**/spec/{abilities,models,lib,controllers,mailers,services,serializers,queries,operations,parameters,workers,requests}/**/*_spec.rb" | \
            circleci tests run --command="xargs bundle exec rspec --color --format documentation --format progress --format RspecJunitFormatter -o /tmp/test-results/rspec/rspec.xml --tag ~skip:true --tag ~require_puppeteer:true --tag ~require_webpack:true --tag ~require_snowflake:true" --verbose --split-by=timings
  run_pptr_test:
    steps:
      - run:
          name: Run puppeteer tests
          command: |
            TEST_FILES=$(circleci tests glob "spec/{abilities,models,lib,controllers,mailers,services,serializers,queries,operations,parameters,workers,integration}/**/*_spec.rb" | \
            circleci tests split --split-by=timings); \
            bundle exec rspec --color \
                              --format documentation \
                              --format RspecJunitFormatter -o /tmp/test-results/rspec/rspec.xml \
                              --tag ~skip:true \
                              --tag require_puppeteer:true \
                              --tag require_webpack:true \
                              -- ${TEST_FILES}

  run_integration_test_standard:
    steps:
      - run:
          name: Run integration tests
          no_output_timeout: 15m # https://support.circleci.com/hc/en-us/articles/360007188574-Build-has-hit-timeout-limit
          command: |
            circleci tests glob "spec/integration/**/*_spec.rb" | \
            circleci tests run --command="xargs bundle exec rspec --color --format documentation --format progress --format RspecJunitFormatter -o /tmp/test-results/rspec/rspec.xml --tag ~skip:true --tag ~stable:true --tag ~legacy:true --tag ~require_puppeteer:true" --verbose --split-by=timings
      - store_artifacts:
          path: /tmp/capybara/

  run_integration_test_legacy_stable:
    steps:
      - run:
          name: Run integration tests
          no_output_timeout: 15m # https://support.circleci.com/hc/en-us/articles/360007188574-Build-has-hit-timeout-limit
          command: |
            circleci tests glob "spec/integration/**/*_spec.rb" | \
            circleci tests run --command="xargs bundle exec rspec --color --format documentation --format progress --format RspecJunitFormatter -o /tmp/test-results/rspec/rspec.xml --tag ~skip:true --tag stable:true --tag legacy:true --tag ~require_puppeteer:true" --verbose --split-by=timings
      - store_artifacts:
          path: /tmp/capybara/

  run_snowflake_test:
    steps:
      - run:
          name: Run snowflake tests
          command: |
            TEST_FILES=$(circleci tests glob "spec/{abilities,models,lib,controllers,mailers,services,serializers,queries,operations,parameters,workers}/**/*_spec.rb" | \
            circleci tests split --split-by=timings); \
            ENABLE_SNOWFLAKE_TESTS=1 bundle exec rspec --color \
                              --format documentation \
                              --format progress \
                              --format RspecJunitFormatter -o /tmp/test-results/rspec/rspec_snowflake.xml \
                              --tag require_snowflake:true \
                              -- ${TEST_FILES}

  persist_coverage:
    parameters:
      coverage_path:
        type: string
      persisted_name:
        type: string
    steps:
      - run:
          name: Stash coverage results
          command: |
            COVERAGE_DIR="test_coverage/${CIRCLE_NODE_INDEX}"
            mkdir -p ${COVERAGE_DIR}
            mkdir -p $(dirname << parameters.coverage_path >>)
            touch << parameters.coverage_path >>
            cp -R "<< parameters.coverage_path >>" "${COVERAGE_DIR}/<< parameters.persisted_name >>"
      - persist_to_workspace:
          root: .
          paths:
            - test_coverage

  persist_ruby_coverage:
    parameters:
      coverage_name:
        type: string
    steps:
      - persist_coverage:
          coverage_path: "/tmp/coverage/.resultset.json"
          persisted_name: "<< parameters.coverage_name >>.json"

  check_coverage:
    steps:
      - attach_workspace:
          at: .
      - run_dangerci_ruby:
          dangerfile: "dangerfiles/coverage.rb"
          danger_id: 'coverage'
      - store_artifacts:
          path: coverage
          destination: coverage

  check_danger_sorbet:
    steps:
      - attach_workspace:
          at: .
      - run_dangerci_ruby:
          dangerfile: "dangerfiles/sorbet.rb"
          danger_id: 'sorbet'

  check_skippable_pr:
    steps:
      - jq/install
      - run:
          name: Check skippable PR
          command: .circleci/skip_ci.sh || true

  notify_vite_build_prod:
    steps:
      - slack/notify:
          channel: devops-deployment-bots
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":package: *${CIRCLE_USERNAME}*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Start to build vite assets for *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>*. (<https://github.com/holistics/holistics/commit/${CIRCLE_SHA1}|${CIRCLE_SHA1:0:10}>)"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":doge-finger-guns: deployment will be started when this job succeeds"
                  }
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "action_id": "basic_success_view",
                      "text": {
                        "type": "plain_text",
                        "text": "View Job"
                      },
                      "url": "${CIRCLE_BUILD_URL}"
                    }
                  ]
                }
              ]
            }

  notify_vite_success_prod:
    steps:
      - slack/notify:
          channel: devops-deployment-bots
          event: pass
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":rocket: *${CIRCLE_USERNAME}*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Build vite assets for *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>* successfully. (<https://github.com/holistics/holistics/commit/${CIRCLE_SHA1}|${CIRCLE_SHA1:0:10}>)"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":deploy_parrot: deployment started!"
                  }
                }
              ]
            }

  notify_deployment_start_prod:
    steps:
      - slack/notify:
          channel: devops-all
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":rocket: *${CIRCLE_USERNAME}* is deploying Holistics *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>* on production"
                  }
                }
              ]
            }

  notify_deployment_start_hotfix:
    steps:
      - slack/notify:
          channel: devops-all
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":fire: *${CIRCLE_USERNAME}* is HOTFIXing Holistics *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>* on Production"
                  }
                }
              ]
            }

  notify_deployment_start_fullhotfix:
    steps:
      - slack/notify:
          channel: devops-all
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":fire: *${CIRCLE_USERNAME}* is Full-HOTFIXing Holistics *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>* on Production"
                  }
                }
              ]
            }

  notify_staging_start_full_hotfix:
    steps:
      - slack/notify:
          channel: devops-deployment-bots
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":fire: *${CIRCLE_USERNAME}* is full_hotfixing *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>* on ${DEPLOYING_SERVER}"
                  }
                }
              ]
            }


  notify_staging_start_hotfix:
    steps:
      - slack/notify:
          channel: devops-deployment-bots
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":cooking: *${CIRCLE_USERNAME}* is hotfixing *<https://github.com/holistics/holistics/releases/tag/${CIRCLE_TAG}|${CIRCLE_TAG:-INVALID_TAG}>* on ${DEPLOYING_SERVER}"
                  }
                }
              ]
            }

  notify_deployment_staging_assets_build:
    steps:
      - slack/notify:
          channel: devops-deployment-staging
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*${DEPLOYING_SERVER} _(1/2)_* :package: *${CIRCLE_USERNAME}* is deploying *<https://github.com/holistics/holistics/commit/${CIRCLE_SHA1}|${CIRCLE_TAG:-INVALID_TAG}>*. Assets are being built on CircleCI"
                  }
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "action_id": "basic_success_view",
                      "text": {
                        "type": "plain_text",
                        "text": "View Job"
                      },
                      "url": "${CIRCLE_BUILD_URL}"
                    }
                  ]
                }
              ]
            }

  notify_deployment_staging_start:
    steps:
      - slack/notify:
          channel: devops-deployment-staging
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*${DEPLOYING_SERVER} _(2/2)_* :rocket: *${CIRCLE_USERNAME}* The deployment for *<https://github.com/holistics/holistics/commit/${CIRCLE_SHA1}|${CIRCLE_TAG:-INVALID_TAG}>* will start soon, check Slack channel <#C03Q9016ZRS> for the progress :deploy_parrot:."
                  }
                }
              ]
            }

  notify_production_deployment_approval:
    parameters:
      action:
        type: string
        default: notify
      circle_api_key:
        type: string
        description: CircleCI API key.
        default: ""
      slack_token:
        type: string
        description: Slack token used for authentication.
        default: ""
      slack_channel:
        type: string
        description: Slack channel to post the message in.
        default: ""
    steps:
      - run:
          name: Notify production deployment approval
          shell: /bin/bash
          command: |
            ruby .circleci/production_deployment_approval.rb --action << parameters.action >> --circle-token << parameters.circle_api_key >> --slack-token << parameters.slack_token >> --slack-channel << parameters.slack_channel >>

  check_production_deployment_approval:
    parameters:
      action:
        type: string
        default: check
      circle_api_key:
        type: string
        description: CircleCI API key.
        default: ""
      slack_token:
        type: string
        description: Slack token used for authentication.
        default: ""
      slack_channel:
        type: string
        description: Slack channel to post the message in.
        default: ""
    steps:
      - run:
          name: Check if approval is valid
          shell: /bin/bash
          command: |
            ruby .circleci/production_deployment_approval.rb --action << parameters.action >> --circle-token << parameters.circle_api_key >> --slack-token << parameters.slack_token >> --slack-channel << parameters.slack_channel >>

  set_staging_server_action:
    steps:
    - run:
        name: Add var DEPLOYING_SERVER & DEPLOYING_ACTION
        command: |
          IFS=/ read -r -a tag_name_parts \<<< ${CIRCLE_TAG}
          server=${tag_name_parts[0]}
          action=${tag_name_parts[1]}
          echo "export DEPLOYING_SERVER=${server} DEPLOYING_ACTION=${action}" >> $BASH_ENV
          source $BASH_ENV

  deploy_staging_by_action:
    steps:
    - run:
          name: Deploy to staging server
          # tag name is conventional: e.g. staging5/<branch_name>
          # staging0 is also staging but it's created by the script bin/conv_deploy.sh
          command: |
            set -euo pipefail
            declare -A DEPLOYING_KEY="${DEPLOYING_SERVER}_${DEPLOYING_ACTION}"
            declare -A TEMPLATE_IDS=( \
            ["staging0_deploy"]="23" ["staging_deploy"]="23" ["staging_full_hotfix_be"]="365" ["staging_hotfix_be"]="41" \
            ["staging2_deploy"]="44" ["staging2_full_hotfix_be"]="370" ["staging2_hotfix_be"]="56" \
            ["staging3_deploy"]="48" ["staging3_full_hotfix_be"]="226" ["staging3_hotfix_be"]="57" \
            ["staging4_deploy"]="60" ["staging4_full_hotfix_be"]="371" ["staging4_hotfix_be"]="93" \
            ["staging5_deploy"]="135" ["staging5_full_hotfix_be"]="372" ["staging5_hotfix_be"]="136" \
            ["staging6_deploy"]="208" ["staging6_full_hotfix_be"]="226" ["staging6_hotfix_be"]="209" \
            ["staging7_deploy"]="252" ["staging7_full_hotfix_be"]="373" ["staging7_hotfix_be"]="254" \
            ["staging8_deploy"]="253" ["staging8_full_hotfix_be"]="374" ["staging8_hotfix_be"]="255" \
            ["staging9_deploy"]="276" ["staging9_full_hotfix_be"]="351" ["staging9_hotfix_be"]="367" \
            )

            if [[ -v "TEMPLATE_IDS[${DEPLOYING_KEY}]" ]];
            then
              id=${TEMPLATE_IDS[${DEPLOYING_KEY}]}

              if [ "$DEPLOYING_SERVER" == "staging0" ]; then
                IFS='_' read -ra git_tag_parts \<<< $CIRCLE_TAG
                deploy_details=$(echo "$CIRCLE_TAG" | sed -E 's|^staging0/deploy/(.*)$|\1|')

                # Extract branch_name, staging_version and github_pr_number from deploy_details
                branch_name=$(echo "$deploy_details" | sed -E 's|.*(staging_v[0-9]+\.[0-9]+)_.*|\1|')
                staging_version=$(echo "$deploy_details" | sed -E 's|.*staging_v([0-9]+\.[0-9]+)_.*|\1|')
                github_pr_number=$(echo "$deploy_details" | sed -E 's|.*staging_v[0-9]+\.[0-9]+_.*_([0-9]+)_.*|\1|')

                echo "deploy_details: $deploy_details"
                echo "staging_version: $staging_version"
                echo "github_pr_number: $github_pr_number"
                echo "branch_name: $branch_name"

                extra_vars="{\
                  \"git_repo_branch\": \"${CIRCLE_TAG}\",
                  \"slack_custom_message\": \"CircleCI: ${CIRCLE_USERNAME} ${DEPLOYING_ACTION} ${CIRCLE_SHA1}\",
                  \"github_username\": \"${CIRCLE_USERNAME}\",
                  \"staging_holistics_version\": \"${staging_version}\",
                  \"github_pr_number\": \"${github_pr_number}\",
                  \"branch_name\": \"${branch_name}\"
                }"
              else
                extra_vars="{\
                  \"git_repo_branch\": \"${CIRCLE_TAG}\",
                  \"slack_custom_message\": \"CircleCI: ${CIRCLE_USERNAME} ${DEPLOYING_ACTION} ${CIRCLE_SHA1}\"
                }"
              fi
              echo "Launch ${DEPLOYING_ACTION} template for ${DEPLOYING_SERVER} with job template: ${id}"
              awx job_templates launch $id --extra_vars "${extra_vars}"
            fi

# Workflows
jobs:
  build_dev:
    executor: minimal
    resource_class: large
    steps:
      - checkout
      - load_secrets
      - check_skippable_pr
      - set_variables
      - setup_npmrc_profile
      - bundle_install
      - bundle_cache
      - yarn_install
      - yarn_cache
      - vite_compile_save

  notify_production_deployment_approval_job:
    executor: minimal
    steps:
      - checkout
      - load_secrets
      - notify_production_deployment_approval:
          circle_api_key: $CIRCLECI_API_TOKEN
          slack_token: $SLACK_BOT_TOKEN
          slack_channel: $SLACK_BOT_CHANNEL_ID

  check_production_deployment_approval_job:
    executor: minimal
    resource_class: small
    steps:
      - checkout
      - load_secrets
      - check_production_deployment_approval:
          circle_api_key: $CIRCLECI_API_TOKEN
          slack_token: $SLACK_BOT_TOKEN
          slack_channel: $SLACK_BOT_CHANNEL_ID

  build_production:
    executor: assets_compiler
    resource_class: xlarge
    steps:
      - notify_vite_build_prod
      - checkout
      - load_secrets
      - setup_npmrc_profile
      - bundle_install
      - yarn_install
      - deploy_vite_assets:
          production-mode: true
      - notify_vite_success_prod
      # Need to run this to calculate the size of the asset's files of a deployment
      # On release branch, the assets are saved at './public/packs/assets'. So, we use the default working dir: './public/packs'
      - run_size_limit_base

  deploy_production:
    executor: assets_compiler
    resource_class: small
    steps:
      - load_secrets
      - run:
          name: Launch production deployment
          command: |
            set -euo pipefail
            export TOWER_OAUTH_TOKEN=$PROD_TOWER_OAUTH_TOKEN
            extra_vars="{\
              \"git_repo_branch\": \"${CIRCLE_TAG}\",
              \"slack_custom_message\": \"CircleCI: ${CIRCLE_USERNAME} deploys ${CIRCLE_TAG} to production\",
              \"awx_end_flow\": \"true\"
            }"
            awx workflow_job_templates launch 157 --extra_vars "${extra_vars}"
      - notify_deployment_start_prod
  deploy_hotfix:
    executor: minimal
    resource_class: small
    steps:
      - load_secrets
      - run:
          name: Launch production hotfix (backend only)
          command: |
            set -euo pipefail
            export TOWER_OAUTH_TOKEN=$PROD_TOWER_OAUTH_TOKEN
            extra_vars="{\
              \"git_repo_branch\": \"${CIRCLE_TAG}\",
              \"slack_custom_message\": \"CircleCI: ${CIRCLE_USERNAME} deploys hotfix ${CIRCLE_TAG} to production\",
              \"awx_end_flow\": \"true\"
            }"
            awx workflow_job_templates launch 166 --extra_vars "${extra_vars}"
      - notify_deployment_start_hotfix

  deploy_full_hotfix:
    executor: minimal
    resource_class: small
    steps:
      - load_secrets
      - run:
          name: Launch production full hotfix (backend only and run bundle install again)
          command: |
            set -euo pipefail
            export TOWER_OAUTH_TOKEN=$PROD_TOWER_OAUTH_TOKEN
            extra_vars="{\
              \"git_repo_branch\": \"${CIRCLE_TAG}\",
              \"slack_custom_message\": \"CircleCI: ${CIRCLE_USERNAME} deploys full hotfix (with bundle install) ${CIRCLE_TAG} to production\",
              \"awx_end_flow\": \"true\",
            }"
            awx workflow_job_templates launch 194 --extra_vars "${extra_vars}"
      - notify_deployment_start_fullhotfix

  build_and_deploy_staging:
    executor: assets_compiler
    resource_class: xlarge
    steps:
      - set_staging_server_action
      - notify_deployment_staging_assets_build

      - checkout
      - load_secrets
      - setup_npmrc_profile
      - bundle_install
      - yarn_install
      - deploy_vite_assets:
          production-mode: false
      - deploy_staging_by_action
      - notify_deployment_staging_start

  full_hotfix_staging:
    executor: minimal
    resource_class: small
    steps:
      - load_secrets
      - set_staging_server_action
      - deploy_staging_by_action
      - notify_staging_start_full_hotfix

  hotfix_staging:
    executor: minimal
    resource_class: small
    steps:
      - load_secrets
      - set_staging_server_action
      - deploy_staging_by_action
      - notify_staging_start_hotfix

  zeitwerk_check:
    executor: zeitwerk
    steps:
      - checkout
      - load_secrets
      - check_skippable_pr
      - bundle_install
      - bundle_cache
      - setup_rails_app_env
      - setup_database
      - run:
          name: Check zeitwerk constants autoloading
          command: RAILS_ENV=production bundle exec rails zeitwerk:check

  brakeman:
    executor: default
    steps:
      - setup_rails_test:
          setup_database: false
          yarn_install: false
      - run_brakeman
      - slack_notify

  dangerci:
    executor: minimal
    steps:
      - setup_js_test
      - run_dangerci
      - slack_notify

  sorbet:
    executor: minimal
    steps:
      - setup_rails_test:
          setup_env: false
          setup_database: false
          yarn_install: false
      - run_sorbet
      - slack_notify

  rubocop:
    executor: minimal
    steps:
      - setup_rails_test:
          setup_database: false
          yarn_install: false
      - run_rubocop
      - slack_notify

  eslint:
    executor: minimal
    steps:
      - setup_js_test
      - run_eslint
      - slack_notify

  test_jest:
    executor: minimal
    parallelism: 1
    steps:
      - setup_js_test
      - run_jest_test
      - store_jest_artifacts
      # - run_dangerci_jest_coverage
      - slack_notify

  test_gems:
    executor: default
    steps:
      - checkout
      - load_secrets
      - check_skippable_pr
      - set_variables
      - bundle_install
      - run_source_control_server
      - run_gems_test
      - run:
          name: Copy gems test coverage files
          command: |
            COVERAGE_DIR="test_coverage/${CIRCLE_NODE_INDEX}"
            mkdir -p "$COVERAGE_DIR"
            cp /tmp/gems_test_coverage/*.json "$COVERAGE_DIR"
      - store_test_results:
          path: /tmp/test-results
      - persist_to_workspace:
          root: .
          paths:
            - test_coverage
      - slack_notify

  test_rails_app:
    executor: rails
    parallelism: 14
    resource_class: medium
    steps:
      - setup_rails_test:
          run_aml_server: true
          run_node_utils_server: true
      - run_rails_test
      - store_rspec_artifacts
      - persist_ruby_coverage:
          coverage_name: rails_app
      - persist_coverage:
          coverage_path: tmp/openapi_coverage.json
          persisted_name: openapi.json
      - slack_notify

  build_puppeteer_related:
    executor: minimal
    resource_class: medium+
    steps:
      - checkout
      - load_secrets
      - check_skippable_pr
      - set_variables
      - setup_npmrc_profile
      - bundle_install
      - yarn_install
      - run_source_control_server
      - vite_compile_pptr_save:
          use-pptr-cache: false

  test_puppeteer_related:
    executor: default_with_canal
    parallelism: 4
    resource_class: medium
    steps:
      - setup_integration_test:
          install_chrome: true
      - run_source_control_server
      - run_pptr_test
      - store_rspec_artifacts
      - persist_ruby_coverage:
          coverage_name: pptr
      - slack_notify

  run_integration_tests:
    executor: rails
    resource_class: medium
    parallelism: 32
    steps:
      - setup_integration_test
      - run_source_control_server
      - run_integration_test_standard
      - store_rspec_artifacts
      - slack_notify

  run_integration_tests_legacy_stable:
    executor: rails
    resource_class: medium
    parallelism: 16
    steps:
      - setup_integration_test
      - run_source_control_server
      - run_integration_test_legacy_stable
      - store_rspec_artifacts
      - slack_notify

  run_snowflake_tests:
    executor: default_with_canal
    parallelism: 2
    steps:
      - setup_rails_test:
          run_node_utils_server: true
          run_aml_server: true
      - run_snowflake_test
      - store_rspec_artifacts
      - slack_notify

  coverage:
    executor: minimal
    steps:
      - setup_rails_test:
          setup_env: false
          setup_database: false
      - check_coverage

  danger_sorbet:
    # TODO: merge this into `sorbet` when we enforce this
    executor: minimal
    steps:
      - setup_rails_test:
          setup_env: false
          setup_database: false
      - check_danger_sorbet

  size_limit_master:
    executor: minimal
    steps:
      - setup_rails_test:
          setup_env: false
          setup_database: false
      - vite_compile_restore
      # On master branch, build_dev saves the built files at './public/vite-dev/assets'. So, the working dir is './public/vite-dev'
      - run_size_limit_base:
          working_dir: "./public/vite-dev"

  danger_size_limit:
    executor: minimal
    steps:
      - setup_rails_test:
          setup_env: false
          setup_database: false
      - restore_size_limit_result
      - run_danger_size_limit

  notify_successful_dev_build:
    executor: minimal
    steps:
      - load_secrets
      - slack/notify:
          channel: dev-github-bots
          event: pass
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Job*: ${CIRCLE_JOB}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Project*: $CIRCLE_PROJECT_REPONAME"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*: $CIRCLE_BRANCH"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit*: ${CIRCLE_SHA1:0:5}"
                    }
                  ],
                  "text": {
                    "type": "mrkdwn",
                    "text": ":cactus: Pull request *<${CIRCLE_PULL_REQUEST}|${CIRCLE_BRANCH}>* by *${CIRCLE_USERNAME}* passed all the required tests!."
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://cdn.holistics.io/slack-icons/yes.png",
                    "alt_text": "check mark passing"
                  }
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "action_id": "basic_success_view",
                      "text": {
                        "type": "plain_text",
                        "text": "View Job"
                      },
                      "url": "${CIRCLE_BUILD_URL}"
                    }
                  ]
                }
              ]
            }


workflows:
  build_and_test:
    jobs:
      - build_dev:
          <<: *shared_context
      - zeitwerk_check:
          <<: *shared_context
          requires: [ build_dev ]
      - dangerci:
          <<: *shared_context
          requires: [ build_dev ]
      - brakeman:
          <<: *shared_context
          requires: [ build_dev ]
      - sorbet:
          <<: *shared_context
          requires: [ build_dev ]
      - danger_sorbet:
          <<: *shared_context
          requires: [ build_dev ]
      - rubocop: # TODO: make ruby tests depend on this when the rubocop rules are solid
          <<: *shared_context
          requires: [ build_dev ]
      - eslint:
          <<: *shared_context
          requires: [ build_dev ]
      - danger_size_limit:
          <<: *shared_context
          requires: [ build_dev ]
          filters:
            branches:
              ignore: [ master, release ]
      - test_jest:
          <<: *shared_context
          requires: [ dangerci, eslint ]
      - test_gems:
          <<: *shared_context
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
      - test_rails_app:
          context:
            - 1password-connect-token
            - slack-app-v4
            - bigquery-test-secrets
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
      - build_puppeteer_related:
          <<: *shared_context
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
      - test_puppeteer_related:
          <<: *shared_context
          requires: [ build_puppeteer_related ]
      - coverage:
          <<: *shared_context
          requires: [ test_gems, test_rails_app, test_puppeteer_related ] # TODO: merge integration tests coverage
      - approve_integration_tests:
          <<: *shared_context
          type: approval
      - run_integration_tests:
          context:
            - 1password-connect-token
            - slack-app-v4
            - bigquery-test-secrets
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet, approve_integration_tests ]
      - approve_snowflake_tests:
          type: approval
      - run_snowflake_tests:
          <<: *shared_context
          name: run_snowflake_tests_manually
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet, approve_snowflake_tests ]
      - approve_integration_tests_legacy_stable:
          type: approval
      - run_integration_tests_legacy_stable:
          context:
            - 1password-connect-token
            - slack-app-v4
            - bigquery-test-secrets
          name: run_integration_tests_legacy_stable_manually
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet, build_puppeteer_related, approve_integration_tests_legacy_stable ]
      - notify_successful_dev_build:
          <<: *shared_context
          requires: [ test_jest, test_gems, test_rails_app, test_puppeteer_related, coverage, run_integration_tests ]
    when:
      matches:
        pattern: "^(?!master|staging|release).*$"
        value: << pipeline.git.branch >>

  # This is build workflow for master/staging/release branches. It is almost the same as build_and_test workflow.
  # The reason for this duplication is that we want the approval-based and automatic integration test runs to
  # have the same name. As CI config currently doesn't support this option, this is a hack for now.
  build_and_test_for_release:
    jobs:
      - build_dev:
          <<: *shared_context
      - zeitwerk_check:
          <<: *shared_context
          requires: [ build_dev ]
      - dangerci:
          <<: *shared_context
          requires: [ build_dev ]
      - brakeman:
          <<: *shared_context
          requires: [ build_dev ]
      - sorbet:
          <<: *shared_context
          requires: [ build_dev ]
      - danger_sorbet:
          <<: *shared_context
          requires: [ build_dev ]
      - rubocop: # TODO: make ruby tests depend on this when the rubocop rules are solid
          <<: *shared_context
          requires: [ build_dev ]
      - eslint:
          <<: *shared_context
          requires: [ build_dev ]
      - size_limit_master:
          <<: *shared_context
          requires: [ build_dev ]
          filters:
            branches:
              only: [ master ]
      - test_jest:
          <<: *shared_context
          requires: [ dangerci, eslint ]
      - test_gems:
          <<: *shared_context
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
      - test_rails_app:
          context:
            - 1password-connect-token
            - slack-app-v4
            - bigquery-test-secrets
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
      - build_puppeteer_related:
          <<: *shared_context
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
      - test_puppeteer_related:
          <<: *shared_context
          requires: [ build_puppeteer_related ]
      - run_integration_tests:
          context:
            - 1password-connect-token
            - slack-app-v4
            - bigquery-test-secrets
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet, build_puppeteer_related ]
      - run_integration_tests_legacy_stable:
          context:
            - 1password-connect-token
            - slack-app-v4
            - bigquery-test-secrets
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet, build_puppeteer_related ]
      - run_snowflake_tests:
          <<: *shared_context
          requires: [ zeitwerk_check, dangerci, brakeman, sorbet ]
          filters:
            branches:
              only: [ release, staging ]
      - coverage:
          <<: *shared_context
          requires: [ test_gems, test_rails_app, test_puppeteer_related ] # TODO: merge integration tests coverage
    when:
      matches:
        pattern: "(^(master|release)$|staging.*)"
        value: << pipeline.git.branch >>

  deploy:
    jobs:
    # BEGIN Hotfix
      - notify_production_deployment_approval_job:
          name: hotfix_notify_production_deployment_approval_job
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_hotfix_be)$/

      - approve_production_deployment:
          name: hotfix_approve_production_deployment
          <<: *shared_context
          type: approval
          requires: [ hotfix_notify_production_deployment_approval_job ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_hotfix_be)$/

      - check_production_deployment_approval_job:
          name: hotfix_check_production_deployment_approval_job
          <<: *shared_context
          requires: [ hotfix_approve_production_deployment ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_hotfix_be)$/

      - deploy_hotfix:
          <<: *shared_context
          requires: [ hotfix_check_production_deployment_approval_job ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_hotfix_be)$/
    # END Hotfix

    # BEGIN Full Hotfix
      - notify_production_deployment_approval_job:
          name: full_hotfix_notify_production_deployment_approval_job
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_full_hotfix_be)$/

      - approve_production_deployment:
          name: full_hotfix_approve_production_deployment
          <<: *shared_context
          type: approval
          requires: [ full_hotfix_notify_production_deployment_approval_job ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_full_hotfix_be)$/

      - check_production_deployment_approval_job:
          name: full_hotfix_check_production_deployment_approval_job
          <<: *shared_context
          requires: [ full_hotfix_approve_production_deployment ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_full_hotfix_be)$/

      - deploy_full_hotfix:
          <<: *shared_context
          requires: [ full_hotfix_approve_production_deployment ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_full_hotfix_be)$/
    # END Full Hotfix

  build_and_deploy:
    jobs:
    # BEGIN Production
      - build_production:
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_deploy)$/

      - notify_production_deployment_approval_job:
          requires: [ build_production ]
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_deploy)$/

      - approve_production_deployment:
          <<: *shared_context
          type: approval
          requires: [ notify_production_deployment_approval_job ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_deploy)$/

      - check_production_deployment_approval_job:
          <<: *shared_context
          requires: [ approve_production_deployment ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_deploy)$/

      - deploy_production:
          <<: *shared_context
          requires: [ check_production_deployment_approval_job ]
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /^v.*(?:_ci_deploy)$/
    # END Production

    # BEGIN Staging
      - build_and_deploy_staging:
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /staging[\d+]?\/deploy\/.*/
    # END Staging

    # BEGIN Staging Full_hotfix
      - full_hotfix_staging:
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /staging[\d+]?\/full\_hotfix\_be\/.*/
    # END Staging Full_hotfix

    # BEGIN Staging Hotfix
      - hotfix_staging:
          <<: *shared_context
          filters:
            branches:
              ignore: /.*/ # check tag version only
            tags:
              only: /staging[\d+]?\/hotfix\_be\/.*/
    # END Staging Hotfix
