PATH
  remote: .
  specs:
    source_control (0.9.0)
      activesupport
      coding_utils (~> 1.0)
      dotenv
      http
      json_rpc_client (~> 0.4)
      logstash-logger
      oj
      puma
      rugged (= 1.7.2)
      sinatra (~> 2.2)
      sorbet-runtime

GEM
  remote: https://rubygems.org/
  specs:
    activesupport (********)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    base64 (0.2.0)
    byebug (11.1.3)
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    diff-lcs (1.5.1)
    docile (1.4.1)
    domain_name (0.6.20240107)
    dotenv (2.8.1)
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86_64-darwin)
    ffi (1.17.0-x86_64-linux-gnu)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    hashdiff (1.1.1)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-cookie (1.0.6)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    listen (3.8.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logstash-event (1.2.02)
    logstash-logger (0.26.1)
      logstash-event (~> 1.2)
    lz4-ruby (0.3.3)
    method_source (1.1.0)
    minitest (5.25.1)
    msgpack (1.7.2)
    msgpack_rails (0.4.3)
      activesupport (>= 3.0)
      msgpack
    mustermann (2.0.2)
      ruby2_keywords (~> 0.0.1)
    nio4r (2.7.3)
    oj (3.13.23)
    polyfill (1.9.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    public_suffix (5.1.1)
    puma (6.4.3)
      nio4r (~> 2.0)
    rack (2.2.16)
    rack-protection (2.2.3)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (4.5.1)
    rerun (0.14.0)
      listen (~> 3.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.1)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    ruby2_keywords (0.0.5)
    rugged (1.7.2)
    safe_type (1.1.1)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    sinatra (2.2.3)
      mustermann (~> 2.0)
      rack (~> 2.2)
      rack-protection (= 2.2.3)
      tilt (~> 2.0)
    sorbet (0.5.11376)
      sorbet-static (= 0.5.11376)
    sorbet-coerce (0.7.0)
      polyfill (~> 1.8)
      safe_type (~> 1.1, >= 1.1.1)
      sorbet-runtime (>= 0.4.4704)
    sorbet-runtime (0.5.11589)
    sorbet-static (0.5.11376-universal-darwin)
    sorbet-static (0.5.11376-x86_64-linux)
    tilt (2.3.0)
    timecop (0.9.6)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    zeitwerk (2.6.18)

GEM
  remote: https://rubygems.pkg.github.com/holistics/
  specs:
    coding_utils (1.0.1)
      activesupport (~> 6.1.0)
      connection_pool (~> 2.2)
      hashdiff (~> 1.1)
      lz4-ruby (~> 0.3.3)
      msgpack_rails (~> 0.4.3)
      oj (~> 3.13)
      redis (~> 4.5.0)
      rspec (~> 3.0)
      sorbet-coerce (~> 0.7.0)
      sorbet-runtime (~> 0.5)
    json_rpc_client (0.4.3)
      activesupport (~> 6.1)
      coding_utils (~> 1.0)
      http (~> 5.2.0)
      sorbet-runtime (~> 0.5)

PLATFORMS
  arm64-darwin-22
  arm64-darwin-23
  arm64-darwin-24
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-darwin-23
  x86_64-darwin-24
  x86_64-linux

DEPENDENCIES
  coding_utils!
  pry-byebug
  rack-test
  rake (~> 13)
  rerun
  rspec (~> 3.0)
  rspec_junit_formatter (~> 0.4)
  simplecov (~> 0.22)
  sorbet
  source_control!
  timecop (~> 0.9.1)

BUNDLED WITH
   2.4.22
